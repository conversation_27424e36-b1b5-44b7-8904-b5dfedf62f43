import AnimatedData<PERSON><PERSON> from "@/components/sections/data-center/AnimatedDataCenter";
import PageBanner from "@/components/elements/PageBanner";

export const metadata = {
  title: "Interactive Data Center | Motshwanelo IT Consulting",
  description: "Explore our Data Center solutions in an interactive visualization.",
};

export default function DataCenterInteractivePage() {
  return (
    <>
      <PageBanner
        pageTitle="Interactive Data Center"
        breadTitle="Data Center Interactive View"
        description="Explore our Data Center facility in an interactive visualization. Click on server racks to learn more."
        type="1"
      />
      
      <AnimatedDataCenter />
      
      <section className="sp">
        <div className="container">
          <div className="row">
            <div className="col-lg-8 m-auto text-center">
              <div className="heading1">
                <h3>Why Choose Our Data Center Solutions?</h3>
                <div className="space16" />
                <p>
                  Our data center facility features the latest in server technology, including the Fusion Module 2000,
                  NetCol5000 cooling systems, and UPS5000 power management. These components work together to provide
                  a secure, efficient, and reliable environment for your critical data and applications.
                </p>
                <div className="space30" />
                <a href="/services/data-centre" className="thm-btn">
                  <span>Learn More About Our Data Center Services</span>
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
}