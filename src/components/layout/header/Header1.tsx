"use client";

import Link from "next/link";
import { useSession, signOut } from "next-auth/react";
import MobileMenu from "../MobileMenu";
import MainMenu from "../MainMenu";
import MainMenuOnePage1 from "../MainMenuOnePage1";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { User, LogOut, Settings, Shield, LogIn } from "lucide-react";
import { useHeader } from "@/hooks/useHeader";

export default function Header({
  scroll,
  isMobileMenu,
  handleMobileMenu,
  mainMenuStyle,
}: any) {
  const { data: session, status } = useSession();

  const handleSignOut = () => {
    signOut({ callbackUrl: "/auth/signin" });
  };
  return (
    <>
      <header>
        <div
          className={`header-area header-area1 header-area-all d-none d-lg-block ${
            scroll ? "sticky" : ""
          } transition-all duration-300`}
          id="header"
        >
          <div className="container">
            <div className="row">
              <div className="col-12">
                <div className="header-elements">
                  <div className="site-logo">
                    <Link href="/" className="flex items-center">
                      <img
                        src="assets/img/logo/logo_full.svg"
                        alt="Motshwanelo IT Consulting"
                        className="h-12 w-auto transition-all duration-300"
                      />
                    </Link>
                  </div>
                  <div className="main-menu-ex main-menu-ex1">
                    {!mainMenuStyle && <MainMenu />}
                    {mainMenuStyle == "one-page" ? <MainMenuOnePage1 /> : null}
                  </div>
                  <div className="header2-buttons">
                    <div className="flex items-center gap-3">
                      {status === "loading" ? (
                        <div className="flex items-center gap-2">
                          <div className="w-4 h-4 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin"></div>
                          <span className="text-sm text-gray-600">
                            Loading...
                          </span>
                        </div>
                      ) : !session ? (
                        // Not authenticated
                        <div className="flex items-center gap-2">
                          <Link className="theme-btn1" href="/contact">
                            Start Digital Transformation
                            <span>
                              <i className="fa-solid fa-arrow-right" />
                            </span>
                          </Link>
                          <Link className="theme-btn1" href="/auth/signin">
                              Sign In
                              <span>
                                <LogIn className="w-4 h-4 mr-2" />
                              </span>
                          </Link>
                        </div>
                      ) : (
                        // Authenticated
                        <div className="flex items-center gap-3">
                          <div className="flex items-center gap-2">
                            <Badge
                              variant="outline"
                              className="flex items-center gap-1"
                            >
                              <Shield className="w-3 h-3" />
                              {session.user.role}
                            </Badge>
                            {session.user.role === "ADMIN" && (
                              <Button asChild size="sm" variant="default">
                                <Link href="/admin">
                                  <Settings className="w-4 h-4 mr-2" />
                                  Admin
                                </Link>
                              </Button>
                            )}
                          </div>

                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button
                                variant="ghost"
                                className="relative h-8 w-8 rounded-full"
                              >
                                <Avatar className="h-8 w-8">
                                  <AvatarImage
                                    src={session.user.image || ""}
                                    alt={session.user.name || ""}
                                  />
                                  <AvatarFallback>
                                    {session.user.name
                                      ? session.user.name
                                          .split(" ")
                                          .map((n) => n[0])
                                          .join("")
                                          .toUpperCase()
                                      : session.user.email?.[0].toUpperCase()}
                                  </AvatarFallback>
                                </Avatar>
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent
                              className="w-56"
                              align="end"
                              forceMount
                            >
                              <DropdownMenuLabel className="font-normal">
                                <div className="flex flex-col space-y-1">
                                  <p className="text-sm font-medium leading-none">
                                    {session.user.name || "User"}
                                  </p>
                                  <p className="text-xs leading-none text-muted-foreground">
                                    {session.user.email}
                                  </p>
                                </div>
                              </DropdownMenuLabel>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem>
                                <User className="mr-2 h-4 w-4" />
                                <span>Profile</span>
                              </DropdownMenuItem>
                              {session.user.role === "ADMIN" && (
                                <DropdownMenuItem asChild>
                                  <Link href="/admin">
                                    <Settings className="mr-2 h-4 w-4" />
                                    <span>Admin Panel</span>
                                  </Link>
                                </DropdownMenuItem>
                              )}
                              <DropdownMenuSeparator />
                              <DropdownMenuItem onClick={handleSignOut}>
                                <LogOut className="mr-2 h-4 w-4" />
                                <span>Log out</span>
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <MobileMenu
          isMobileMenu={isMobileMenu}
          handleMobileMenu={handleMobileMenu}
        />
      </header>
      <div className="mobile-header mobile-header-main d-block d-lg-none">
        <div className="container-fluid">
          <div className="col-12">
            <div className="mobile-header-elements flex items-center justify-between py-3">
              <div className="mobile-logo">
                <Link href="/" className="flex items-center">
                  <img
                    src="assets/img/logo/logo_full.svg"
                    alt="Motshwanelo IT Consulting"
                    className="h-10 w-auto transition-all duration-300"
                  />
                </Link>
              </div>
              <div className="flex items-center gap-3">
                {/* Mobile Auth */}
                {status === "loading" ? (
                  <div className="w-5 h-5 border-2 border-blue-300 border-t-blue-600 rounded-full animate-spin"></div>
                ) : !session ? (
                  <Button asChild variant="outline" size="sm" className="hidden sm:flex">
                    <Link href="/auth/signin">
                      <LogIn className="w-4 h-4 mr-2" />
                      Sign In
                    </Link>
                  </Button>
                ) : (
                  <div className="flex items-center gap-2">
                    {session.user.role === "ADMIN" && (
                      <Button asChild size="sm" variant="default">
                        <Link href="/admin">
                          <Settings className="w-4 h-4" />
                        </Link>
                      </Button>
                    )}
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          className="relative h-8 w-8 rounded-full"
                        >
                          <Avatar className="h-6 w-6">
                            <AvatarImage
                              src={session.user.image || ""}
                              alt={session.user.name || ""}
                            />
                            <AvatarFallback className="text-xs">
                              {session.user.name
                                ? session.user.name
                                    .split(" ")
                                    .map((n) => n[0])
                                    .join("")
                                    .toUpperCase()
                                : session.user.email?.[0].toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent
                        className="w-56"
                        align="end"
                        forceMount
                      >
                        <DropdownMenuLabel className="font-normal">
                          <div className="flex flex-col space-y-1">
                            <p className="text-sm font-medium leading-none">
                              {session.user.name || "User"}
                            </p>
                            <p className="text-xs leading-none text-muted-foreground">
                              {session.user.email}
                            </p>
                            <Badge
                              variant="outline"
                              className="w-fit text-xs mt-1"
                            >
                              <Shield className="w-3 h-3 mr-1" />
                              {session.user.role}
                            </Badge>
                          </div>
                        </DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem>
                          <User className="mr-2 h-4 w-4" />
                          <span>Profile</span>
                        </DropdownMenuItem>
                        {session.user.role === "ADMIN" && (
                          <DropdownMenuItem asChild>
                            <Link href="/admin">
                              <Settings className="mr-2 h-4 w-4" />
                              <span>Admin Panel</span>
                            </Link>
                          </DropdownMenuItem>
                        )}
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={handleSignOut}>
                          <LogOut className="mr-2 h-4 w-4" />
                          <span>Log out</span>
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                )}
                <button
                  className="mobile-nav-icon flex items-center justify-center w-10 h-10 rounded-lg bg-blue-50 hover:bg-blue-100 transition-all duration-300 border border-blue-200"
                  onClick={handleMobileMenu}
                  aria-label="Toggle mobile menu"
                >
                  <i className={`fa-solid ${isMobileMenu ? 'fa-xmark' : 'fa-bars'} text-blue-600 transition-all duration-300 text-lg`} />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
