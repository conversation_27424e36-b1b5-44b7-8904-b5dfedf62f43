# Page Builder System

This system allows you to create editable duplicates of your components that can be used as blocks in a page builder. The blocks are stored in Appwrite and can be dynamically loaded and rendered on your pages.

## Setup

1. Install the required dependencies:

```bash
npm install appwrite tailwindcss @tailwindcss/forms uuid
```

2. Create an Appwrite account and set up a project with the following:
   - A database for storing blocks and pages
   - A collection for blocks with the appropriate structure
   - A collection for pages with the appropriate structure
   - A storage bucket for images

3. Copy the `.env.example` file to `.env.local` and fill in your Appwrite credentials:

```bash
cp .env.example .env.local
```

4. Edit the `.env.local` file with your Appwrite credentials:

```
NEXT_PUBLIC_APPWRITE_ENDPOINT=https://cloud.appwrite.io/v1
NEXT_PUBLIC_APPWRITE_PROJECT_ID=your-project-id
NEXT_PUBLIC_APPWRITE_DATABASE_ID=your-database-id
NEXT_PUBLIC_APPWRITE_BLOCKS_COLLECTION_ID=your-blocks-collection-id
NEXT_PUBLIC_APPWRITE_PAGES_COLLECTION_ID=your-pages-collection-id
NEXT_PUBLIC_APPWRITE_STORAGE_ID=your-storage-id
```

## Usage

### Converting Components to Blocks

To convert an existing component to an editable block, use the CLI script:

```bash
# First, compile the TypeScript files
npx tsc scripts/cli.ts scripts/generateBlockComponent.ts utils/componentToBlock.ts --esModuleInterop

# Then run the script
node scripts/cli.js
```

The script will guide you through the process of selecting a component type and a specific component to convert. It will generate the following files:

- `components/blocks/{type}/{ComponentName}Block.tsx`: The block component
- `components/blocks/{type}/{ComponentName}Editor.tsx`: The editor component
- `components/blocks/{type}/{ComponentName}Renderer.tsx`: The renderer component

### Building Pages with Blocks

Once you have converted your components to blocks, you can use the page builder to create pages:

1. Navigate to `/admin/page-builder` in your browser
2. Select blocks from the available blocks list
3. Arrange and edit the blocks as needed
4. Save the page

### Rendering Pages

Pages built with the page builder can be rendered using the dynamic page renderer:

- Navigate to `/{slug}` in your browser, where `{slug}` is the slug of the page you created

## File Structure

- `utils/appwrite.ts`: Appwrite configuration and helper functions
- `utils/componentToBlock.ts`: Utilities for converting components to blocks
- `types/block.ts`: TypeScript types for blocks
- `scripts/generateBlockComponent.ts`: Script for generating block components
- `scripts/cli.ts`: CLI for converting components to blocks
- `components/blocks/BlockRenderer.tsx`: Component for rendering blocks
- `components/blocks/PageBuilder.tsx`: Component for building pages with blocks
- `pages/api/pages.ts`: API endpoint for saving pages
- `pages/admin/page-builder.tsx`: Page builder admin interface
- `pages/[slug].tsx`: Dynamic page renderer

## Customization

### Adding New Block Types

To add a new block type:

1. Add the new block type to the `types/block.ts` file
2. Add a new conversion function in `utils/componentToBlock.ts`
3. Update the `convertComponentToBlock` function to handle the new type
4. Update the `BlockRenderer` component to render the new block type

### Customizing the Editor

The editor components are generated with basic form controls for editing block content. You can customize these components to add more advanced editing capabilities, such as:

- Rich text editing
- Image uploads
- Color pickers
- Layout controls

### Adding Tailwind Support

The system already includes basic Tailwind class configuration. To add more advanced Tailwind support:

1. Add a `styles` property to your block types
2. Update the editor components to allow editing of Tailwind classes
3. Apply the dynamic classes in your block components

## Best Practices

- Keep your components modular and focused on a single responsibility
- Use meaningful names for your blocks and components
- Document your block types and their properties
- Test your blocks in different contexts before using them in production
- Regularly back up your Appwrite database

## Troubleshooting

- If blocks are not rendering, check that the block ID is correct
- If the editor is not saving changes, check your Appwrite permissions
- If images are not loading, check your Appwrite storage configuration
- If the page builder is not working, check your browser console for errors