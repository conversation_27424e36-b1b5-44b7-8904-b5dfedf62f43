"use client"

import { useSession } from "next-auth/react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export default function DebugAuthPage() {
  const { data: session, status } = useSession()

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
      <Card className="w-full max-w-2xl">
        <CardHeader>
          <CardTitle>Authentication Debug</CardTitle>
          <CardDescription>
            This page shows the current authentication state
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <strong>Status:</strong> {status}
            </div>
            <div>
              <strong>Session:</strong>
              <pre className="mt-2 p-4 bg-gray-100 rounded-md overflow-auto">
                {JSON.stringify(session, null, 2)}
              </pre>
            </div>
            <div>
              <strong>User Role:</strong> {session?.user?.role || "Not available"}
            </div>
            <div>
              <strong>User ID:</strong> {session?.user?.id || "Not available"}
            </div>
            <div>
              <strong>User Email:</strong> {session?.user?.email || "Not available"}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}