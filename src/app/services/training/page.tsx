"use client";

import Layout from "@/components/layout/Layout";
import SectionHeader from "@/components/layout/SectionHeader";

export default function TrainingPage() {
    return (
        <>
            <Layout>
                <SectionHeader 
                    title="Skills Development & Training" 
                    group_page="Empowering the Next Generation of IT Professionals" 
                    current_page="Training" 
                    display="d-none" 
                />
                
                <div className="service-details-section sp">
                    <div className="container">
                        <div className="row">
                            <div className="col-lg-8">
                                <div className="service-details-content">
                                    <h2>🎓 Comprehensive IT Skills Development</h2>
                                    <p className="lead">
                                        We're not just service providers—we're skill builders. Our comprehensive training programs 
                                        have empowered 500+ IT professionals across South Africa, providing them with cutting-edge 
                                        skills and industry certifications that drive career success and technological innovation.
                                    </p>
                                    
                                    <div className="training-programs mt-4">
                                        <div className="row">
                                            <div className="col-md-6 mb-4">
                                                <div className="program-item">
                                                    <div className="icon">
                                                        <i className="fas fa-graduation-cap text-primary"></i>
                                                    </div>
                                                    <h4>Graduate Empowerment Programs</h4>
                                                    <p>Comprehensive programs designed to bridge the gap between academic learning and industry requirements.</p>
                                                    <div className="program-features">
                                                        <ul>
                                                            <li>12-month structured programs</li>
                                                            <li>Industry mentorship</li>
                                                            <li>Real-world project experience</li>
                                                            <li>Job placement assistance</li>
                                                            <li>Professional networking opportunities</li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div className="col-md-6 mb-4">
                                                <div className="program-item">
                                                    <div className="icon">
                                                        <i className="fas fa-tools text-primary"></i>
                                                    </div>
                                                    <h4>Technical Training Workshops</h4>
                                                    <p>Hands-on technical training in the latest technologies and industry best practices.</p>
                                                    <div className="program-features">
                                                        <ul>
                                                            <li>Cloud Computing (AWS, Azure)</li>
                                                            <li>Cybersecurity & Ethical Hacking</li>
                                                            <li>Software Development</li>
                                                            <li>Data Centre Management</li>
                                                            <li>Network Administration</li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div className="col-md-6 mb-4">
                                                <div className="program-item">
                                                    <div className="icon">
                                                        <i className="fas fa-certificate text-primary"></i>
                                                    </div>
                                                    <h4>Industry Certifications</h4>
                                                    <p>Preparation programs for globally recognized IT certifications that enhance career prospects.</p>
                                                    <div className="program-features">
                                                        <ul>
                                                            <li>CompTIA Certifications</li>
                                                            <li>Cisco CCNA/CCNP</li>
                                                            <li>Microsoft Certifications</li>
                                                            <li>AWS Cloud Practitioner</li>
                                                            <li>Huawei ICT Certifications</li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div className="col-md-6 mb-4">
                                                <div className="program-item">
                                                    <div className="icon">
                                                        <i className="fas fa-rocket text-primary"></i>
                                                    </div>
                                                    <h4>Career Development</h4>
                                                    <p>Comprehensive career guidance and professional development support for IT professionals.</p>
                                                    <div className="program-features">
                                                        <ul>
                                                            <li>Career coaching & mentoring</li>
                                                            <li>Resume & portfolio development</li>
                                                            <li>Interview preparation</li>
                                                            <li>Professional skills training</li>
                                                            <li>Leadership development</li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div className="training-approach mt-5">
                                        <h3>Our Training Methodology</h3>
                                        <div className="approach-grid">
                                            <div className="approach-item">
                                                <div className="approach-icon">
                                                    <i className="fas fa-chalkboard-teacher"></i>
                                                </div>
                                                <h4>Expert-Led Instruction</h4>
                                                <p>Learn from industry experts with real-world experience and deep technical knowledge</p>
                                            </div>
                                            <div className="approach-item">
                                                <div className="approach-icon">
                                                    <i className="fas fa-hands-helping"></i>
                                                </div>
                                                <h4>Hands-On Learning</h4>
                                                <p>Practical, project-based learning with access to real industry tools and environments</p>
                                            </div>
                                            <div className="approach-item">
                                                <div className="approach-icon">
                                                    <i className="fas fa-users"></i>
                                                </div>
                                                <h4>Collaborative Environment</h4>
                                                <p>Team-based projects and peer learning opportunities that mirror real workplace dynamics</p>
                                            </div>
                                            <div className="approach-item">
                                                <div className="approach-icon">
                                                    <i className="fas fa-chart-line"></i>
                                                </div>
                                                <h4>Continuous Assessment</h4>
                                                <p>Regular progress evaluation and personalized feedback to ensure optimal learning outcomes</p>
                                            </div>
                                        </div>
                                    </div>

                                    <div className="success-metrics mt-5">
                                        <h3>Our Impact & Success Stories</h3>
                                        <div className="metrics-row">
                                            <div className="metric-card">
                                                <div className="metric-number">500+</div>
                                                <div className="metric-label">Professionals Trained</div>
                                            </div>
                                            <div className="metric-card">
                                                <div className="metric-number">85%</div>
                                                <div className="metric-label">Job Placement Rate</div>
                                            </div>
                                            <div className="metric-card">
                                                <div className="metric-number">200+</div>
                                                <div className="metric-label">Partner Companies</div>
                                            </div>
                                            <div className="metric-card">
                                                <div className="metric-number">95%</div>
                                                <div className="metric-label">Certification Success Rate</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div className="col-lg-4">
                                <div className="service-sidebar">
                                    <div className="sidebar-widget">
                                        <h3>Join Our Programs</h3>
                                        <div className="contact-info">
                                            <p><i className="fas fa-phone"></i> +27 11 4 9 20992</p>
                                            <p><i className="fas fa-envelope"></i> <EMAIL></p>
                                        </div>
                                        <a href="/contact" className="theme-btn1 w-100 text-center">
                                            Apply for Training
                                            <span><i className="fas fa-arrow-right"></i></span>
                                        </a>
                                    </div>
                                    
                                    <div className="sidebar-widget">
                                        <h3>Training Tracks</h3>
                                        <ul className="training-tracks">
                                            <li><i className="fas fa-cloud"></i> Cloud Engineering</li>
                                            <li><i className="fas fa-shield-alt"></i> Cybersecurity</li>
                                            <li><i className="fas fa-code"></i> Software Development</li>
                                            <li><i className="fas fa-database"></i> Data Science & Analytics</li>
                                            <li><i className="fas fa-network-wired"></i> Network Engineering</li>
                                            <li><i className="fas fa-mobile-alt"></i> Mobile App Development</li>
                                        </ul>
                                    </div>
                                    
                                    <div className="sidebar-widget">
                                        <h3>Program Benefits</h3>
                                        <div className="benefits-list">
                                            <div className="benefit-item">
                                                <i className="fas fa-check-circle text-success"></i>
                                                <span>Industry-relevant curriculum</span>
                                            </div>
                                            <div className="benefit-item">
                                                <i className="fas fa-check-circle text-success"></i>
                                                <span>Experienced instructors</span>
                                            </div>
                                            <div className="benefit-item">
                                                <i className="fas fa-check-circle text-success"></i>
                                                <span>Certification preparation</span>
                                            </div>
                                            <div className="benefit-item">
                                                <i className="fas fa-check-circle text-success"></i>
                                                <span>Job placement assistance</span>
                                            </div>
                                            <div className="benefit-item">
                                                <i className="fas fa-check-circle text-success"></i>
                                                <span>Flexible learning options</span>
                                            </div>
                                            <div className="benefit-item">
                                                <i className="fas fa-check-circle text-success"></i>
                                                <span>Ongoing career support</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <style jsx>{`
                    .program-item {
                        background: #f8f9fa;
                        padding: 25px;
                        border-radius: 10px;
                        height: 100%;
                        border-top: 4px solid #03276e;
                    }
                    
                    .program-item .icon {
                        font-size: 2.5rem;
                        margin-bottom: 15px;
                    }
                    
                    .program-features ul {
                        list-style: none;
                        padding: 0;
                        margin-top: 15px;
                    }
                    
                    .program-features li {
                        padding: 5px 0;
                        position: relative;
                        padding-left: 20px;
                    }
                    
                    .program-features li:before {
                        content: "•";
                        position: absolute;
                        left: 0;
                        color: #03276e;
                        font-weight: bold;
                    }
                    
                    .approach-grid {
                        display: grid;
                        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                        gap: 25px;
                        margin-top: 20px;
                    }
                    
                    .approach-item {
                        text-align: center;
                        padding: 20px;
                        border: 1px solid #e9ecef;
                        border-radius: 10px;
                    }
                    
                    .approach-icon {
                        width: 60px;
                        height: 60px;
                        border-radius: 50%;
                        background: #03276e;
                        color: white;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        margin: 0 auto 15px;
                        font-size: 1.5rem;
                    }
                    
                    .metrics-row {
                        display: grid;
                        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                        gap: 20px;
                        margin-top: 20px;
                    }
                    
                    .metric-card {
                        background: #03276e;
                        color: white;
                        padding: 30px;
                        border-radius: 10px;
                        text-align: center;
                    }
                    
                    .metric-number {
                        font-size: 2.5rem;
                        font-weight: bold;
                        margin-bottom: 10px;
                    }
                    
                    .metric-label {
                        font-size: 1rem;
                        opacity: 0.9;
                    }
                    
                    .service-sidebar {
                        padding-left: 30px;
                    }
                    
                    .sidebar-widget {
                        background: #f8f9fa;
                        padding: 25px;
                        border-radius: 10px;
                        margin-bottom: 30px;
                    }
                    
                    .contact-info p {
                        margin-bottom: 10px;
                        display: flex;
                        align-items: center;
                        gap: 10px;
                    }
                    
                    .training-tracks {
                        list-style: none;
                        padding: 0;
                    }
                    
                    .training-tracks li {
                        padding: 10px 0;
                        display: flex;
                        align-items: center;
                        gap: 10px;
                        border-bottom: 1px solid #dee2e6;
                    }
                    
                    .training-tracks i {
                        color: #03276e;
                        width: 20px;
                    }
                    
                    .benefits-list {
                        display: flex;
                        flex-direction: column;
                        gap: 10px;
                    }
                    
                    .benefit-item {
                        display: flex;
                        align-items: center;
                        gap: 10px;
                    }
                    
                    @media (max-width: 768px) {
                        .service-sidebar {
                            padding-left: 0;
                            margin-top: 30px;
                        }
                    }
                `}</style>
            </Layout>
        </>
    );
}