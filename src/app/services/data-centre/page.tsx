import Layout from "@/components/layout/Layout";
import SectionHeader from "@/components/layout/SectionHeader";
import type { Metadata } from "next";
import DataCentreContent from "./DataCentreContent";

export const metadata: Metadata = {
  title: "Data Centre Infrastructure | Motshwanelo IT Consulting - Enterprise-Grade Solutions",
  description: "Discover our world-class Data Centre infrastructure solutions featuring Fusion Module 2000, NetCol5000, and UPS5000. 99.999% uptime guarantee with enterprise-grade security and efficiency for South African businesses.",
  keywords: ["Data Centre", "Fusion Module 2000", "NetCol5000", "UPS5000", "Data Center Infrastructure", "Enterprise Data Solutions", "Server Housing"],
  openGraph: {
    title: "Data Centre Infrastructure | Enterprise-Grade Solutions",
    description: "World-class Data Centre infrastructure with 99.999% uptime guarantee. Featuring cutting-edge Fusion Module 2000 and advanced cooling systems.",
    type: "website",
    url: "https://motshwaneloitconsulting.co.za/services/data-centre",
  },
};

export default function DataCentrePage() {
    return (
        <>
            <Layout>
                <SectionHeader 
                    title="Data Centre Infrastructure Excellence" 
                    group_page="Enterprise-Grade Solutions with 99.999% Uptime" 
                    current_page="Data Centre" 
                    display="d-none" 
                />

                <DataCentreContent />
            </Layout>
        </>
    );
}