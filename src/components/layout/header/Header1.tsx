"use client";

import Link from "next/link";
import { useState } from "react";
import { useSession, signOut } from "next-auth/react";
import { usePathname } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  User,
  LogOut,
  Settings,
  Shield,
  LogIn,
  ChevronDown,
  Menu,
  X,
  Home,
  Users,
  Cog,
  Lightbulb,
  Briefcase,
  BookOpen,
  Mail,
  ArrowRight
} from "lucide-react";

// Modern Navigation Data
const navigationItems = [
  {
    name: "Home",
    href: "/",
    icon: Home,
  },
  {
    name: "About",
    href: "/about",
    icon: Users,
  },
  {
    name: "Services",
    href: "/service",
    icon: Cog,
    dropdown: [
      { name: "All Services", href: "/service", description: "Complete overview of our services" },
      { name: "Data Centre Solutions", href: "/services/data-centre", description: "Enterprise data center infrastructure" },
      { name: "Smart City Solutions", href: "/services/smart-city", description: "IoT and smart city technologies" },
      { name: "IT Consulting", href: "/services/it-consulting", description: "Strategic IT advisory services" },
      { name: "Software Development", href: "/services/software-development", description: "Custom software solutions" },
      { name: "Training & Support", href: "/services/training", description: "Professional training programs" },
    ]
  },
  {
    name: "Solutions",
    href: "/solutions",
    icon: Lightbulb,
    dropdown: [
      { name: "DIGIM Platform", href: "/solutions/digim", description: "Digital transformation platform" },
      { name: "NetEco Solutions", href: "/solutions/neteco", description: "Network ecosystem management" },
      { name: "FusionModule", href: "/solutions/fusion-module", description: "Modular data center solutions" },
      { name: "UPS Solutions", href: "/solutions/ups", description: "Uninterruptible power systems" },
    ]
  },
  {
    name: "Projects",
    href: "/project",
    icon: Briefcase,
    dropdown: [
      { name: "All Projects", href: "/project", description: "View our complete portfolio" },
      { name: "Enhanced Projects", href: "/enhanced-projects", description: "Featured project showcases" },
      { name: "Success Stories", href: "/success-stories", description: "Client success case studies" },
      { name: "Government Solutions", href: "/project/government", description: "Public sector implementations" },
      { name: "Enterprise Solutions", href: "/project/enterprise", description: "Corporate project deliveries" },
    ]
  },
  {
    name: "Resources",
    href: "/blog",
    icon: BookOpen,
    dropdown: [
      { name: "Blog & News", href: "/blog", description: "Latest insights and updates" },
      { name: "Case Studies", href: "/case-studies", description: "Detailed project analyses" },
      { name: "Whitepapers", href: "/whitepapers", description: "Technical documentation" },
      { name: "Technology Showcase", href: "/technology-showcase", description: "Innovation demonstrations" },
    ]
  },
  {
    name: "Contact",
    href: "/contact",
    icon: Mail,
  },
];

export default function Header({
  scroll,
  isMobileMenu,
  handleMobileMenu,
  mainMenuStyle,
}: any) {
  const { data: session, status } = useSession();
  const pathname = usePathname();
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const handleSignOut = () => {
    signOut({ callbackUrl: "/auth/signin" });
  };

  const isActive = (href: string) => {
    if (href === "/" && pathname === "/") return true;
    if (href !== "/" && pathname.startsWith(href)) return true;
    return false;
  };

  const handleDropdownEnter = (name: string) => {
    setActiveDropdown(name);
  };

  const handleDropdownLeave = () => {
    setActiveDropdown(null);
  };

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  return (
    <>
      {/* Modern Desktop Header */}
      <header className="fixed top-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-md border-b border-gray-200/50 shadow-sm">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            {/* Logo */}
            <Link href="/" className="flex items-center space-x-2">
              <img
                src="assets/img/logo/logo_full.svg"
                alt="Motshwanelo IT Consulting"
                className="h-10 w-auto"
              />
            </Link>

            {/* Desktop Navigation */}
            <nav className="hidden lg:flex items-center space-x-1">
              {navigationItems.map((item) => (
                <div
                  key={item.name}
                  className="relative"
                  onMouseEnter={() => item.dropdown && handleDropdownEnter(item.name)}
                  onMouseLeave={handleDropdownLeave}
                >
                  <Link
                    href={item.href}
                    className={`flex items-center space-x-1 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                      isActive(item.href)
                        ? "bg-blue-50 text-blue-600"
                        : "text-gray-700 hover:bg-gray-50 hover:text-blue-600"
                    }`}
                  >
                    <item.icon className="w-4 h-4" />
                    <span>{item.name}</span>
                    {item.dropdown && (
                      <ChevronDown className={`w-3 h-3 transition-transform duration-200 ${
                        activeDropdown === item.name ? "rotate-180" : ""
                      }`} />
                    )}
                  </Link>

                  {/* Dropdown Menu */}
                  {item.dropdown && activeDropdown === item.name && (
                    <div className="absolute top-full left-0 mt-1 w-80 bg-white rounded-xl shadow-lg border border-gray-200/50 py-2 z-50">
                      {item.dropdown.map((dropdownItem) => (
                        <Link
                          key={dropdownItem.name}
                          href={dropdownItem.href}
                          className="block px-4 py-3 hover:bg-gray-50 transition-colors duration-200"
                        >
                          <div className="font-medium text-gray-900 text-sm">
                            {dropdownItem.name}
                          </div>
                          <div className="text-xs text-gray-500 mt-1">
                            {dropdownItem.description}
                          </div>
                        </Link>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </nav>

            {/* Right Section */}
            <div className="flex items-center space-x-3">
              {/* CTA Button */}
              <Link
                href="/contact"
                className="hidden md:flex items-center space-x-2 bg-gradient-to-r from-blue-600 to-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium hover:from-blue-700 hover:to-blue-800 transition-all duration-200 shadow-sm"
              >
                <span>Start Digital Transformation</span>
                <ArrowRight className="w-4 h-4" />
              </Link>

              {/* Auth Section */}
              {status === "loading" ? (
                <div className="w-6 h-6 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin"></div>
              ) : !session ? (
                <Link
                  href="/auth/signin"
                  className="flex items-center space-x-2 text-gray-700 hover:text-blue-600 px-3 py-2 rounded-lg hover:bg-gray-50 transition-all duration-200"
                >
                  <LogIn className="w-4 h-4" />
                  <span className="hidden sm:block">Sign In</span>
                </Link>
              ) : (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="relative h-10 w-10 rounded-full">
                      <Avatar className="h-10 w-10">
                        <AvatarImage src={session.user?.image || ""} alt={session.user?.name || ""} />
                        <AvatarFallback className="bg-blue-600 text-white">
                          {session.user?.name?.charAt(0) || session.user?.email?.charAt(0) || "U"}
                        </AvatarFallback>
                      </Avatar>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-56" align="end" forceMount>
                    <DropdownMenuLabel className="font-normal">
                      <div className="flex flex-col space-y-1">
                        <p className="text-sm font-medium leading-none">{session.user?.name}</p>
                        <p className="text-xs leading-none text-muted-foreground">{session.user?.email}</p>
                      </div>
                    </DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem asChild>
                      <Link href="/admin" className="flex items-center">
                        <User className="mr-2 h-4 w-4" />
                        <span>Profile</span>
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link href="/admin" className="flex items-center">
                        <Settings className="mr-2 h-4 w-4" />
                        <span>Settings</span>
                      </Link>
                    </DropdownMenuItem>
                    {session.user?.role === "ADMIN" && (
                      <DropdownMenuItem asChild>
                        <Link href="/admin" className="flex items-center">
                          <Shield className="mr-2 h-4 w-4" />
                          <span>Admin Panel</span>
                          <Badge variant="secondary" className="ml-auto">Admin</Badge>
                        </Link>
                      </DropdownMenuItem>
                    )}
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={handleSignOut} className="text-red-600">
                      <LogOut className="mr-2 h-4 w-4" />
                      <span>Log out</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              )}

              {/* Mobile Menu Button */}
              <button
                onClick={toggleMobileMenu}
                className="lg:hidden p-2 rounded-lg hover:bg-gray-50 transition-colors duration-200"
              >
                {isMobileMenuOpen ? (
                  <X className="w-5 h-5 text-gray-700" />
                ) : (
                  <Menu className="w-5 h-5 text-gray-700" />
                )}
              </button>
            </div>
          </div>
        </div>
                </div>
              </div>
            </div>
          </div>

        <MobileMenu
          isMobileMenu={isMobileMenu}
          handleMobileMenu={handleMobileMenu}
        />
      </header>
      <div className="mobile-header mobile-header-main d-block d-lg-none">
        <div className="container-fluid">
          <div className="col-12">
            <div className="mobile-header-elements flex items-center justify-between py-3">
              <div className="mobile-logo">
                <Link href="/" className="flex items-center">
                  <img
                    src="assets/img/logo/logo_full.svg"
                    alt="Motshwanelo IT Consulting"
                    className="h-10 w-auto transition-all duration-300"
                  />
                </Link>
              </div>
              <div className="flex items-center gap-3">
                {/* Mobile Auth */}
                {status === "loading" ? (
                  <div className="w-5 h-5 border-2 border-blue-300 border-t-blue-600 rounded-full animate-spin"></div>
                ) : !session ? (
                  <Button asChild variant="outline" size="sm" className="hidden sm:flex">
                    <Link href="/auth/signin">
                      <LogIn className="w-4 h-4 mr-2" />
                      Sign In
                    </Link>
                  </Button>
                ) : (
                  <div className="flex items-center gap-2">
                    {session.user.role === "ADMIN" && (
                      <Button asChild size="sm" variant="default">
                        <Link href="/admin">
                          <Settings className="w-4 h-4" />
                        </Link>
                      </Button>
                    )}
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          className="relative h-8 w-8 rounded-full"
                        >
                          <Avatar className="h-6 w-6">
                            <AvatarImage
                              src={session.user.image || ""}
                              alt={session.user.name || ""}
                            />
                            <AvatarFallback className="text-xs">
                              {session.user.name
                                ? session.user.name
                                    .split(" ")
                                    .map((n) => n[0])
                                    .join("")
                                    .toUpperCase()
                                : session.user.email?.[0].toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent
                        className="w-56"
                        align="end"
                        forceMount
                      >
                        <DropdownMenuLabel className="font-normal">
                          <div className="flex flex-col space-y-1">
                            <p className="text-sm font-medium leading-none">
                              {session.user.name || "User"}
                            </p>
                            <p className="text-xs leading-none text-muted-foreground">
                              {session.user.email}
                            </p>
                            <Badge
                              variant="outline"
                              className="w-fit text-xs mt-1"
                            >
                              <Shield className="w-3 h-3 mr-1" />
                              {session.user.role}
                            </Badge>
                          </div>
                        </DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem>
                          <User className="mr-2 h-4 w-4" />
                          <span>Profile</span>
                        </DropdownMenuItem>
                        {session.user.role === "ADMIN" && (
                          <DropdownMenuItem asChild>
                            <Link href="/admin">
                              <Settings className="mr-2 h-4 w-4" />
                              <span>Admin Panel</span>
                            </Link>
                          </DropdownMenuItem>
                        )}
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={handleSignOut}>
                          <LogOut className="mr-2 h-4 w-4" />
                          <span>Log out</span>
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                )}
                <button
                  className="mobile-nav-icon flex items-center justify-center w-10 h-10 rounded-lg bg-blue-50 hover:bg-blue-100 transition-all duration-300 border border-blue-200"
                  onClick={handleMobileMenu}
                  aria-label="Toggle mobile menu"
                >
                  <i className={`fa-solid ${isMobileMenu ? 'fa-xmark' : 'fa-bars'} text-blue-600 transition-all duration-300 text-lg`} />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
