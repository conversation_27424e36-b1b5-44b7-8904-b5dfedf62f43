import AnimatedHero from "@/components/sections/home/<USER>";
import InteractiveTechStack from "@/components/sections/technology/InteractiveTechStack";
import AnimatedServicesShowcase from "@/components/sections/services/AnimatedServicesShowcase";
import dynamic from "next/dynamic";

// Import existing home sections
const Section5 = dynamic(() => import("@/components/sections/home/<USER>"));
const Section6 = dynamic(() => import("@/components/sections/home/<USER>"));
const Section7 = dynamic(() => import("@/components/sections/home/<USER>"));
const Section8 = dynamic(() => import("@/components/sections/home/<USER>"));
const Section9 = dynamic(() => import("@/components/sections/home/<USER>"));

export const metadata = {
  title: "Animated Home | Motshwanelo IT Consulting",
  description: "Experience Motshwanelo IT Consulting with engaging animations and interactive elements.",
};

export default function AnimatedHomePage() {
  return (
    <>
      <AnimatedHero />
      <AnimatedServicesShowcase />
      <InteractiveTechStack />
      <Section5 />
      <Section6 />
      <Section7 />
      <Section8 />
      <Section9 />
    </>
  );
}