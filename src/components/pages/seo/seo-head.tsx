'use client'

import Head from 'next/head'
import { useRouter } from 'next/navigation'

interface SEOHeadProps {
  title: string
  description?: string
  image?: string
  url?: string
  type?: 'website' | 'article' | 'profile'
  siteName?: string
  locale?: string
  publishedTime?: Date
  modifiedTime?: Date
  author?: string
  tags?: string[]
  canonical?: string
  noindex?: boolean
  nofollow?: boolean
  schema?: any
}

export function SEOHead({
  title,
  description,
  image,
  url,
  type = 'website',
  siteName = 'Your Site Name',
  locale = 'en_US',
  publishedTime,
  modifiedTime,
  author,
  tags = [],
  canonical,
  noindex = false,
  nofollow = false,
  schema,
}: SEOHeadProps) {
  const router = useRouter()
  
  // Get current URL if not provided
  const currentUrl = url || (typeof window !== 'undefined' ? window.location.href : '')
  const canonicalUrl = canonical || currentUrl

  // Default image
  const defaultImage = '/images/og-default.jpg'
  const ogImage = image || defaultImage

  // Ensure absolute URLs
  const absoluteUrl = currentUrl.startsWith('http') ? currentUrl : `${process.env.NEXT_PUBLIC_SITE_URL}${currentUrl}`
  const absoluteImage = ogImage.startsWith('http') ? ogImage : `${process.env.NEXT_PUBLIC_SITE_URL}${ogImage}`

  // Generate structured data
  const generateSchema = () => {
    if (schema) return schema

    const baseSchema = {
      '@context': 'https://schema.org',
      '@type': type === 'article' ? 'Article' : 'WebPage',
      name: title,
      headline: title,
      description: description,
      url: absoluteUrl,
      image: absoluteImage,
    }

    if (type === 'article') {
      return {
        ...baseSchema,
        '@type': 'Article',
        author: author ? {
          '@type': 'Person',
          name: author,
        } : undefined,
        publisher: {
          '@type': 'Organization',
          name: siteName,
          logo: {
            '@type': 'ImageObject',
            url: `${process.env.NEXT_PUBLIC_SITE_URL}/images/logo.png`,
          },
        },
        datePublished: publishedTime?.toISOString(),
        dateModified: modifiedTime?.toISOString(),
        keywords: tags.join(', '),
      }
    }

    return baseSchema
  }

  const structuredData = generateSchema()

  return (
    <Head>
      {/* Basic Meta Tags */}
      <title>{title}</title>
      {description && <meta name="description" content={description} />}
      {tags.length > 0 && <meta name="keywords" content={tags.join(', ')} />}
      {author && <meta name="author" content={author} />}
      
      {/* Canonical URL */}
      <link rel="canonical" href={canonicalUrl} />
      
      {/* Robots */}
      <meta 
        name="robots" 
        content={`${noindex ? 'noindex' : 'index'},${nofollow ? 'nofollow' : 'follow'}`} 
      />
      
      {/* Open Graph */}
      <meta property="og:type" content={type} />
      <meta property="og:title" content={title} />
      {description && <meta property="og:description" content={description} />}
      <meta property="og:url" content={absoluteUrl} />
      <meta property="og:image" content={absoluteImage} />
      <meta property="og:image:alt" content={title} />
      <meta property="og:site_name" content={siteName} />
      <meta property="og:locale" content={locale} />
      
      {/* Article specific Open Graph */}
      {type === 'article' && (
        <>
          {author && <meta property="article:author" content={author} />}
          {publishedTime && (
            <meta property="article:published_time" content={publishedTime.toISOString()} />
          )}
          {modifiedTime && (
            <meta property="article:modified_time" content={modifiedTime.toISOString()} />
          )}
          {tags.map((tag, index) => (
            <meta key={index} property="article:tag" content={tag} />
          ))}
        </>
      )}
      
      {/* Twitter Card */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={title} />
      {description && <meta name="twitter:description" content={description} />}
      <meta name="twitter:image" content={absoluteImage} />
      <meta name="twitter:image:alt" content={title} />
      
      {/* Additional Meta Tags */}
      <meta name="viewport" content="width=device-width, initial-scale=1" />
      <meta httpEquiv="Content-Type" content="text/html; charset=utf-8" />
      <meta name="language" content={locale.split('_')[0]} />
      
      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData, null, 2),
        }}
      />
      
      {/* Favicon */}
      <link rel="icon" href="/favicon.ico" />
      <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
      <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
      <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
      <link rel="manifest" href="/site.webmanifest" />
    </Head>
  )
}

// Hook for managing SEO data
export function useSEO() {
  const router = useRouter()

  const updateSEO = (seoData: Partial<SEOHeadProps>) => {
    // Update document title
    if (seoData.title) {
      document.title = seoData.title
    }

    // Update meta description
    if (seoData.description) {
      const metaDescription = document.querySelector('meta[name="description"]')
      if (metaDescription) {
        metaDescription.setAttribute('content', seoData.description)
      }
    }

    // Update Open Graph tags
    if (seoData.title) {
      const ogTitle = document.querySelector('meta[property="og:title"]')
      if (ogTitle) {
        ogTitle.setAttribute('content', seoData.title)
      }
    }

    if (seoData.description) {
      const ogDescription = document.querySelector('meta[property="og:description"]')
      if (ogDescription) {
        ogDescription.setAttribute('content', seoData.description)
      }
    }

    if (seoData.image) {
      const ogImage = document.querySelector('meta[property="og:image"]')
      if (ogImage) {
        const absoluteImage = seoData.image.startsWith('http') 
          ? seoData.image 
          : `${process.env.NEXT_PUBLIC_SITE_URL}${seoData.image}`
        ogImage.setAttribute('content', absoluteImage)
      }
    }
  }

  return { updateSEO }
}

// Component for dynamic SEO updates
export function DynamicSEO({ 
  children, 
  ...seoProps 
}: SEOHeadProps & { children: React.ReactNode }) {
  return (
    <>
      <SEOHead {...seoProps} />
      {children}
    </>
  )
}

// SEO-optimized link component
interface SEOLinkProps {
  href: string
  children: React.ReactNode
  title?: string
  rel?: string
  target?: string
  className?: string
  prefetch?: boolean
}

export function SEOLink({ 
  href, 
  children, 
  title, 
  rel, 
  target, 
  className,
  prefetch = true 
}: SEOLinkProps) {
  const isExternal = href.startsWith('http') && !href.includes(process.env.NEXT_PUBLIC_SITE_URL || '')
  
  const linkRel = rel || (isExternal ? 'noopener noreferrer' : undefined)
  const linkTarget = target || (isExternal ? '_blank' : undefined)

  return (
    <a
      href={href}
      title={title}
      rel={linkRel}
      target={linkTarget}
      className={className}
    >
      {children}
    </a>
  )
}

// Breadcrumb structured data
export function BreadcrumbSchema({ items }: { 
  items: Array<{ name: string; url: string }> 
}) {
  const schema = {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: items.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.name,
      item: item.url.startsWith('http') ? item.url : `${process.env.NEXT_PUBLIC_SITE_URL}${item.url}`,
    })),
  }

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(schema, null, 2),
      }}
    />
  )
}

// FAQ structured data
export function FAQSchema({ faqs }: { 
  faqs: Array<{ question: string; answer: string }> 
}) {
  const schema = {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: faqs.map((faq) => ({
      '@type': 'Question',
      name: faq.question,
      acceptedAnswer: {
        '@type': 'Answer',
        text: faq.answer,
      },
    })),
  }

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(schema, null, 2),
      }}
    />
  )
}

// Organization structured data
export function OrganizationSchema({ 
  name, 
  url, 
  logo, 
  description,
  contactPoint,
  sameAs = []
}: {
  name: string
  url: string
  logo: string
  description?: string
  contactPoint?: {
    telephone: string
    contactType: string
    email?: string
  }
  sameAs?: string[]
}) {
  const schema = {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name,
    url,
    logo: {
      '@type': 'ImageObject',
      url: logo.startsWith('http') ? logo : `${process.env.NEXT_PUBLIC_SITE_URL}${logo}`,
    },
    description,
    contactPoint: contactPoint ? {
      '@type': 'ContactPoint',
      telephone: contactPoint.telephone,
      contactType: contactPoint.contactType,
      email: contactPoint.email,
    } : undefined,
    sameAs,
  }

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(schema, null, 2),
      }}
    />
  )
}