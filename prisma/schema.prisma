// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Authentication Models (Simplified for credentials-only auth)
model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String    @unique
  emailVerified DateTime?
  image         String?
  password      String    // Required for credentials authentication
  role          UserRole  @default(USER)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Auth relations
  sessions Session[]

  // CMS relations
  posts         Post[]
  pages         Page[]
  categories    Category[]
  tags          Tag[]
  media         Media[]
  comments      Comment[]
  createdBlocks Block[]   @relation("BlockCreator")
  updatedBlocks Block[]   @relation("BlockUpdater")

  @@map("users")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verificationtokens")
}

// CMS Models
enum UserRole {
  ADMIN
  USER
}

enum PostStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
  SCHEDULED
}

enum PageStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
}

enum MediaType {
  IMAGE
  VIDEO
  AUDIO
  DOCUMENT
  OTHER
}

model Category {
  id          String   @id @default(cuid())
  name        String   @unique
  slug        String   @unique
  description String?
  color       String?
  image       String?
  parentId    String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  createdBy   String
  
  parent   Category?  @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children Category[] @relation("CategoryHierarchy")
  creator  User       @relation(fields: [createdBy], references: [id])
  posts    Post[]

  @@map("categories")
}

model Tag {
  id        String   @id @default(cuid())
  name      String   @unique
  slug      String   @unique
  color     String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  createdBy String

  creator User   @relation(fields: [createdBy], references: [id])
  posts   Post[]

  @@map("tags")
}

model Post {
  id          String     @id @default(cuid())
  title       String
  slug        String     @unique
  excerpt     String?
  content     String?
  featuredImage String?
  status      PostStatus @default(DRAFT)
  publishedAt DateTime?
  scheduledAt DateTime?
  seoTitle    String?
  seoDescription String?
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  authorId    String
  categoryId  String?

  author   User      @relation(fields: [authorId], references: [id])
  category Category? @relation(fields: [categoryId], references: [id])
  tags     Tag[]
  comments Comment[]
  blocks   Block[]

  @@map("posts")
}

model Page {
  id          String     @id @default(cuid())
  title       String
  slug        String     @unique
  content     String?
  template    String?
  status      PageStatus @default(DRAFT)
  publishedAt DateTime?
  seoTitle    String?
  seoDescription String?
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  authorId    String

  author User    @relation(fields: [authorId], references: [id])
  blocks Block[]

  @@map("pages")
}

model Media {
  id          String    @id @default(cuid())
  filename    String
  originalName String
  mimeType    String
  size        Int
  width       Int?
  height      Int?
  url         String
  thumbnailUrl String?
  alt         String?
  caption     String?
  type        MediaType @default(OTHER)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  uploadedBy  String

  uploader User @relation(fields: [uploadedBy], references: [id])

  @@map("media")
}

model Comment {
  id        String   @id @default(cuid())
  content   String
  approved  Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  authorId  String
  postId    String
  parentId  String?

  author   User      @relation(fields: [authorId], references: [id])
  post     Post      @relation(fields: [postId], references: [id], onDelete: Cascade)
  parent   Comment?  @relation("CommentReplies", fields: [parentId], references: [id])
  replies  Comment[] @relation("CommentReplies")

  @@map("comments")
}

// Block System for Page Builder
model Block {
  id        String   @id @default(cuid())
  type      String   // e.g., "hero", "text", "image", "gallery", etc.
  data      Json     // Block-specific data
  order     Int      @default(0)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  createdBy String
  updatedBy String?
  postId    String?
  pageId    String?

  creator User  @relation("BlockCreator", fields: [createdBy], references: [id])
  updater User? @relation("BlockUpdater", fields: [updatedBy], references: [id])
  post    Post? @relation(fields: [postId], references: [id], onDelete: Cascade)
  page    Page? @relation(fields: [pageId], references: [id], onDelete: Cascade)

  @@map("blocks")
}

// Settings and Configuration
model Setting {
  id        String   @id @default(cuid())
  key       String   @unique
  value     Json
  type      String   @default("string") // string, number, boolean, json, etc.
  group     String?  // e.g., "general", "seo", "social", etc.
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("settings")
}

// Menu System
model Menu {
  id        String     @id @default(cuid())
  name      String     @unique
  location  String?    // e.g., "header", "footer", "sidebar"
  createdAt DateTime   @default(now())
  updatedAt DateTime   @updatedAt
  items     MenuItem[]

  @@map("menus")
}

model MenuItem {
  id       String  @id @default(cuid())
  label    String
  url      String?
  target   String? @default("_self")
  order    Int     @default(0)
  parentId String?
  menuId   String

  parent   MenuItem?  @relation("MenuItemHierarchy", fields: [parentId], references: [id])
  children MenuItem[] @relation("MenuItemHierarchy")
  menu     Menu       @relation(fields: [menuId], references: [id], onDelete: Cascade)

  @@map("menu_items")
}

// Analytics and Tracking
model PageView {
  id        String   @id @default(cuid())
  path      String
  userAgent String?
  ip        String?
  referer   String?
  createdAt DateTime @default(now())

  @@map("page_views")
}

// Form System
model Form {
  id          String      @id @default(cuid())
  name        String
  title       String
  description String?
  fields      Json        // Form field definitions
  settings    Json?       // Form settings (notifications, redirects, etc.)
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  submissions FormSubmission[]

  @@map("forms")
}

model FormSubmission {
  id        String   @id @default(cuid())
  data      Json     // Submitted form data
  ip        String?
  userAgent String?
  createdAt DateTime @default(now())
  formId    String

  form Form @relation(fields: [formId], references: [id], onDelete: Cascade)

  @@map("form_submissions")
}