{"name": "motshwanelo-it-consulting", "version": "1.0.1", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start -p 3079", "lint": "next lint", "generate-block": "tsc scripts/cli.ts scripts/generateBlockComponent.ts utils/componentToBlock.ts --esModuleInterop && node scripts/cli.js"}, "dependencies": {"@auth/prisma-adapter": "^2.10.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^5.1.1", "@prisma/client": "6.10.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@react-three/cannon": "^6.6.0", "@react-three/drei": "^10.3.0", "@react-three/fiber": "^9.1.2", "@tabler/icons-react": "^3.34.0", "@tailwindcss/forms": "^0.5.10", "@tanstack/react-table": "^8.21.3", "@tiptap/extension-character-count": "^2.14.0", "@tiptap/extension-code-block-lowlight": "^2.14.0", "@tiptap/extension-color": "^2.14.0", "@tiptap/extension-dropcursor": "^2.14.0", "@tiptap/extension-focus": "^2.14.0", "@tiptap/extension-gapcursor": "^2.14.0", "@tiptap/extension-image": "^2.14.0", "@tiptap/extension-link": "^2.14.0", "@tiptap/extension-placeholder": "^2.14.0", "@tiptap/extension-table": "^2.14.0", "@tiptap/extension-table-cell": "^2.14.0", "@tiptap/extension-table-header": "^2.14.0", "@tiptap/extension-table-row": "^2.14.0", "@tiptap/extension-text-align": "^2.14.0", "@tiptap/extension-text-style": "^2.14.0", "@tiptap/extension-underline": "^2.14.0", "@tiptap/react": "^2.14.0", "@tiptap/starter-kit": "^2.14.0", "@types/bcryptjs": "^3.0.0", "aos": "^2.3.4", "appwrite": "^18.1.1", "bcryptjs": "^3.0.2", "bootstrap": "^5.3.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "critters": "^0.0.25", "date-fns": "^3.2.0", "embla-carousel-react": "^8.6.0", "formik": "^2.4.5", "framer-motion": "^12.19.1", "gray-matter": "^4.0.3", "gsap": "^3.12.7", "input-otp": "^1.4.2", "isotope-layout": "^3.0.6", "lodash": "^4.17.21", "lowlight": "^3.3.0", "lucide-react": "^0.517.0", "mapbox-gl": "^3.1.0", "next": "^15.3.1", "next-auth": "5.0.0-beta.29", "next-themes": "^0.4.6", "nextjs-toploader": "^3.8.16", "nodemailer": "^6.10.0", "react": "^18.2.0", "react-day-picker": "^9.7.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.58.1", "react-modal-video": "^2.0.2", "react-resizable-panels": "^3.0.3", "recharts": "^2.15.3", "remark": "^15.0.1", "remark-html": "^16.0.1", "sass": "^1.70.0", "sonner": "^2.0.5", "split-text": "^1.0.0", "sticky-js": "^1.3.0", "swiper": "^11.2.5", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.10", "three": "^0.177.0", "uploadthing": "^7.7.2", "uuid": "^11.1.0", "vaul": "^1.1.2", "wowjs": "^1.1.3", "yet-another-react-lightbox": "^3.16.0", "yup": "^1.6.1", "zod": "^3.25.67", "zustand": "^5.0.5"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.10", "@types/aos": "^3.0.7", "@types/bootstrap": "^5.2.10", "@types/isotope-layout": "^3.0.14", "@types/lodash": "^4.17.18", "@types/node": "^22", "@types/nodemailer": "^6.4.17", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@types/react-dropzone": "^5.1.0", "@types/react-modal-video": "^1.2.3", "@types/uuid": "^9.0.8", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "^15.3.1", "node-appwrite": "^17.0.0", "postcss": "^8.5.6", "prisma": "^6.10.1", "tw-animate-css": "^1.3.4", "typescript": "5.5.2"}}