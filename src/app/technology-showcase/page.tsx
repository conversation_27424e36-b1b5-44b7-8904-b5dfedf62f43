import InteractiveTechStack from "@/components/sections/technology/InteractiveTechStack";
import PageBanner from "@/components/elements/PageBanner";

export const metadata = {
  title: "Technology Showcase | Motshwanelo IT Consulting",
  description: "Explore our technology stack in an interactive visualization.",
};

export default function TechnologyShowcasePage() {
  return (
    <>
      <PageBanner
        pageTitle="Technology Showcase"
        breadTitle="Technology Showcase"
        description="Discover the cutting-edge technologies that power our solutions in an interactive visualization."
        type="1"
      />
      
      <InteractiveTechStack />
      
      <section className="sp">
        <div className="container">
          <div className="row">
            <div className="col-lg-8 m-auto text-center">
              <div className="heading1">
                <h3>Why Our Technology Matters</h3>
                <div className="space16" />
                <p>
                  At Motshwanelo IT Consulting, we carefully select the best technologies for each project,
                  ensuring that our solutions are not only effective today but also adaptable for tomorrow's challenges.
                  Our expertise across multiple technology domains allows us to create integrated systems that deliver
                  real business value.
                </p>
                <div className="space30" />
                <a href="/contact" className="thm-btn">
                  <span>Discuss Your Technology Needs</span>
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
}