"use client";

import Link from "next/link";
import { useState, useEffect } from "react";
import { usePathname } from "next/navigation";
import { useSession, signOut } from "next-auth/react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { User, LogOut, Settings, Shield, LogIn } from "lucide-react";

interface MobileMenuProps {
    isMobileMenu: boolean;
    handleMobileMenu: () => void;
}

export default function MobileMenu({ isMobileMenu, handleMobileMenu }: MobileMenuProps) {
    const { data: session, status } = useSession();
    const [openSubMenus, setOpenSubMenus] = useState<{ [key: string]: boolean }>({});
    const pathname = usePathname();

    useEffect(() => {
        if (isMobileMenu) {
            handleMobileMenu();
        }
    }, [pathname]);

    useEffect(() => {
        const handleResize = () => {
            if (window.innerWidth > 991) {
                setOpenSubMenus({});
            }
        };
        window.addEventListener("resize", handleResize);
        return () => window.removeEventListener("resize", handleResize);
    }, []);

    const handleToggleSubMenu = (key: string) => {
        setOpenSubMenus((prev) => ({
            ...prev,
            [key]: !prev[key],
        }));
    };

    const isHashNav = (href: string) => href === "#";

    return (
        <>
            {isMobileMenu && <div className="mobile-menu-overlay" onClick={handleMobileMenu} />}
            {/*=====Mobile header start=======*/}
            <div className={`mobile-sidebar d-block d-lg-none ${isMobileMenu ? "mobile-menu-active" : ""}`}>
                <div className="logo-m">
                    <Link href="/" className="flex items-center justify-center">
                        <img
                            src="assets/img/logo/logo_full.svg"
                            alt="Motshwanelo IT Consulting"
                            className="h-12 w-auto filter brightness-0 invert"
                        />
                    </Link>
                </div>
                <button
                    className="menu-close flex items-center justify-center w-10 h-10 rounded-full bg-white/10 hover:bg-white/20 transition-all duration-300 absolute top-6 right-6"
                    onClick={handleMobileMenu}
                    aria-label="Close mobile menu"
                >
                    <i className="fa-solid fa-xmark text-white text-xl" />
                </button>
                <div className="mobile-nav">
                    <ul>
                        <li>
                            <Link
                                href="/"
                                className={`flex items-center gap-3 p-4 rounded-lg transition-all duration-300 ${pathname === "/" ? "bg-orange-500/20 text-orange-400" : "text-white/90 hover:bg-white/10"}`}
                                onClick={handleMobileMenu}
                            >
                                <i className="fa-solid fa-home text-lg"></i>
                                Home
                            </Link>
                        </li>
                        <li>
                            <Link
                                href="/about"
                                className={`flex items-center gap-3 p-4 rounded-lg transition-all duration-300 ${pathname === "/about" ? "bg-orange-500/20 text-orange-400" : "text-white/90 hover:bg-white/10"}`}
                                onClick={handleMobileMenu}
                            >
                                <i className="fa-solid fa-users text-lg"></i>
                                About Us
                            </Link>
                        </li>
                        <li className="has-dropdown">
                            <div className="menu-item-with-toggle">
                                <Link
                                    href="/service"
                                    className={`flex items-center gap-3 p-4 rounded-lg transition-all duration-300 ${pathname.startsWith("/service") ? "bg-orange-500/20 text-orange-400" : "text-white/90 hover:bg-white/10"}`}
                                >
                                    <i className="fa-solid fa-cogs text-lg"></i>
                                    Services
                                </Link>
                                <span className={`submenu-button${openSubMenus["service"] ? " submenu-opened" : ""}`} onClick={() => handleToggleSubMenu("service")}>
                                    <em />
                                </span>
                            </div>
                            <ul className="sub-menu" style={{ display: openSubMenus["service"] ? "block" : "none" }}>
                                <li>
                                    <Link href="/service" onClick={handleMobileMenu}>
                                        <i className="fa-solid fa-list mr-2"></i>
                                        All Services
                                    </Link>
                                </li>
                                <li>
                                    <Link href="/services/data-centre" onClick={handleMobileMenu}>
                                        <i className="fa-solid fa-server mr-2"></i>
                                        Data Centre Solutions
                                    </Link>
                                </li>
                                <li>
                                    <Link href="/services/smart-city" onClick={handleMobileMenu}>
                                        <i className="fa-solid fa-city mr-2"></i>
                                        Smart City Solutions
                                    </Link>
                                </li>
                                <li>
                                    <Link href="/services/it-consulting" onClick={handleMobileMenu}>
                                        <i className="fa-solid fa-laptop-code mr-2"></i>
                                        IT Consulting
                                    </Link>
                                </li>
                                <li>
                                    <Link href="/services/software-development" onClick={handleMobileMenu}>
                                        <i className="fa-solid fa-code mr-2"></i>
                                        Software Development
                                    </Link>
                                </li>
                                <li>
                                    <Link href="/services/training" onClick={handleMobileMenu}>
                                        <i className="fa-solid fa-graduation-cap mr-2"></i>
                                        Training & Support
                                    </Link>
                                </li>

                            </ul>
                        </li>
                        <li className="has-dropdown">
                            <div className="menu-item-with-toggle">
                                <Link
                                    href="/solutions"
                                    className={`flex items-center gap-3 p-4 rounded-lg transition-all duration-300 ${pathname.startsWith("/solutions") ? "bg-orange-500/20 text-orange-400" : "text-white/90 hover:bg-white/10"}`}
                                >
                                    <i className="fa-solid fa-lightbulb text-lg"></i>
                                    Solutions
                                </Link>
                                <span className={`submenu-button${openSubMenus["solutions"] ? " submenu-opened" : ""}`} onClick={() => handleToggleSubMenu("solutions")}>
                                    <em />
                                </span>
                            </div>
                            <ul className="sub-menu" style={{ display: openSubMenus["solutions"] ? "block" : "none" }}>
                                <li>
                                    <Link href="/solutions/digim" onClick={handleMobileMenu}>
                                        <i className="fa-solid fa-digital-tachograph mr-2"></i>
                                        DIGIM Platform
                                    </Link>
                                </li>
                                <li>
                                    <Link href="/solutions/neteco" onClick={handleMobileMenu}>
                                        <i className="fa-solid fa-network-wired mr-2"></i>
                                        NetEco Solutions
                                    </Link>
                                </li>
                                <li>
                                    <Link href="/solutions/fusion-module" onClick={handleMobileMenu}>
                                        <i className="fa-solid fa-cube mr-2"></i>
                                        FusionModule
                                    </Link>
                                </li>
                                <li>
                                    <Link href="/solutions/ups" onClick={handleMobileMenu}>
                                        <i className="fa-solid fa-battery-full mr-2"></i>
                                        UPS Solutions
                                    </Link>
                                </li>
                            </ul>
                        </li>
                        <li className="has-dropdown">
                            <div className="menu-item-with-toggle">
                                <Link
                                    href="/project"
                                    className={`flex items-center gap-3 p-4 rounded-lg transition-all duration-300 ${pathname.startsWith("/project") ? "bg-orange-500/20 text-orange-400" : "text-white/90 hover:bg-white/10"}`}
                                >
                                    <i className="fa-solid fa-briefcase text-lg"></i>
                                    Projects
                                </Link>
                                <span className={`submenu-button${openSubMenus["projects"] ? " submenu-opened" : ""}`} onClick={() => handleToggleSubMenu("projects")}>
                                    <em />
                                </span>
                            </div>
                            <ul className="sub-menu" style={{ display: openSubMenus["projects"] ? "block" : "none" }}>
                                <li>
                                    <Link href="/project" onClick={handleMobileMenu}>
                                        <i className="fa-solid fa-folder-open mr-2"></i>
                                        All Projects
                                    </Link>
                                </li>
                                <li>
                                    <Link href="/enhanced-projects" onClick={handleMobileMenu}>
                                        <i className="fa-solid fa-star mr-2"></i>
                                        Enhanced Projects
                                    </Link>
                                </li>
                                <li>
                                    <Link href="/success-stories" onClick={handleMobileMenu}>
                                        <i className="fa-solid fa-trophy mr-2"></i>
                                        Success Stories
                                    </Link>
                                </li>
                            </ul>
                        </li>
                        <li className="has-dropdown">
                            <div className="menu-item-with-toggle">
                                <Link
                                    href="/blog"
                                    className={`flex items-center gap-3 p-4 rounded-lg transition-all duration-300 ${pathname.startsWith("/blog") || pathname.startsWith("/case-studies") || pathname.startsWith("/whitepapers") ? "bg-orange-500/20 text-orange-400" : "text-white/90 hover:bg-white/10"}`}
                                >
                                    <i className="fa-solid fa-book text-lg"></i>
                                    Resources
                                </Link>
                                <span className={`submenu-button${openSubMenus["resources"] ? " submenu-opened" : ""}`} onClick={() => handleToggleSubMenu("resources")}>
                                    <em />
                                </span>
                            </div>
                            <ul className="sub-menu" style={{ display: openSubMenus["resources"] ? "block" : "none" }}>
                                <li>
                                    <Link href="/blog" onClick={handleMobileMenu}>
                                        <i className="fa-solid fa-newspaper mr-2"></i>
                                        Blog & News
                                    </Link>
                                </li>
                                <li>
                                    <Link href="/case-studies" onClick={handleMobileMenu}>
                                        <i className="fa-solid fa-chart-line mr-2"></i>
                                        Case Studies
                                    </Link>
                                </li>
                                <li>
                                    <Link href="/whitepapers" onClick={handleMobileMenu}>
                                        <i className="fa-solid fa-file-alt mr-2"></i>
                                        Whitepapers
                                    </Link>
                                </li>
                                <li>
                                    <Link href="/technology-showcase" onClick={handleMobileMenu}>
                                        <i className="fa-solid fa-display mr-2"></i>
                                        Technology Showcase
                                    </Link>
                                </li>
                            </ul>
                        </li>
                        <li>
                            <Link
                                href="/contact"
                                className={`flex items-center gap-3 p-4 rounded-lg transition-all duration-300 ${pathname === "/contact" ? "bg-orange-500/20 text-orange-400" : "text-white/90 hover:bg-white/10"}`}
                                onClick={handleMobileMenu}
                            >
                                <i className="fa-solid fa-envelope text-lg"></i>
                                Contact
                            </Link>
                        </li>
                        <li className="has-dropdown">
                            <div className="menu-item-with-toggle">
                                <Link href="#" onClick={(e) => e.preventDefault()}>
                                    Pages
                                </Link>
                                <span className={`submenu-button${openSubMenus["pages"] ? " submenu-opened" : ""}`} onClick={() => handleToggleSubMenu("pages")}>
                                    <em />
                                </span>
                            </div>
                            <ul className="sub-menu" style={{ display: openSubMenus["pages"] ? "block" : "none" }}>
                                <li>
                                    <Link href="/contact">Contact</Link>
                                </li>
                                <li>
                                    <Link href="/team">Team</Link>
                                </li>
                                <li>
                                    <Link href="/testimonial">Testimonial</Link>
                                </li>
                                <li>
                                    <Link href="/error">404</Link>
                                </li>
                            </ul>
                        </li>
                        <li className="has-dropdown">
                            <div className="menu-item-with-toggle">
                                <Link href="#" onClick={(e) => e.preventDefault()}>
                                    Blog
                                </Link>
                                <span className={`submenu-button${openSubMenus["blog"] ? " submenu-opened" : ""}`} onClick={() => handleToggleSubMenu("blog")}>
                                    <em />
                                </span>
                            </div>
                            <ul className="sub-menu" style={{ display: openSubMenus["blog"] ? "block" : "none" }}>
                                <li>
                                    <Link href="/blog">Blog</Link>
                                </li>
                                <li>
                                    <Link href="/blog-details-left">Details Left</Link>
                                </li>
                                <li>
                                    <Link href="/blog-details-right">Details Right</Link>
                                </li>
                                <li>
                                    <Link href="/blog-details">Blog Details</Link>
                                </li>
                            </ul>
                        </li>
                        <li className="has-dropdown">
                            <div className="menu-item-with-toggle">
                                <Link href="#" onClick={(e) => e.preventDefault()}>
                                    Project
                                </Link>
                                <span className={`submenu-button${openSubMenus["project"] ? " submenu-opened" : ""}`} onClick={() => handleToggleSubMenu("project")}>
                                    <em />
                                </span>
                            </div>
                            <ul className="sub-menu" style={{ display: openSubMenus["project"] ? "block" : "none" }}>
                                <li>
                                    <Link href="/project">Project</Link>
                                </li>
                                <li>
                                    <Link href="/project-details-left">Project Left</Link>
                                </li>
                                <li>
                                    <Link href="/project-details-right">Project Right</Link>
                                </li>
                                <li>
                                    <Link href="/project-details">Project Details</Link>
                                </li>
                            </ul>
                        </li>
                    </ul>
                    
                    {/* Authentication Section */}
                    <div className="mobile-auth-section" style={{ padding: "20px 0", borderTop: "1px solid #eee", borderBottom: "1px solid #eee", margin: "20px 0" }}>
                        {status === "loading" ? (
                            <div className="flex items-center justify-center gap-2">
                                <div className="w-4 h-4 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin"></div>
                                <span className="text-sm text-gray-600">Loading...</span>
                            </div>
                        ) : !session ? (
                            <div className="flex flex-col gap-3">
                                <h4 style={{ margin: "0 0 10px 0", fontSize: "16px", fontWeight: "600" }}>Account</h4>
                                <div className="flex gap-2">
                                    <Button asChild variant="outline" size="sm" className="flex-1">
                                        <Link href="/auth/signin">
                                            <LogIn className="w-4 h-4 mr-2" />
                                            Sign In
                                        </Link>
                                    </Button>
                                    <Button asChild variant="default" size="sm" className="flex-1">
                                        <Link href="/auth/signup">
                                            <User className="w-4 h-4 mr-2" />
                                            Sign Up
                                        </Link>
                                    </Button>
                                </div>
                            </div>
                        ) : (
                            <div className="flex flex-col gap-3">
                                <div className="flex items-center gap-3">
                                    <div className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center text-sm font-medium">
                                        {session.user.name
                                            ? session.user.name.split(" ").map(n => n[0]).join("").toUpperCase()
                                            : session.user.email?.[0].toUpperCase()}
                                    </div>
                                    <div className="flex-1">
                                        <p className="text-sm font-medium">{session.user.name || "User"}</p>
                                        <p className="text-xs text-gray-600">{session.user.email}</p>
                                        <Badge variant="outline" className="text-xs mt-1">
                                            <Shield className="w-3 h-3 mr-1" />
                                            {session.user.role}
                                        </Badge>
                                    </div>
                                </div>
                                <div className="flex flex-col gap-2">
                                    {session.user.role === "ADMIN" && (
                                        <Button asChild variant="default" size="sm" className="w-full">
                                            <Link href="/admin">
                                                <Settings className="w-4 h-4 mr-2" />
                                                Admin Panel
                                            </Link>
                                        </Button>
                                    )}
                                    <Button 
                                        variant="outline" 
                                        size="sm" 
                                        className="w-full"
                                        onClick={() => {
                                            signOut({ callbackUrl: "/auth/signin" });
                                            handleMobileMenu();
                                        }}
                                    >
                                        <LogOut className="w-4 h-4 mr-2" />
                                        Sign Out
                                    </Button>
                                </div>
                            </div>
                        )}
                    </div>

                    <div className="mobile-button">
                        <Link className="theme-btn1" href="service">
                            Learn More
                            <span>
                                <i className="fa-solid fa-arrow-right" />
                            </span>
                        </Link>
                    </div>
                    <div className="single-footer-items">
                        <h3>Contact Us</h3>
                        <div className="contact-box">
                            <div className="icon">
                                <img src="assets/img/icons/footer-icon1.png" alt="" />
                            </div>
                            <div className="pera">
                                <Link href="tel:+880123456789">+880 123 456 789</Link>
                            </div>
                        </div>
                        <div className="contact-box">
                            <div className="icon">
                                <img src="assets/img/icons/footer-icon2.png" alt="" />
                            </div>
                            <div className="pera">
                                <Link href="mailto:<EMAIL>"><EMAIL></Link>
                            </div>
                        </div>
                        <div className="contact-box">
                            <div className="icon">
                                <img src="assets/img/icons/footer-icon3.png" alt="" />
                            </div>
                            <div className="pera">
                                <Link href="tel:+880123456789">
                                    8502 Preston Rd. <br /> Inglewoo Maine 98380
                                </Link>
                            </div>
                        </div>
                    </div>
                    <div className="contact-infos">
                        <h3>Our Location</h3>
                        <ul className="social-icon">
                            <li>
                                <Link href="#">
                                    <i className="fa-brands fa-linkedin-in" />
                                </Link>
                            </li>
                            <li>
                                <Link href="#">
                                    <i className="fa-brands fa-x-twitter" />
                                </Link>
                            </li>
                            <li>
                                <Link href="#">
                                    <i className="fa-brands fa-youtube" />
                                </Link>
                            </li>
                            <li>
                                <Link href="#">
                                    <i className="fa-brands fa-instagram" />
                                </Link>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </>
    );
}
