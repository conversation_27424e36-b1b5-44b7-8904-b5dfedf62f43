"use client"

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { getPage, updatePage } from '@/utils/appwrite';
import DragDropEditor from '@/lib/page-builder/DragDropEditor';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import { useSidebar } from '@/components/ui/sidebar';
import { useAdminHeader } from '@/hooks/use-admin-header';

export default function PageBuilderPage() {
  const params = useParams();
  const router = useRouter();
  const pageId = params.id as string;

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pageData, setPageData] = useState<{
    title: string;
    blocks: string[];
  } | null>(null);

  const { open, setOpen } = useSidebar();
  const { show, hide, setTitle } = useAdminHeader();

  // Fetch page data on component mount
  useEffect(() => {
    const fetchPageData = async () => {
      if (!pageId) return;

      try {
        setLoading(true);
        const page = await getPage(pageId);

        setPageData({
          title: page?.title,
          blocks: page?.blocks || [],
        });
      } catch (err) {
        console.error('Error fetching page:', err);
        setError('Failed to load page data. Please try again.');
      } finally {
        setLoading(false);
        //Hide Menu and Header
        hide();
        setOpen(false);
      }
    };

    fetchPageData();
  }, [pageId]);

  // Handle saving the page
  const handleSavePage = async (blockIds: string[]) => {
    if (!pageId || !pageData) return;

    try {
      await updatePage(pageId, {
        ...pageData,
        blocks: blockIds,
      });

      toast.success('Page saved successfully');
    } catch (err) {
      console.error('Error saving page:', err);
      toast.error('Failed to save page. Please try again.');
    }
  };

  // Handle navigating back to pages list
  const handleBackToPages = () => {
    router.push('/admin/pages');
    setOpen(true);
    show();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">Loading page builder...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-8">
        <div className="max-w-4xl mx-auto">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
            <h2 className="text-lg font-medium text-red-800 mb-2">Error</h2>
            <p className="text-red-700">{error}</p>
            <Button
              onClick={handleBackToPages}
              variant="outline"
              className="mt-4"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Pages
            </Button>
          </div>
        </div>
      </div>
    );
  }

  if (!pageData) {
    return (
      <div className="p-8 text-center">
        <p>No page data found</p>
        <Button
          onClick={handleBackToPages}
          variant="outline"
          className="mt-4"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Pages
        </Button>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col">
      <DragDropEditor
        pageId={pageId}
        initialBlocks={pageData.blocks}
        onSave={handleSavePage}
      />
    </div>
  );
}