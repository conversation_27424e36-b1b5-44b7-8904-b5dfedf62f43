import EnhancedProjectSection from "@/components/sections/project/EnhancedProjectSection";
import PageBanner from "@/components/elements/PageBanner";

export const metadata = {
  title: "Enhanced Projects Showcase | Motshwanelo IT Consulting",
  description: "Explore our success stories and project achievements with an enhanced interactive showcase featuring real client results and testimonials.",
};

export default function EnhancedProjectsPage() {
  return (
    <>
      <PageBanner
        pageTitle="Enhanced Projects Showcase"
        breadTitle="Success Stories & Projects"
        description="Discover how we've transformed businesses across Africa with innovative technology solutions. Real projects, real results, real impact."
        type="1"
      />
      
      <EnhancedProjectSection />
      
      <section className="sp" style={{ backgroundColor: "#f8f9fa" }}>
        <div className="container">
          <div className="row">
            <div className="col-lg-8 m-auto text-center">
              <div className="heading1">
                <span className="span" data-aos="zoom-in-left" data-aos-duration={700}>
                  Our Impact
                </span>
                <h2 className="text-anime-style-3">Transforming Africa's Digital Landscape</h2>
                <div className="space16" />
                <p data-aos="fade-up" data-aos-duration={800}>
                  With over 17 years of experience, we've successfully delivered innovative IT solutions 
                  that have transformed businesses, secured cities, and empowered communities across Africa.
                </p>
              </div>
            </div>
          </div>
          
          <div className="space60" />
          
          {/* Impact Statistics */}
          <div className="row">
            <div className="col-lg-3 col-md-6">
              <div className="stat-item text-center" data-aos="fade-up" data-aos-duration={700}>
                <div className="stat-number" style={{
                  fontSize: "48px",
                  fontWeight: "700",
                  color: "#03276e",
                  marginBottom: "10px"
                }}>
                  50+
                </div>
                <h5 style={{ color: "#03276e", marginBottom: "10px" }}>Projects Completed</h5>
                <p style={{ color: "#666", fontSize: "14px" }}>
                  Successfully delivered across various industries and sectors
                </p>
              </div>
            </div>
            <div className="col-lg-3 col-md-6">
              <div className="stat-item text-center" data-aos="fade-up" data-aos-duration={800}>
                <div className="stat-number" style={{
                  fontSize: "48px",
                  fontWeight: "700",
                  color: "#e89d1a",
                  marginBottom: "10px"
                }}>
                  95%
                </div>
                <h5 style={{ color: "#e89d1a", marginBottom: "10px" }}>Client Satisfaction</h5>
                <p style={{ color: "#666", fontSize: "14px" }}>
                  Consistently exceeding client expectations and requirements
                </p>
              </div>
            </div>
            <div className="col-lg-3 col-md-6">
              <div className="stat-item text-center" data-aos="fade-up" data-aos-duration={900}>
                <div className="stat-number" style={{
                  fontSize: "48px",
                  fontWeight: "700",
                  color: "#030376",
                  marginBottom: "10px"
                }}>
                  17+
                </div>
                <h5 style={{ color: "#030376", marginBottom: "10px" }}>Years Experience</h5>
                <p style={{ color: "#666", fontSize: "14px" }}>
                  Proven track record since 2007 in IT consulting and solutions
                </p>
              </div>
            </div>
            <div className="col-lg-3 col-md-6">
              <div className="stat-item text-center" data-aos="fade-up" data-aos-duration={1000}>
                <div className="stat-number" style={{
                  fontSize: "48px",
                  fontWeight: "700",
                  color: "#6F6F87",
                  marginBottom: "10px"
                }}>
                  500+
                </div>
                <h5 style={{ color: "#6F6F87", marginBottom: "10px" }}>People Trained</h5>
                <p style={{ color: "#666", fontSize: "14px" }}>
                  Graduates and professionals empowered through our training programs
                </p>
              </div>
            </div>
          </div>

          <div className="space60" />

          {/* Call to Action */}
          <div className="row">
            <div className="col-lg-8 m-auto text-center">
              <div className="cta-section" style={{
                background: "linear-gradient(135deg, #03276e, #030376)",
                padding: "40px",
                borderRadius: "20px",
                color: "white"
              }}>
                <h3 style={{ color: "white", marginBottom: "20px" }}>
                  Ready to Transform Your Business?
                </h3>
                <p style={{ color: "rgba(255,255,255,0.9)", marginBottom: "30px" }}>
                  Join our growing list of successful clients and experience the difference 
                  that innovative IT solutions can make for your organization.
                </p>
                <div style={{ display: "flex", gap: "15px", justifyContent: "center", flexWrap: "wrap" }}>
                  <a 
                    href="/contact" 
                    className="btn"
                    style={{
                      background: "#e89d1a",
                      color: "white",
                      padding: "12px 30px",
                      borderRadius: "25px",
                      textDecoration: "none",
                      fontWeight: "600",
                      transition: "all 0.3s ease"
                    }}
                  >
                    Start Your Project
                  </a>
                  <a 
                    href="/services" 
                    className="btn"
                    style={{
                      background: "transparent",
                      color: "white",
                      padding: "12px 30px",
                      borderRadius: "25px",
                      textDecoration: "none",
                      fontWeight: "600",
                      border: "2px solid white",
                      transition: "all 0.3s ease"
                    }}
                  >
                    Explore Services
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
}