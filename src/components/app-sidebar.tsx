"use client"

import * as React from "react"
import {
  IconCamera,
  IconChartBar,
  IconDashboard,
  IconDatabase,
  IconFileDescription,
  IconHelp,
  IconListDetails,
  IconReport,
  IconSearch,
  IconSettings,
  IconUsers,
  IconPlus,
  IconEdit,
  IconEye,
  IconTemplate,
  IconSeo,
  IconAnalyze,
  IconWorldWww,
  IconMenu2,
  IconTags,
  IconCategory,
  IconPhoto,
  IconVideo,
  IconFiles,
  IconMessage,
  IconMail,
  IconBell,
  IconShield,
  IconDatabase as IconBackup,
  IconCloud,
  IconCode,
  IconPalette,
  IconLayout,
  IconComponents,
  IconPlugConnected,
  IconRocket,
  IconTrendingUp,
  IconActivity,
  IconCalendar,
  IconHistory,
  IconShare,
  IconLink,
  IconExternalLink,
  IconDeviceDesktop,
  IconLanguage,
  IconGlobe,
  IconLock,
  IconKey,
  IconUserCheck,
  IconUserPlus,
  IconUserCog,
  IconUsers as IconTeam,
  IconApi,
  IconWebhook,
  IconTerminal,
  IconReportAnalytics,
  IconChartLine,
  IconChartPie,
  IconTarget,
  IconFilter,
  IconTable,
  IconList,
  IconGrid,
  IconLayoutGrid,
  IconApps,
  IconTool,
  IconAdjustments,
  IconForms,
  IconBrandGoogle,
  IconBrandTwitter,
  IconBrandFacebook,
  IconBrandInstagram,
  IconBrandLinkedin,
  IconBrandYoutube,
  IconBrandGithub
} from "@tabler/icons-react"

import { NavDocuments } from "@/components/nav-documents"
import { NavMain } from "@/components/nav-main"
import { NavSecondary } from "@/components/nav-secondary"
import { NavUser } from "@/components/nav-user"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"
import LogoIcon from "@/components/LogoIcon"
import { useSidebar } from "@/components/ui/sidebar"
import Link from "next/link"

const data = {
  navMain: [
    {
      title: "Dashboard",
      url: "/admin",
      icon: IconDashboard,
      badge: "Overview",
    },
    {
      title: "Pages",
      url: "/admin/pages",
      icon: IconFileDescription,
      badge: "New",
      items: [
        {
          title: "All Pages",
          url: "/admin/pages",
          icon: IconList,
          description: "Manage all pages"
        },
        {
          title: "Create New",
          url: "/admin/pages/new",
          icon: IconPlus,
          description: "Create a new page"
        },
        {
          title: "Templates",
          url: "/admin/pages/templates",
          icon: IconTemplate,
          description: "Page templates"
        },
        {
          title: "Drafts",
          url: "/admin/pages?status=draft",
          icon: IconEdit,
          description: "Draft pages"
        },
        {
          title: "Published",
          url: "/admin/pages?status=published",
          icon: IconEye,
          description: "Published pages"
        },
      ],
    },
    {
      title: "Blog",
      url: "/admin/blog",
      icon: IconReport,
      items: [
        {
          title: "All Posts",
          url: "/admin/blog",
          icon: IconList,
          description: "Manage blog posts"
        },
        {
          title: "Create Post",
          url: "/admin/blog/new",
          icon: IconPlus,
          description: "Write new post"
        },
        {
          title: "Categories",
          url: "/admin/blog/categories",
          icon: IconCategory,
          description: "Manage categories"
        },
        {
          title: "Tags",
          url: "/admin/blog/tags",
          icon: IconTags,
          description: "Manage tags"
        },
        {
          title: "Comments",
          url: "/admin/blog/comments",
          icon: IconMessage,
          description: "Moderate comments"
        },
      ],
    },
    {
      title: "Media Library",
      url: "/admin/media",
      icon: IconCamera,
      items: [
        {
          title: "All Media",
          url: "/admin/media",
          icon: IconFiles,
          description: "Browse all files"
        },
        {
          title: "Images",
          url: "/admin/media?type=image",
          icon: IconPhoto,
          description: "Image files"
        },
        {
          title: "Videos",
          url: "/admin/media?type=video",
          icon: IconVideo,
          description: "Video files"
        },
        {
          title: "Upload",
          url: "/admin/media/upload",
          icon: IconPlus,
          description: "Upload new files"
        },
      ],
    },
    {
      title: "Navigation",
      url: "/admin/navigation",
      icon: IconMenu2,
      items: [
        {
          title: "Menus",
          url: "/admin/navigation",
          icon: IconListDetails,
          description: "Manage menus"
        },
        {
          title: "Menu Items",
          url: "/admin/navigation/items",
          icon: IconLink,
          description: "Menu items"
        },
        {
          title: "Footer Links",
          url: "/admin/navigation/footer",
          icon: IconExternalLink,
          description: "Footer navigation"
        },
      ],
    },
  ],
  navContent: [
    {
      title: "Content Management",
      items: [
        {
          name: "Forms",
          url: "/admin/forms",
          icon: IconForms,
          description: "Contact & custom forms"
        },
        {
          name: "Components",
          url: "/admin/components",
          icon: IconComponents,
          description: "Reusable components"
        },
        {
          name: "Layouts",
          url: "/admin/layouts",
          icon: IconLayout,
          description: "Page layouts"
        },
        {
          name: "Themes",
          url: "/admin/themes",
          icon: IconPalette,
          description: "Site themes"
        },
      ]
    }
  ],
  navAnalytics: [
    {
      title: "Analytics & SEO",
      items: [
        {
          name: "Site Analytics",
          url: "/admin/analytics",
          icon: IconChartBar,
          description: "Traffic & engagement"
        },
        {
          name: "SEO Tools",
          url: "/admin/seo",
          icon: IconSeo,
          description: "Search optimization"
        },
        {
          name: "Performance",
          url: "/admin/performance",
          icon: IconRocket,
          description: "Site performance"
        },
        {
          name: "Search Console",
          url: "/admin/search-console",
          icon: IconBrandGoogle,
          description: "Google Search Console"
        },
        {
          name: "Social Media",
          url: "/admin/social",
          icon: IconShare,
          description: "Social media integration"
        },
      ]
    }
  ],
  navTools: [
    {
      title: "Tools & Utilities",
      items: [
        {
          name: "Backups",
          url: "/admin/backups",
          icon: IconBackup,
          description: "Site backups"
        },
        {
          name: "Import/Export",
          url: "/admin/import-export",
          icon: IconDatabase,
          description: "Data management"
        },
        {
          name: "API Keys",
          url: "/admin/api-keys",
          icon: IconKey,
          description: "API management"
        },
        {
          name: "Webhooks",
          url: "/admin/webhooks",
          icon: IconWebhook,
          description: "Webhook management"
        },
        {
          name: "Logs",
          url: "/admin/logs",
          icon: IconTerminal,
          description: "System logs"
        },
      ]
    }
  ],
  navSecondary: [
    {
      title: "User Management",
      url: "/admin/users",
      icon: IconUsers,
      items: [
        {
          title: "All Users",
          url: "/admin/users",
          icon: IconTeam,
          description: "Manage users"
        },
        {
          title: "Add User",
          url: "/admin/users/new",
          icon: IconUserPlus,
          description: "Create new user"
        },
        {
          title: "Roles & Permissions",
          url: "/admin/users/roles",
          icon: IconShield,
          description: "User permissions"
        },
        {
          title: "Profile Settings",
          url: "/admin/users/profile",
          icon: IconUserCog,
          description: "User profiles"
        },
      ],
    },
    {
      title: "Site Settings",
      url: "/admin/settings",
      icon: IconSettings,
      items: [
        {
          title: "General",
          url: "/admin/settings",
          icon: IconAdjustments,
          description: "General settings"
        },
        {
          title: "Appearance",
          url: "/admin/settings/appearance",
          icon: IconPalette,
          description: "Site appearance"
        },
        {
          title: "Email",
          url: "/admin/settings/email",
          icon: IconMail,
          description: "Email configuration"
        },
        {
          title: "Security",
          url: "/admin/settings/security",
          icon: IconLock,
          description: "Security settings"
        },
        {
          title: "Integrations",
          url: "/admin/settings/integrations",
          icon: IconPlugConnected,
          description: "Third-party integrations"
        },
        {
          title: "Header Editor",
          url: "/admin/header-editor",
          icon: IconLayout,
          description: "Customize site header"
        },
      ],
    },
    {
      title: "Help & Support",
      url: "/admin/help",
      icon: IconHelp,
      items: [
        {
          title: "Documentation",
          url: "/admin/help",
          icon: IconHelp,
          description: "User guide"
        },
        {
          title: "System Status",
          url: "/admin/status",
          icon: IconActivity,
          description: "System health"
        },
        {
          title: "Updates",
          url: "/admin/updates",
          icon: IconRocket,
          description: "System updates"
        },
        {
          title: "Support",
          url: "/admin/support",
          icon: IconMail,
          description: "Get support"
        },
      ],
    },
  ],
}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {

  const { 
    open
  } = useSidebar()

  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <div className="flex items-center">
            <a href="#" className="flex items-center gap-2">
              <LogoIcon
                size={36}
              />
              {open && (
              <div className="flex flex-col gap-0.5">
                <h2 className="text-base font-semibold leading-snug">
                  Motshwanelo
                </h2>
                <p className="text-sm font-normal text-gray-500 leading-snug">
                  IT Consulting
                </p>
              </div>
              )}
            </a>
          </div>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
        <NavDocuments items={data.navContent} />
        <NavDocuments items={data.navAnalytics} />
        <NavDocuments items={data.navTools} />
        <NavSecondary items={data.navSecondary} className="mt-auto" />
      </SidebarContent>
      <SidebarFooter>
        <NavUser />
      </SidebarFooter>
    </Sidebar>
  )
}
