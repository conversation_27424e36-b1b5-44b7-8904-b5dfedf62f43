import dynamic from "next/dynamic";
import TechStackSection from "@/components/sections/home/<USER>";

// Import existing home sections
const Section1 = dynamic(() => import("@/components/sections/home/<USER>"));
const Section2 = dynamic(() => import("@/components/sections/home/<USER>"));
const Section3 = dynamic(() => import("@/components/sections/home/<USER>"));
const Section4 = dynamic(() => import("@/components/sections/home/<USER>"));
const Section5 = dynamic(() => import("@/components/sections/home/<USER>"));
const Section6 = dynamic(() => import("@/components/sections/home/<USER>"));
const Section7 = dynamic(() => import("@/components/sections/home/<USER>"));
const Section8 = dynamic(() => import("@/components/sections/home/<USER>"));
const Section9 = dynamic(() => import("@/components/sections/home/<USER>"));

export const metadata = {
  title: "Home | Motshwanelo IT Consulting",
  description: "Motshwanelo IT Consulting: Transforming African Infrastructure with Innovative Technology Solutions",
};

export default function HomeWithTech() {
  return (
    <>
      <Section1 />
      <Section2 />
      <Section3 />
      <Section4 />
      {/* Add our new Technology Stack section here */}
      <TechStackSection />
      <Section5 />
      <Section6 />
      <Section7 />
      <Section8 />
      <Section9 />
    </>
  );
}