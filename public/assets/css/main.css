/*
============================
Name:  Recrute - Staffing Agency HTML Template
Version: 2.0.0
Description:
Author:  VikingLab
Author URI: https://themeforest.net/user/vikinglab/portfolio
Location:
============================
*/
/*

CSS LIST

=<::::::::::::::::::::::::::=>
  HEADER AREA CSS
=<::::::::::::::::::::::::::=>
  NAV MENU AREA CSS
=<::::::::::::::::::::::::::=>
  WELCOME AREA CSS
=<::::::::::::::::::::::::::=>
  ABOUT AREA CSS
=<::::::::::::::::::::::::::=>
  SERVICE AREA CSS
=<::::::::::::::::::::::::::=>
  ACCOUNT AREA CSS
=<::::::::::::::::::::::::::=>
  FEATURES AREA CSS
=<::::::::::::::::::::::::::=>
  BRAND AREA CSS
=<::::::::::::::::::::::::::=>
  CHOOSE AREA CSS
=<::::::::::::::::::::::::::=>
  PRRELOADER AREA CSS
=<::::::::::::::::::::::::::=>
  PRICING AREA CSS
=<::::::::::::::::::::::::::=>
  TEAM AREA CSS
=<::::::::::::::::::::::::::=>
  TESTIMONIAL AREA CSS
=<::::::::::::::::::::::::::=>
  WORK AREA CSS
=<::::::::::::::::::::::::::=>
  OTHERS AREA CSS
=<::::::::::::::::::::::::::=>
  CONMMON AREA CSS
=<::::::::::::::::::::::::::=>
  BLOG AREA CSS
=<::::::::::::::::::::::::::=>
  CTA AREA CSS
=<::::::::::::::::::::::::::=>
 ANIMATION AREA CSS
=<::::::::::::::::::::::::::=>
  BUTTONS AREA CSS
=<::::::::::::::::::::::::::=>
  TYPOGRAPHY AREA CSS
=<::::::::::::::::::::::::::=>
  FOOTER AREA CSS
=<::::::::::::::::::::::::::=>
*/
/*
::::::::::::::::::::::::::
 TYPOGRAPHY AREA CSS
::::::::::::::::::::::::::
*/
@import url("https://fonts.googleapis.com/css2?family=Figtree:ital,wght@0,300..900;1,300..900&display=swap");
/*
 ::::::::::::::::::::::::::
  ANIMATION AREA CSS
 ::::::::::::::::::::::::::
 */
@keyframes shape-animaiton1 {
    0% {
        transform: translateY(0);
    }
    100% {
        transform: translateY(100px);
    }
}
@keyframes shape-animaiton2 {
    0% {
        transform: translateY(0);
    }
    100% {
        transform: translateY(30px);
    }
}
@keyframes shape-animaiton3 {
    0% {
        transform: translateY(0);
    }
    100% {
        transform: translateY(10px);
    }
}
@keyframes shape-animaiton4 {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(1000deg);
    }
}
@keyframes animate1 {
    0% {
        transform: translateY(0px);
    }
    100% {
        transform: translateY(-20px);
    }
}
@keyframes animate2 {
    0% {
        transform: translateY(0px);
    }
    100% {
        transform: translateY(20px);
    }
}
@keyframes animate3 {
    0% {
        transform: translateY(0px);
    }
    100% {
        transform: translateY(15px);
    }
}
@keyframes animate4 {
    0% {
        transform: translateY(0px);
    }
    100% {
        transform: translateY(-25px);
    }
}
@keyframes animate5 {
    0% {
        transform: scale(0.5);
    }
    100% {
        transform: scale(1);
    }
}
.shape-animaiton1 {
    position: relative;
    animation-name: shape-animaiton1;
    animation-duration: 2s;
    animation-iteration-count: infinite;
    animation-direction: alternate;
    animation-timing-function: cubic-bezier(0.59, 0.59, 1, 1);
}

.shape-animaiton2 {
    position: relative;
    animation-name: shape-animaiton2;
    animation-duration: 2s;
    animation-iteration-count: infinite;
    animation-direction: alternate;
    animation-timing-function: ease-in-out;
}

.shape-animaiton3 {
    position: relative;
    animation-name: shape-animaiton3;
    animation-duration: 2s;
    animation-iteration-count: infinite;
    animation-direction: alternate;
    animation-timing-function: ease-in-out;
}

.shape-animaiton4 {
    position: relative;
    animation-name: shape-animaiton4;
    animation-duration: 62s;
    animation-iteration-count: infinite;
    animation-direction: alternate;
    animation-timing-function: linear;
}

.animate1 {
    position: relative;
    animation-name: animate1;
    animation-duration: 2s;
    animation-iteration-count: infinite;
    animation-direction: alternate;
    animation-timing-function: ease-in-out;
}

.animate2 {
    position: relative;
    animation-name: animate2;
    animation-duration: 2s;
    animation-iteration-count: infinite;
    animation-direction: alternate;
    animation-timing-function: ease-in-out;
}

.animate3 {
    position: relative;
    animation-name: animate3;
    animation-duration: 4s;
    animation-iteration-count: infinite;
    animation-direction: alternate;
    animation-timing-function: ease-in-out;
}

.animate4 {
    position: relative;
    animation-name: animate4;
    animation-duration: 4s;
    animation-iteration-count: infinite;
    animation-direction: alternate;
    animation-timing-function: ease-in-out;
}

.animate5 {
    position: relative;
    animation-name: animate5;
    animation-duration: 4s;
    animation-iteration-count: infinite;
    animation-direction: alternate;
    animation-timing-function: ease-in-out;
}

/*
::::::::::::::::::::::::::
ANIMATION AREA CSS
::::::::::::::::::::::::::
*/
body.body,
html {
    overflow-x: hidden;
}

body {
    font-family: "Figtree", sans-serif;
    font-size: var(--f-fs-font-fs16);
}

.body2 {
    background-image: url(../img/bg/home-page2-bg.png);
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
    z-index: -33;
}

@media screen and (min-width: 769px) {
    .body.unic-body {
        overflow-x: initial !important;
    }
}
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

input,
textarea,
select,
option {
    max-width: 100%;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    padding: 0;
    margin: 0;
}

ul {
    padding: 0;
    margin: 0;
    list-style: none;
}

p {
    padding: 0;
    margin: 0;
}

img {
    max-width: 100%;
    max-height: 100%;
}

a,
a:hover,
a:focus {
    outline: none;
    text-decoration: none;
}

body.body {
    font-size: 16px;
    font-weight: 400;
    line-height: 26px;
    overflow-x: hidden;
    z-index: -33;
    position: relative;
}

.sp {
    padding: 100px 0px;
}

.sp2 {
    padding: 120px 0px;
}

.pt100 {
    padding-top: 100px;
}

.pt140 {
    padding-top: 140px;
}

.pb120 {
    padding-bottom: 120px !important;
}

.pl60 {
    padding-left: 60px;
}

.pb40 {
    padding-bottom: 40px;
}

.pr60 {
    padding-right: 60px;
}

@media (max-width: 768px) {
    .sp {
        padding: 50px 0px;
    }
    .pt100 {
        padding-top: 50px;
    }
    .pt140 {
        padding-top: 70px;
    }
    .pb120 {
        padding-bottom: 60px !important;
    }
    .sp2 {
        padding: 60px 0px;
    }
    .pl60 {
        padding-left: 0;
    }
    .pr60 {
        padding-right: 0;
    }
}
.space4 {
    height: 4px;
}

.space6 {
    height: 6px;
}

.space28 {
    height: 28px;
}

.space8 {
    height: 8px;
}

.space12 {
    height: 12px;
}

.space14 {
    height: 14px;
}

.space16 {
    height: 16px;
}

.space24 {
    height: 24px;
}

.space32 {
    height: 32px;
}

.space10 {
    height: 10px;
}

.space5 {
    height: 5px;
}

.space5 {
    height: 5px;
}

@media (max-width: 767px) {
    .space10 {
        height: 5px;
    }
    .sp5 {
        padding-bottom: 50px;
    }
}
.space20 {
    height: 20px;
}

@media (max-width: 767px) {
    .space20 {
        height: 10px;
    }
}
.space30 {
    height: 30px;
}

@media (max-width: 767px) {
    .space30 {
        height: 15px;
    }
}
.space40 {
    height: 40px;
}

@media (max-width: 767px) {
    .space40 {
        height: 20px;
    }
}
.space50 {
    height: 50px;
}

.space45 {
    height: 45px;
}

@media (max-width: 767px) {
    .space50 {
        height: 25px;
    }
}
.space60 {
    height: 60px;
}

.space55 {
    height: 55px;
}

.space70 {
    height: 70px;
}

.space80 {
    height: 80px;
}

.space90 {
    height: 90px;
}

.space100 {
    height: 100px;
}

.space120 {
    height: 120px;
}

.text-right {
    text-align: right;
}

.section-padding {
    padding: 120px 0;
}

@media (max-width: 991px) {
    .section-padding {
        padding: 60px 0;
    }
}
.section-padding2 {
    padding: 120px 0 90px;
}

@media (max-width: 991px) {
    .section-padding2 {
        padding: 60px 0 30px;
    }
}
.padding-bottom {
    padding-bottom: 120px;
}

@media (max-width: 991px) {
    .padding-bottom {
        padding-bottom: 60px;
    }
}
.padding-bottom2 {
    padding-bottom: 90px;
}

@media (max-width: 991px) {
    .padding-bottom2 {
        padding-bottom: 30px;
    }
}
.padding-top {
    padding-top: 120px;
}

@media (max-width: 991px) {
    .padding-top {
        padding-top: 60px;
    }
}
.padding-top2 {
    padding-top: 90px;
}

@media (max-width: 991px) {
    .padding-top2 {
        padding-top: 30px;
    }
}
.padding-90 {
    padding: 90px 0;
}

@media (max-width: 991px) {
    .padding-90 {
        padding: 50px 0;
    }
}
.w-full {
    width: 100%;
}

@media (min-width: 992px) {
    .w-lg-full {
        width: 100%;
    }
}
.text-center {
    text-align: center;
}

@media (min-width: 992px) {
    .text-lg-center {
        text-align: center;
    }
}
.text-left {
    text-align: left !important;
}

.weight-400 {
    font-weight: 400 !important;
}

.weight-500 {
    font-weight: 500 !important;
}

.weight-600 {
    font-weight: 600 !important;
}

.weight-700 {
    font-weight: 700 !important;
}

.weight-800 {
    font-weight: 800 !important;
}

.weight-900 {
    font-weight: 900 !important;
}

.font-f-1 {
    font-family: "Hind", sans-serif !important;
}

.font-f-2 {
    font-family: "Plus Jakarta Sans", sans-serif !important;
}

.font-f-3 {
    font-family: "Spline Sans", sans-serif !important;
}

.font-f-4 {
    font-family: "Catamaran", sans-serif !important;
}

.font-12 {
    font-size: 12px;
}

.font-14 {
    font-size: 14px;
}

.font-16 {
    font-size: 16px;
}

.font-18 {
    font-size: 18px;
}

.font-20 {
    font-size: 20px;
}

.font-22 {
    font-size: 22px;
}

.font-24 {
    font-size: 24px;
}

.font-26 {
    font-size: 26px;
}

.font-28 {
    font-size: 28px;
}

.font-30 {
    font-size: 30px;
}

.font-32 {
    font-size: 32px;
}

.font-34 {
    font-size: 34px;
}

.font-36 {
    font-size: 36px;
}

.font-40 {
    font-size: 40px;
}

.font-42 {
    font-size: 42px;
}

.font-44 {
    font-size: 44px;
}

.font-46 {
    font-size: 48px;
}

.font-48 {
    font-size: 48px;
}

.font-50 {
    font-size: 60px;
}

.font-52 {
    font-size: 52px;
}

.font-54 {
    font-size: 54px;
}

.font-56 {
    font-size: 56px;
}

.font-58 {
    font-size: 58px;
}

.font-60 {
    font-size: 60px;
}

.font-62 {
    font-size: 62px;
}

.font-70 {
    font-size: 70px;
}

.font-72 {
    font-size: 72px;
}

.font-74 {
    font-size: 74px;
}

.font-76 {
    font-size: 76px;
}

.font-78 {
    font-size: 78px;
}

.font-80 {
    font-size: 80px;
}

.font-82 {
    font-size: 82px;
}

.font-84 {
    font-size: 84px;
}

.font-86 {
    font-size: 86px;
}

.font-88 {
    font-size: 88px;
}

.font-90 {
    font-size: 90px;
}

.font-92 {
    font-size: 92px;
}

.font-94 {
    font-size: 94px;
}

.font-96 {
    font-size: 96px;
}

.font-98 {
    font-size: 98px;
}

.font-100 {
    font-size: 100px;
}

@media screen and (min-width: 1024px) {
    .text-md-right {
        text-align: right;
    }
    .text-md-center {
        text-align: center;
    }
    .text-md-left {
        text-align: left;
    }
    .font-lg-12 {
        font-size: 12px;
    }
    .font-lg-14 {
        font-size: 14px;
    }
    .font-lg-16 {
        font-size: 16px;
    }
    .font-lg-18 {
        font-size: 18px;
    }
    .font-lg-20 {
        font-size: 20px;
    }
    .font-lg-22 {
        font-size: 22px;
    }
    .font-lg-24 {
        font-size: 24px;
    }
    .font-lg-26 {
        font-size: 26px;
    }
    .font-lg-28 {
        font-size: 28px;
    }
    .font-lg-30 {
        font-size: 30px;
    }
    .font-lg-32 {
        font-size: 32px;
    }
    .font-lg-34 {
        font-size: 34px;
    }
    .font-lg-36 {
        font-size: 36px;
    }
    .font-lg-40 {
        font-size: 40px;
    }
    .font-lg-42 {
        font-size: 42px;
    }
    .font-lg-44 {
        font-size: 44px;
    }
    .font-lg-45 {
        font-size: 45px;
    }
    .font-lg-46 {
        font-size: 48px;
    }
    .font-lg-48 {
        font-size: 48px;
    }
    .font-lg-50 {
        font-size: 60px;
    }
    .font-lg-52 {
        font-size: 52px;
    }
    .font-lg-54 {
        font-size: 54px;
    }
    .font-lg-56 {
        font-size: 56px;
    }
    .font-lg-58 {
        font-size: 58px;
    }
    .font-lg-60 {
        font-size: 60px;
    }
    .font-lg-62 {
        font-size: 62px;
    }
    .font-lg-66 {
        font-size: 66px;
    }
    .font-lg-70 {
        font-size: 70px;
    }
    .font-lg-72 {
        font-size: 72px;
    }
    .font-lg-74 {
        font-size: 74px;
    }
    .font-lg-76 {
        font-size: 76px;
    }
    .font-lg-78 {
        font-size: 78px;
    }
    .font-lg-80 {
        font-size: 80px;
    }
    .font-lg-82 {
        font-size: 82px;
    }
    .font-lg-84 {
        font-size: 84px;
    }
    .font-lg-86 {
        font-size: 86px;
    }
    .font-lg-88 {
        font-size: 88px;
    }
    .font-lg-90 {
        font-size: 90px;
    }
    .font-lg-92 {
        font-size: 92px;
    }
    .font-lg-94 {
        font-size: 94px;
    }
    .font-lg-96 {
        font-size: 96px;
    }
    .font-lg-98 {
        font-size: 98px;
    }
    .font-lg-100 {
        font-size: 100px;
    }
    .line-height-lg-14 {
        line-height: 14px;
    }
    .line-height-lg-16 {
        line-height: 16px;
    }
    .line-height-lg-18 {
        line-height: 18px;
    }
    .line-height-lg-20 {
        line-height: 20px;
    }
    .line-height-lg-22 {
        line-height: 22px;
    }
    .line-height-lg-24 {
        line-height: 24px;
    }
    .line-height-lg-26 {
        line-height: 26px;
    }
    .line-height-lg-28 {
        line-height: 28px;
    }
    .line-height-lg-30 {
        line-height: 30px;
    }
    .line-height-lg-32 {
        line-height: 32px;
    }
    .line-height-lg-34 {
        line-height: 34px;
    }
    .line-height-lg-36 {
        line-height: 36px;
    }
    .line-height-lg-38 {
        line-height: 38px;
    }
    .line-height-lg-40 {
        line-height: 40px;
    }
    .line-height-lg-42 {
        line-height: 42px;
    }
    .line-height-lg-44 {
        line-height: 44px;
    }
    .line-height-lg-48 {
        line-height: 48px;
    }
    .line-height-lg-50 {
        line-height: 50px;
    }
    .line-height-lg-52 {
        line-height: 52px;
    }
    .line-height-lg-54 {
        line-height: 54px;
    }
    .line-height-lg-56 {
        line-height: 56px;
    }
    .line-height-lg-58 {
        line-height: 58px;
    }
    .line-height-lg-60 {
        line-height: 60px;
    }
    .line-height-lg-62 {
        line-height: 62px;
    }
    .line-height-lg-64 {
        line-height: 64px;
    }
    .line-height-lg-66 {
        line-height: 66px;
    }
    .line-height-lg-68 {
        line-height: 68px;
    }
    .line-height-lg-70 {
        line-height: 70px;
    }
    .line-height-lg-72 {
        line-height: 72px;
    }
    .line-height-lg-74 {
        line-height: 74px;
    }
    .line-height-lg-76 {
        line-height: 76px;
    }
    .line-height-lg-78 {
        line-height: 78px;
    }
    .line-height-lg-80 {
        line-height: 80px;
    }
    .line-height-lg-82 {
        line-height: 82px;
    }
    .line-height-lg-84 {
        line-height: 84px;
    }
    .line-height-lg-86 {
        line-height: 86px;
    }
    .line-height-lg-88 {
        line-height: 88px;
    }
    .line-height-lg-90 {
        line-height: 90px;
    }
    .line-height-lg-92 {
        line-height: 92px;
    }
    .line-height-lg-94 {
        line-height: 94px;
    }
    .line-height-lg-96 {
        line-height: 96px;
    }
    .line-height-lg-98 {
        line-height: 98px;
    }
    .line-height-lg-100 {
        line-height: 100px;
    }
}
@media screen and (min-width: 768px) {
    .font-md-12 {
        font-size: 12px;
    }
    .font-md-14 {
        font-size: 14px;
    }
    .font-md-16 {
        font-size: 16px;
    }
    .font-md-18 {
        font-size: 18px;
    }
    .font-md-20 {
        font-size: 20px;
    }
    .font-md-22 {
        font-size: 22px;
    }
    .font-md-24 {
        font-size: 24px;
    }
    .font-md-26 {
        font-size: 26px;
    }
    .font-md-28 {
        font-size: 28px;
    }
    .font-md-30 {
        font-size: 30px;
    }
    .font-md-32 {
        font-size: 32px;
    }
    .font-md-34 {
        font-size: 34px;
    }
    .font-md-36 {
        font-size: 36px;
    }
    .font-md-40 {
        font-size: 40px;
    }
    .font-md-42 {
        font-size: 42px;
    }
    .font-md-44 {
        font-size: 44px;
    }
    .font-md-46 {
        font-size: 48px;
    }
    .font-md-48 {
        font-size: 48px;
    }
    .font-md-50 {
        font-size: 60px;
    }
    .font-md-52 {
        font-size: 52px;
    }
    .font-md-54 {
        font-size: 54px;
    }
    .font-md-56 {
        font-size: 56px;
    }
    .font-md-58 {
        font-size: 58px;
    }
    .font-md-60 {
        font-size: 60px;
    }
    .font-md-62 {
        font-size: 62px;
    }
    .font-md-74 {
        font-size: 74px;
    }
    .font-md-76 {
        font-size: 76px;
    }
    .font-md-78 {
        font-size: 78px;
    }
    .font-md-80 {
        font-size: 80px;
    }
    .font-md-82 {
        font-size: 82px;
    }
    .font-md-84 {
        font-size: 84px;
    }
    .font-md-86 {
        font-size: 86px;
    }
    .font-md-88 {
        font-size: 88px;
    }
    .font-md-90 {
        font-size: 90px;
    }
    .font-md-92 {
        font-size: 92px;
    }
    .font-md-94 {
        font-size: 94px;
    }
    .font-md-96 {
        font-size: 96px;
    }
    .font-md-98 {
        font-size: 98px;
    }
    .font-md-100 {
        font-size: 100px;
    }
    .line-height-md-12 {
        line-height: 12px;
    }
    .line-height-md-14 {
        line-height: 14px;
    }
    .line-height-md-16 {
        line-height: 16px;
    }
    .line-height-md-18 {
        line-height: 18px;
    }
    .line-height-md-20 {
        line-height: 20px;
    }
    .line-height-md-22 {
        line-height: 22px;
    }
    .line-height-md-24 {
        line-height: 24px;
    }
    .line-height-md-26 {
        line-height: 26px;
    }
    .line-height-md-28 {
        line-height: 28px;
    }
    .line-height-md-30 {
        line-height: 30px;
    }
    .line-height-md-32 {
        line-height: 32px;
    }
    .line-height-md-34 {
        line-height: 34px;
    }
    .line-height-md-36 {
        line-height: 36px;
    }
    .line-height-md-38 {
        line-height: 38px;
    }
    .line-height-md-40 {
        line-height: 40px;
    }
    .line-height-md-42 {
        line-height: 42px;
    }
    .line-height-md-44 {
        line-height: 44px;
    }
    .line-height-md-48 {
        line-height: 48px;
    }
    .line-height-md-50 {
        line-height: 50px;
    }
    .line-height-md-52 {
        line-height: 52px;
    }
    .line-height-md-54 {
        line-height: 54px;
    }
    .line-height-md-56 {
        line-height: 56px;
    }
    .line-height-md-58 {
        line-height: 58px;
    }
    .line-height-md-60 {
        line-height: 60px;
    }
    .line-height-md-62 {
        line-height: 62px;
    }
    .line-height-md-64 {
        line-height: 64px;
    }
    .line-height-md-66 {
        line-height: 66px;
    }
    .line-height-md-68 {
        line-height: 68px;
    }
    .line-height-md-70 {
        line-height: 70px;
    }
    .line-height-md-72 {
        line-height: 72px;
    }
    .line-height-md-74 {
        line-height: 74px;
    }
    .line-height-md-76 {
        line-height: 76px;
    }
    .line-height-md-78 {
        line-height: 78px;
    }
    .line-height-md-80 {
        line-height: 80px;
    }
    .line-height-md-82 {
        line-height: 82px;
    }
    .line-height-md-84 {
        line-height: 84px;
    }
    .line-height-md-86 {
        line-height: 86px;
    }
    .line-height-md-88 {
        line-height: 88px;
    }
    .line-height-md-90 {
        line-height: 90px;
    }
    .line-height-md-92 {
        line-height: 92px;
    }
    .line-height-md-94 {
        line-height: 94px;
    }
    .line-height-md-96 {
        line-height: 96px;
    }
    .line-height-md-98 {
        line-height: 98px;
    }
    .line-height-md-100 {
        line-height: 100px;
    }
}
@media screen and (min-width: 576px) {
    .font-sm-12 {
        font-size: 12px;
    }
    .font-sm-14 {
        font-size: 14px;
    }
    .font-sm-16 {
        font-size: 16px;
    }
    .font-sm-18 {
        font-size: 18px;
    }
    .font-sm-20 {
        font-size: 20px;
    }
    .font-sm-22 {
        font-size: 22px;
    }
    .font-sm-24 {
        font-size: 24px;
    }
    .font-sm-26 {
        font-size: 26px;
    }
    .font-sm-28 {
        font-size: 28px;
    }
    .font-sm-30 {
        font-size: 30px;
    }
    .font-sm-32 {
        font-size: 32px;
    }
    .font-sm-34 {
        font-size: 34px;
    }
    .font-sm-36 {
        font-size: 36px;
    }
    .font-sm-40 {
        font-size: 40px;
    }
    .font-sm-42 {
        font-size: 42px;
    }
    .font-sm-44 {
        font-size: 44px;
    }
    .font-sm-46 {
        font-size: 48px;
    }
    .font-sm-48 {
        font-size: 48px;
    }
    .font-sm-50 {
        font-size: 60px;
    }
    .font-sm-52 {
        font-size: 52px;
    }
    .font-sm-54 {
        font-size: 54px;
    }
    .font-sm-56 {
        font-size: 56px;
    }
    .font-sm-58 {
        font-size: 58px;
    }
    .font-sm-60 {
        font-size: 60px;
    }
    .font-sm-62 {
        font-size: 62px;
    }
    .font-sm-74 {
        font-size: 74px;
    }
    .font-sm-76 {
        font-size: 76px;
    }
    .font-sm-78 {
        font-size: 78px;
    }
    .font-sm-80 {
        font-size: 80px;
    }
    .font-sm-82 {
        font-size: 82px;
    }
    .font-sm-84 {
        font-size: 84px;
    }
    .font-sm-86 {
        font-size: 86px;
    }
    .font-sm-88 {
        font-size: 88px;
    }
    .font-sm-90 {
        font-size: 90px;
    }
    .font-sm-92 {
        font-size: 92px;
    }
    .font-sm-94 {
        font-size: 94px;
    }
    .font-sm-96 {
        font-size: 96px;
    }
    .font-sm-98 {
        font-size: 98px;
    }
    .font-sm-100 {
        font-size: 100px;
    }
    .line-height-sm-12 {
        line-height: 12px;
    }
    .line-height-sm-14 {
        line-height: 14px;
    }
    .line-height-sm-16 {
        line-height: 16px;
    }
    .line-height-sm-18 {
        line-height: 18px;
    }
    .line-height-sm-20 {
        line-height: 20px;
    }
    .line-height-sm-22 {
        line-height: 22px;
    }
    .line-height-sm-24 {
        line-height: 24px;
    }
    .line-height-sm-26 {
        line-height: 26px;
    }
    .line-height-sm-28 {
        line-height: 28px;
    }
    .line-height-sm-30 {
        line-height: 30px;
    }
    .line-height-sm-32 {
        line-height: 32px;
    }
    .line-height-sm-34 {
        line-height: 34px;
    }
    .line-height-sm-36 {
        line-height: 36px;
    }
    .line-height-sm-38 {
        line-height: 38px;
    }
    .line-height-sm-40 {
        line-height: 40px;
    }
    .line-height-sm-42 {
        line-height: 42px;
    }
    .line-height-sm-44 {
        line-height: 44px;
    }
    .line-height-sm-48 {
        line-height: 48px;
    }
    .line-height-sm-50 {
        line-height: 50px;
    }
    .line-height-sm-52 {
        line-height: 52px;
    }
    .line-height-sm-54 {
        line-height: 54px;
    }
    .line-height-sm-56 {
        line-height: 56px;
    }
    .line-height-sm-58 {
        line-height: 58px;
    }
    .line-height-sm-60 {
        line-height: 60px;
    }
    .line-height-sm-62 {
        line-height: 62px;
    }
    .line-height-sm-64 {
        line-height: 64px;
    }
    .line-height-sm-66 {
        line-height: 66px;
    }
    .line-height-sm-68 {
        line-height: 68px;
    }
    .line-height-sm-70 {
        line-height: 70px;
    }
    .line-height-sm-72 {
        line-height: 72px;
    }
    .line-height-sm-74 {
        line-height: 74px;
    }
    .line-height-sm-76 {
        line-height: 76px;
    }
    .line-height-sm-78 {
        line-height: 78px;
    }
    .line-height-sm-80 {
        line-height: 80px;
    }
    .line-height-sm-82 {
        line-height: 82px;
    }
    .line-height-sm-84 {
        line-height: 84px;
    }
    .line-height-sm-86 {
        line-height: 86px;
    }
    .line-height-sm-88 {
        line-height: 88px;
    }
    .line-height-sm-90 {
        line-height: 90px;
    }
    .line-height-sm-92 {
        line-height: 92px;
    }
    .line-height-sm-94 {
        line-height: 94px;
    }
    .line-height-sm-96 {
        line-height: 96px;
    }
    .line-height-sm-98 {
        line-height: 98px;
    }
    .line-height-sm-100 {
        line-height: 100px;
    }
}
.mr-2 {
    margin-right: 8px;
}

@media (max-width: 767px) {
    .mobile-hidden {
        display: none;
    }
}
.mb-30 {
    margin-bottom: 30px;
}

.mobile-sidebar .single-footer h3 {
    color: #ffffff;
}

.mobile-sidebar .single-contact a {
    color: #ffffff;
}

.lg-ml-15 {
    margin-left: 15px;
}

@media (max-width: 767px) {
    .lg-ml-15 {
        margin-left: 0;
    }
}
.lg-mr-15 {
    margin-right: 15px;
}

@media (max-width: 767px) {
    .lg-mr-15 {
        margin-right: 0;
    }
}
._relative {
    position: relative;
}

._absolute {
    position: absolute;
}

.bg-cover {
    background-size: cover;
    background-position: center center;
}

.bg-contain {
    background-size: contain;
    background-position: center center;
}

.img-cover img,
.img-cover {
    -o-object-fit: cover;
    object-fit: cover;
}

.width100 img {
    width: 100%;
}

/*
 ::::::::::::::::::::::::::
  fonts area css
 ::::::::::::::::::::::::::
 */
:root {
    --vtc-text-heading-text-1: #081120;
    --vtc-text-heading-text-2: #19326a;
    --vtc-text-heading-text-3: #001431;
    --vtc-text-pera-text-1: #77787b;
    --vtc-text-pera-text-2: #ccccd5;
    --vtc-text-pera-text-3: #5a5f6a;
    --vtc-text-text-white-text-1: #ffffff;
    --vtc-bg-main-bg-1: #010E5D;
    --vtc-bg-main-bg-2: #fc253f;
    --vtc-bg-main-bg-3: #fd965b;
    --vtc-bg-main-bg-4: #23342e;
    --vtc-bg-main-bg-5: #19326a;
    --vtc-bg-main-bg-6: #52b5e9;
    --vtc-bg-common-bg1: #fff2e6;
    --vtc-bg-common-bg2: #f5f3f4;
    --vtc-bg-common-bg3: #28284d;
    --vtc-bg-common-bg4: #fffaec;
    --vtc-bg-common-bg5: #fff4ef;
    --vtc-bg-common-bg6: #e9ebea;
    --vtc-bg-common-bg7: #ecf3f6;
    --vtc-bg-common-bg8: #e5e7ea;
    --vtc-bg-common-bg9: #f2f4f7;
    --vtc-bg-bg-white: #ffffff;
    --vtc-border-border-1: #f0f0f0;
    --vtc-border-border-2: #dfdcdc;
    --f-fw-regular: 400;
    --f-fw-medium: 500;
    --f-fw-semibold: 600;
    --f-fw-bold: 700;
    --f-fw-ex-bold: 800;
    --f-ff-font-1: "Figtree", sans-serif;
    --f-fs-font-fs16: 16px;
    --f-fs-font-fs18: 18px;
    --f-fs-font-fs20: 20px;
    --f-fs-font-fs22: 22px;
    --f-fs-font-fs24: 24px;
    --f-fs-font-fs26: 26px;
    --f-fs-font-fs28: 28px;
    --f-fs-font-fs30: 30px;
    --f-fs-font-fs32: 32px;
    --f-fs-font-fs34: 34px;
    --f-fs-font-fs36: 36px;
    --f-fs-font-fs40: 40px;
    --f-fs-font-fs42: 42px;
    --f-fs-font-fs44: 44px;
    --f-fs-font-fs48: 48px;
    --f-fs-font-fs50: 50px;
    --f-fs-font-fs52: 52px;
    --f-fs-font-fs54: 54px;
    --f-fs-font-fs56: 56px;
    --f-fs-font-fs58: 58px;
    --f-fs-font-fs60: 60px;
    --f-fs-font-fs62: 62px;
    --f-fs-font-fs64: 64px;
    --f-fs-font-fs66: 66px;
    --f-fs-font-fs68: 68px;
    --f-fs-font-fs70: 70px;
    --f-fs-font-fs72: 72px;
    --f-fs-font-fs74: 74px;
    --f-fs-font-fs76: 76px;
    --f-fs-font-fs78: 78px;
    --f-fs-font-fs80: 80px;
    --f-fs-font-fs82: 82px;
    --f-fs-font-fs84: 84px;
    --f-fs-font-fs86: 86px;
    --f-fs-font-fs88: 88px;
}

/*
::::::::::::::::::::::::::
 COMMON AREA CSS
::::::::::::::::::::::::::
*/
.heading1-w span.span {
    display: inline-block;
    color: var(--vtc-text-text-white-text-1);
    font-size: var(--f-fs-font-fs16);
    line-height: var(--f-fs-font-fs16);
    font-weight: var(--f-fw-medium);
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.1764705882);
    padding: 8px 12px;
    margin-bottom: 16px;
}
.heading1-w h2 {
    font-size: var(--f-fs-font-fs44);
    line-height: var(--f-fs-font-fs54);
    color: var(--vtc-text-text-white-text-1);
    font-weight: var(--f-fw-bold);
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .heading1-w h2 {
        font-size: var(--f-fs-font-fs32);
        line-height: var(--f-fs-font-fs40);
    }
}
@media (max-width: 767px) {
    .heading1-w h2 {
        font-size: var(--f-fs-font-fs32);
        line-height: var(--f-fs-font-fs40);
    }
}
.heading1-w p {
    color: var(--vtc-text-pera-text-2);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-medium);
}
.heading1-w h4 a {
    display: inline-block;
    font-size: var(--f-fs-font-fs20);
    line-height: var(--f-fs-font-fs20);
    font-weight: var(--f-fw-bold);
    color: var(--vtc-text-text-white-text-1);
}

.heading1 span.span {
    display: inline-block;
    color: var(--vtc-text-heading-text-1);
    font-size: var(--f-fs-font-fs16);
    line-height: var(--f-fs-font-fs16);
    font-weight: var(--f-fw-medium);
    border-radius: 4px;
    background-color: rgba(255, 124, 1, 0.1568627451);
    padding: 8px 12px;
    margin-bottom: 16px;
}
.heading1 h5 a {
    display: inline-block;
    font-size: var(--f-fs-font-fs20);
    line-height: var(--f-fs-font-fs20);
    color: var(--vtc-text-heading-text-1);
    font-weight: var(--f-fw-bold);
    transition: all 0.4s;
}
.heading1 h5 a:hover {
    color: var(--vtc-bg-main-bg-1);
    transition: all 0.4s;
}
.heading1 h2 {
    font-size: var(--f-fs-font-fs44);
    line-height: var(--f-fs-font-fs54);
    color: var(--vtc-text-heading-text-1);
    font-weight: var(--f-fw-bold);
    transition: all 0.4s;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .heading1 h2 {
        font-size: var(--f-fs-font-fs32);
        line-height: var(--f-fs-font-fs40);
    }
}
@media (max-width: 767px) {
    .heading1 h2 {
        font-size: var(--f-fs-font-fs32);
        line-height: var(--f-fs-font-fs40);
    }
}
.heading1 h3 {
    font-size: var(--f-fs-font-fs32);
    line-height: var(--f-fs-font-fs32);
    color: var(--vtc-text-heading-text-1);
    font-weight: var(--f-fw-bold);
    transition: all 0.4s;
}
.heading1 p {
    color: var(--vtc-text-pera-text-1);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-medium);
    transition: all 0.4s;
}
.heading1 h4 a {
    font-size: var(--f-fs-font-fs20);
    line-height: var(--f-fs-font-fs20);
    font-weight: var(--f-fw-bold);
    color: var(--vtc-text-heading-text-1);
    display: inline-block;
    transition: all 0.4s;
}

.heading2 span.span {
    display: inline-block;
    color: var(--vtc-text-text-white-text-1);
    font-size: var(--f-fs-font-fs16);
    line-height: var(--f-fs-font-fs16);
    font-weight: var(--f-fw-medium);
    border-radius: 4px;
    background-color: var(--vtc-bg-common-bg3);
    padding: 8px 12px;
    margin-bottom: 16px;
}
.heading2 h2 {
    font-size: var(--f-fs-font-fs44);
    line-height: var(--f-fs-font-fs54);
    color: var(--vtc-text-text-white-text-1);
    font-weight: var(--f-fw-bold);
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .heading2 h2 {
        font-size: var(--f-fs-font-fs32);
        line-height: var(--f-fs-font-fs40);
    }
}
@media (max-width: 767px) {
    .heading2 h2 {
        font-size: var(--f-fs-font-fs32);
        line-height: var(--f-fs-font-fs40);
    }
}
.heading2 h3 {
    font-size: var(--f-fs-font-fs32);
    line-height: var(--f-fs-font-fs32);
    color: var(--vtc-text-text-white-text-1);
    font-weight: var(--f-fw-bold);
    transition: all 0.4s;
}
.heading2 h5 a {
    display: inline-block;
    font-size: var(--f-fs-font-fs20);
    line-height: var(--f-fs-font-fs20);
    color: var(--vtc-text-text-white-text-1);
    font-weight: var(--f-fw-bold);
    transition: all 0.4s;
}
.heading2 h5 a:hover {
    color: var(--vtc-bg-main-bg-2);
    transition: all 0.4s;
}
.heading2 h4 a {
    display: inline-block;
    font-size: var(--f-fs-font-fs24);
    line-height: var(--f-fs-font-fs24);
    color: var(--vtc-text-text-white-text-1);
    font-weight: var(--f-fw-bold);
    transition: all 0.4s;
}
.heading2 p {
    color: var(--vtc-text-pera-text-2);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-medium);
    transition: all 0.4s;
}

.heading3 span.span {
    display: inline-block;
    color: var(--vtc-text-heading-text-1);
    font-size: var(--f-fs-font-fs16);
    line-height: var(--f-fs-font-fs16);
    font-weight: var(--f-fw-medium);
    border-radius: 4px;
    background-color: var(--vtc-bg-common-bg6);
    padding: 8px 12px;
    margin-bottom: 16px;
}
.heading3 h2 {
    font-size: var(--f-fs-font-fs44);
    line-height: var(--f-fs-font-fs54);
    color: var(--vtc-text-heading-text-1);
    font-weight: var(--f-fw-bold);
    transition: all 0.4s;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .heading3 h2 {
        font-size: var(--f-fs-font-fs32);
        line-height: var(--f-fs-font-fs40);
    }
}
@media (max-width: 767px) {
    .heading3 h2 {
        font-size: var(--f-fs-font-fs32);
        line-height: var(--f-fs-font-fs40);
    }
}
.heading3 p {
    color: var(--vtc-text-pera-text-1);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-medium);
}
.heading3 h4 a {
    display: inline-block;
    font-size: var(--f-fs-font-fs24);
    line-height: var(--f-fs-font-fs24);
    color: var(--vtc-text-heading-text-1);
    font-weight: var(--f-fw-bold);
    transition: all 0.4s;
}
.heading3 h4 a:hover {
    color: var(--vtc-bg-main-bg-3);
    transition: all 0.4s;
}
.heading3 h5 a {
    display: inline-block;
    font-size: var(--f-fs-font-fs20);
    line-height: var(--f-fs-font-fs20);
    color: var(--vtc-text-heading-text-1);
    font-weight: var(--f-fw-bold);
    transition: all 0.4s;
}
.heading3 h5 a:hover {
    color: var(--vtc-bg-main-bg-3);
    transition: all 0.4s;
}

.heading3-w span.span {
    display: inline-block;
    color: var(--vtc-bg-bg-white);
    font-size: var(--f-fs-font-fs16);
    line-height: var(--f-fs-font-fs16);
    font-weight: var(--f-fw-medium);
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.2549019608);
    padding: 8px 12px;
    margin-bottom: 16px;
}
.heading3-w h2 {
    font-size: var(--f-fs-font-fs44);
    line-height: var(--f-fs-font-fs54);
    color: var(--vtc-bg-bg-white);
    font-weight: var(--f-fw-bold);
    transition: all 0.4s;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .heading3-w h2 {
        font-size: var(--f-fs-font-fs32);
        line-height: var(--f-fs-font-fs40);
    }
}
@media (max-width: 767px) {
    .heading3-w h2 {
        font-size: var(--f-fs-font-fs32);
        line-height: var(--f-fs-font-fs40);
    }
}
.heading3-w p {
    color: var(--vtc-text-pera-text-2);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-medium);
}
.heading3-w h5 {
    display: inline-block;
    font-size: var(--f-fs-font-fs24);
    line-height: var(--f-fs-font-fs24);
    color: var(--vtc-bg-bg-white);
    font-weight: var(--f-fw-bold);
    transition: all 0.4s;
}
.heading3-w h4 a {
    display: inline-block;
    font-size: var(--f-fs-font-fs20);
    line-height: var(--f-fs-font-fs20);
    color: var(--vtc-bg-bg-white);
    font-weight: var(--f-fw-bold);
    transition: all 0.4s;
}
.heading3-w h4 a:hover {
    color: var(--vtc-bg-main-bg-3);
    transition: all 0.4s;
}

.heading4 span.span {
    display: inline-block;
    color: var(--vtc-text-heading-text-2);
    font-size: var(--f-fs-font-fs16);
    line-height: var(--f-fs-font-fs16);
    font-weight: var(--f-fw-medium);
    border-radius: 4px;
    background-color: var(--vtc-bg-common-bg7);
    padding: 8px 12px;
    margin-bottom: 16px;
}
.heading4 span.span2 {
    display: inline-block;
    color: var(--vtc-text-heading-text-2);
    font-size: var(--f-fs-font-fs16);
    line-height: var(--f-fs-font-fs16);
    font-weight: var(--f-fw-medium);
    border-radius: 4px;
    background-color: var(--vtc-bg-bg-white);
    padding: 8px 12px;
    margin-bottom: 16px;
}
.heading4 h2 {
    font-size: var(--f-fs-font-fs44);
    line-height: var(--f-fs-font-fs54);
    color: var(--vtc-text-heading-text-2);
    font-weight: var(--f-fw-bold);
    transition: all 0.4s;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .heading4 h2 {
        font-size: var(--f-fs-font-fs32);
        line-height: var(--f-fs-font-fs40);
    }
}
@media (max-width: 767px) {
    .heading4 h2 {
        font-size: var(--f-fs-font-fs32);
        line-height: var(--f-fs-font-fs40);
    }
}
.heading4 h5 a {
    display: inline-block;
    font-size: var(--f-fs-font-fs20);
    line-height: var(--f-fs-font-fs20);
    color: var(--vtc-text-heading-text-2);
    font-weight: var(--f-fw-bold);
    transition: all 0.4s;
}
.heading4 h5 a:hover {
    transition: all 0.4s;
    color: var(--vtc-bg-main-bg-6);
}
.heading4 h4 a {
    display: inline-block;
    font-size: var(--f-fs-font-fs24);
    line-height: var(--f-fs-font-fs24);
    color: var(--vtc-text-heading-text-2);
    font-weight: var(--f-fw-bold);
    transition: all 0.4s;
}
.heading4 h4 a:hover {
    transition: all 0.4s;
    color: var(--vtc-bg-main-bg-6);
}
.heading4 p {
    color: var(--vtc-text-pera-text-3);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-medium);
}

.heading4-w span.span {
    display: inline-block;
    color: var(--vtc-text-text-white-text-1);
    font-size: var(--f-fs-font-fs16);
    line-height: var(--f-fs-font-fs16);
    font-weight: var(--f-fw-medium);
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.1843137255);
    padding: 8px 12px;
    margin-bottom: 16px;
}
.heading4-w h2 {
    font-size: var(--f-fs-font-fs44);
    line-height: var(--f-fs-font-fs54);
    color: var(--vtc-text-text-white-text-1);
    font-weight: var(--f-fw-bold);
    transition: all 0.4s;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .heading4-w h2 {
        font-size: var(--f-fs-font-fs32);
        line-height: var(--f-fs-font-fs40);
    }
}
@media (max-width: 767px) {
    .heading4-w h2 {
        font-size: var(--f-fs-font-fs32);
        line-height: var(--f-fs-font-fs40);
    }
}
.heading4-w h5 a {
    display: inline-block;
    font-size: var(--f-fs-font-fs20);
    line-height: var(--f-fs-font-fs28);
    color: var(--vtc-text-text-white-text-1);
    font-weight: var(--f-fw-bold);
    transition: all 0.4s;
}
.heading4-w h5 a:hover {
    transition: all 0.4s;
    color: var(--vtc-bg-main-bg-6);
}
.heading4-w p {
    color: var(--vtc-text-pera-text-2);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-medium);
}

.heading5 span.span {
    display: inline-block;
    color: var(--vtc-text-heading-text-3);
    font-size: var(--f-fs-font-fs16);
    line-height: var(--f-fs-font-fs16);
    font-weight: var(--f-fw-medium);
    border-radius: 4px;
    background-color: var(--vtc-bg-common-bg8);
    padding: 8px 12px;
    margin-bottom: 16px;
}
.heading5 h2 {
    font-size: var(--f-fs-font-fs44);
    line-height: var(--f-fs-font-fs54);
    color: var(--vtc-text-heading-text-3);
    font-weight: var(--f-fw-bold);
    transition: all 0.4s;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .heading5 h2 {
        font-size: var(--f-fs-font-fs32);
        line-height: var(--f-fs-font-fs40);
    }
}
@media (max-width: 767px) {
    .heading5 h2 {
        font-size: var(--f-fs-font-fs32);
        line-height: var(--f-fs-font-fs40);
    }
}
.heading5 h5 a {
    display: inline-block;
    font-size: var(--f-fs-font-fs20);
    line-height: var(--f-fs-font-fs28);
    color: var(--vtc-text-heading-text-3);
    font-weight: var(--f-fw-bold);
    transition: all 0.4s;
}
.heading5 h5 a:hover {
    transition: all 0.4s;
    color: var(--vtc-bg-main-bg-2);
}
.heading5 h4 a {
    display: inline-block;
    color: var(--vtc-text-heading-text-3);
    font-size: var(--f-fs-font-fs24);
    line-height: var(--f-fs-font-fs24);
    font-weight: var(--f-fw-bold);
    transition: all 0.4s;
}
.heading5 h4 a:hover {
    color: var(--vtc-bg-main-bg-2);
    transition: all 0.4s;
}
.heading5 p {
    color: var(--vtc-text-pera-text-1);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-medium);
    transition: all 0.4s;
}

.heading6 span.span {
    color: #31572c;
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: 18px; /* 100% */
    text-transform: uppercase;
    border-radius: 4px;
    background: #e0e5de;
    padding: 10px 14px 5px 14px;
    display: inline-block;
    margin-bottom: 16px;
}
.heading6 span.span img {
    transform: translateY(-3px);
    margin-right: 3px;
}
.heading6 h2 {
    color: #081120;
    font-size: 44px;
    font-style: normal;
    font-weight: 600;
    line-height: 44px; /* 100% */
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .heading6 h2 {
        font-size: 30px;
        line-height: 38px;
    }
}
@media (max-width: 767px) {
    .heading6 h2 {
        font-size: 30px;
        line-height: 38px;
    }
}
.heading6 p {
    color: #555;
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 26px; /* 144.444% */
}
.heading6 h4 a {
    display: inline-block;
    color: #081120;
    font-size: 20px;
    font-style: normal;
    font-weight: 700;
    line-height: 20px; /* 100% */
    transition: all 0.4s;
}
.heading6 h4 a:hover {
    color: #f1c832;
    transition: all 0.4s;
}

.heading6-w span.span {
    color: #fff;
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: 18px; /* 100% */
    text-transform: uppercase;
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.1);
    padding: 10px 14px 5px 14px;
    display: inline-block;
    margin-bottom: 16px;
}
.heading6-w span.span img {
    filter: brightness(0) invert(1);
    transform: translateY(-3px);
    margin-right: 3px;
}
.heading6-w h2 {
    color: #fff;
    font-size: 44px;
    font-style: normal;
    font-weight: 700;
    line-height: 54px; /* 122.727% */
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .heading6-w h2 {
        font-size: 30px;
        line-height: 38px;
    }
}
@media (max-width: 767px) {
    .heading6-w h2 {
        font-size: 30px;
        line-height: 38px;
    }
}
.heading6-w p {
    color: rgba(255, 255, 255, 0.7725490196);
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 28px; /* 155.556% */
}

.heading7 span.span {
    color: #5957e5;
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: 18px; /* 100% */
    text-transform: uppercase;
    border-radius: 4px;
    background: #eeeefc;
    padding: 10px 14px 5px 14px;
    display: inline-block;
    margin-bottom: 16px;
}
.heading7 span.span img {
    transform: translateY(-3px);
    margin-right: 3px;
}
.heading7 h2 {
    color: #081120;
    font-size: 44px;
    font-style: normal;
    font-weight: 600;
    line-height: 44px; /* 100% */
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .heading7 h2 {
        font-size: 30px;
        line-height: 38px;
    }
}
@media (max-width: 767px) {
    .heading7 h2 {
        font-size: 30px;
        line-height: 38px;
    }
}
.heading7 p {
    color: #555;
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 26px; /* 144.444% */
}
.heading7 h4 a {
    display: inline-block;
    color: #081120;
    font-size: 20px;
    font-style: normal;
    font-weight: 700;
    line-height: 20px; /* 100% */
    transition: all 0.4s;
}
.heading7 h4 a:hover {
    color: #f1c832;
    transition: all 0.4s;
}

.heading8 span.span {
    color: #141339;
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: 18px; /* 100% */
    text-transform: uppercase;
    border-radius: 4px;
    background: rgba(20, 19, 57, 0.0666666667);
    padding: 10px 14px 5px 14px;
    display: inline-block;
    margin-bottom: 16px;
}
.heading8 span.span img {
    transform: translateY(-3px);
    margin-right: 3px;
}
.heading8 h2 {
    color: #081120;
    font-size: 44px;
    font-style: normal;
    font-weight: 600;
    line-height: 44px; /* 100% */
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .heading8 h2 {
        font-size: 30px;
        line-height: 38px;
    }
}
@media (max-width: 767px) {
    .heading8 h2 {
        font-size: 30px;
        line-height: 38px;
    }
}
.heading8 p {
    color: #555;
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 26px; /* 144.444% */
}

.heading9 span.span {
    color: #2a9134;
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: 18px; /* 100% */
    text-transform: uppercase;
    border-radius: 4px;
    background: #eaf4eb;
    padding: 10px 14px 5px 14px;
    display: inline-block;
    margin-bottom: 16px;
}
.heading9 span.span img {
    transform: translateY(-3px);
    margin-right: 3px;
}
.heading9 h2 {
    color: #081120;
    font-size: 44px;
    font-style: normal;
    font-weight: 600;
    line-height: 44px; /* 100% */
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .heading9 h2 {
        font-size: 30px;
        line-height: 38px;
    }
}
@media (max-width: 767px) {
    .heading9 h2 {
        font-size: 30px;
        line-height: 38px;
    }
}
.heading9 p {
    color: #555;
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 26px; /* 144.444% */
}

.heading10 span.span {
    color: #fa6444;
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: 18px; /* 100% */
    text-transform: uppercase;
    border-radius: 4px;
    background: #ffefec;
    padding: 10px 14px 5px 14px;
    display: inline-block;
    margin-bottom: 16px;
}
.heading10 span.span img {
    transform: translateY(-3px);
    margin-right: 3px;
}
.heading10 h2 {
    color: #081120;
    font-size: 44px;
    font-style: normal;
    font-weight: 600;
    line-height: 44px; /* 100% */
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .heading10 h2 {
        font-size: 30px;
        line-height: 38px;
    }
}
@media (max-width: 767px) {
    .heading10 h2 {
        font-size: 30px;
        line-height: 38px;
    }
}
.heading10 p {
    color: #555;
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 26px; /* 144.444% */
}

/*Pagination*/
.theme-pagination ul {
    margin: 0;
    padding: 0;
    list-style: none;
}

.theme-pagination li {
    display: inline-block;
}

.theme-pagination li a {
    width: 55px;
    height: 55px;
    text-align: center;
    line-height: 55px;
    background: #f5f3f4;
    border-radius: 4px;
    margin: 0 4px;
    transition: all 0.3s;
    display: block;
    color: #000;
    font-weight: var(--f-fw-bold);
}

.blog2-box {
    border: 1px solid rgba(86, 95, 118, 0.2);
    border-radius: 6px;
}

.theme-pagination li a:hover,
.theme-pagination li a.active {
    background: var(--vtc-bg-main-bg-1);
    box-shadow: 0px 4px 10px rgba(122, 120, 198, 0.2);
    transition: all 0.3s;
    color: #ffffff;
}

@media screen and (min-width: 768px) {
    .overlay-anim {
        position: relative;
    }
    .overlay-anim:after {
        background: rgba(255, 255, 255, 0.3);
        content: "";
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 0;
        opacity: 1;
        z-index: 9;
        pointer-events: none;
    }
    .overlay-anim:hover:after {
        height: 100%;
        opacity: 0;
        transition: all 400ms linear;
    }
}
@media screen and (min-width: 768px) {
    .reveal {
        position: relative;
        display: inline-flex;
        visibility: hidden;
        overflow: hidden;
    }
    .reveal img {
        height: 100%;
        width: 100%;
        -o-object-fit: cover;
        object-fit: cover;
        transform-origin: left;
    }
}
/*============================
++++PAGE-PROGRESS-SATRT+++++
=============================*/
.blok:nth-of-type(odd) {
    background-color: white;
}

.blok:nth-of-type(even) {
    background-color: black;
}
/* #Progress
================================================== */
.progress-wrap {
    position: fixed;
    right: 30px;
    bottom: 30px;
    height: 56px;
    width: 56px;
    cursor: pointer;
    display: block;
    border-radius: 50px;
    box-shadow: inset 0 0 0 2px rgba(0, 0, 0, 0.1);
    z-index: 1;
    opacity: 0;
    visibility: hidden;
    transform: translateY(15px);
    transition: all 200ms linear;
}

.progress-wrap.active-progress {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
    z-index: 99;
}

.progress-wrap::after {
    position: absolute;
    font-family: "FontAwesome";
    content: "\f062";
    text-align: center;
    line-height: 56px;
    font-size: 18px;
    color: #03256c;
    left: 0;
    top: 0;
    height: 56px;
    width: 56px;
    cursor: pointer;
    display: block;
    z-index: 1;
    transition: all 200ms linear;
}

.progress-wrap:hover::after {
    opacity: 0;
}

.progress-wrap::before {
    position: absolute;
    font-family: "FontAwesome";
    content: "\f062";
    text-align: center;
    line-height: 56px;
    font-size: 18px;
    opacity: 0;
    left: 0;
    top: 0;
    height: 56px;
    width: 56px;
    cursor: pointer;
    display: block;
    z-index: 2;
    transition: all 200ms linear;
}

.progress-wrap:hover::before {
    opacity: 1;
}

.progress-wrap svg path {
    fill: none;
}

.progress-wrap svg.progress-circle path {
    stroke: #03256c; /* --- Lijn progres kleur --- */
    stroke-width: 4;
    box-sizing: border-box;
    transition: all 200ms linear;
}

/*============================
++++PAGE-PROGRESS-END+++++
=============================*/
.body2 .progress-wrap {
    position: fixed;
    right: 30px;
    bottom: 30px;
    height: 56px;
    width: 56px;
    cursor: pointer;
    display: block;
    border-radius: 50px;
    box-shadow: inset 0 0 0 2px rgba(255, 255, 255, 0.267);
    z-index: 1;
    opacity: 0;
    visibility: hidden;
    transform: translateY(15px);
    transition: all 200ms linear;
}

.body2 .progress-wrap.active-progress {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
    z-index: 99;
    color: rgb(255, 255, 255);
}

.body2 .progress-wrap::after {
    position: absolute;
    font-family: "FontAwesome";
    content: "\f062";
    text-align: center;
    line-height: 56px;
    font-size: 18px;
    color: #f7f7f7;
    left: 0;
    top: 0;
    height: 56px;
    width: 56px;
    cursor: pointer;
    display: block;
    z-index: 1;
    transition: all 200ms linear;
}

.body2 .progress-wrap:hover::after {
    opacity: 0;
}

.body2 .progress-wrap::before {
    position: absolute;
    font-family: "FontAwesome";
    content: "\f062";
    text-align: center;
    line-height: 56px;
    font-size: 18px;
    opacity: 0;
    left: 0;
    top: 0;
    height: 56px;
    width: 56px;
    cursor: pointer;
    display: block;
    z-index: 2;
    transition: all 200ms linear;
}

.body2 .progress-wrap:hover::before {
    opacity: 1;
}

.body2 .progress-wrap svg path {
    fill: none;
}

.body2 .progress-wrap svg.progress-circle path {
    stroke: #ffffff; /* --- Lijn progres kleur --- */
    stroke-width: 4;
    box-sizing: border-box;
    transition: all 200ms linear;
}

.shape-right {
    position: absolute;
    right: 0;
    top: 0;
    z-index: -2;
}

.shape-left {
    position: absolute;
    left: 0;
    top: 0;
    z-index: -2;
}

/*
::::::::::::::::::::::::::
 COMMON AREA CSS
::::::::::::::::::::::::::
*/
/*
::::::::::::::::::::::::::
 BUTTONS AREA CSS
::::::::::::::::::::::::::
*/
.theme-btn1 {
    position: relative;
    display: inline-block;
    vertical-align: middle;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border: none;
    outline: none !important;
    font-weight: var(--f-fw-bold);
    font-size: var(--f-fs-font-fs16);
    line-height: var(--f-fs-font-fs16);
    color: var(--vtc-text-text-white-text-1);
    text-transform: capitalize;
    padding: 18px 22px 18px 22px;
    background-color: var(--vtc-bg-main-bg-1);
    overflow: hidden;
    z-index: 1;
    border-radius: 4px;
}
.theme-btn1::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-color: #ffaa60;
    background-position: left center;
    transition-delay: 0.1s;
    transition-timing-function: ease-in-out;
    transition-duration: 0.5s;
    transition-property: all;
    transform-origin: left;
    transform-style: preserve-3d;
    transform: scaleX(0);
    z-index: -1;
}
.theme-btn1:hover::before {
    transform: scaleX(1);
}
.theme-btn1::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-color: rgb(3, 0, 0);
    transform: scaleY(0);
    transform-origin: center;
    transform-style: preserve-3d;
    transition: all 0.6s linear;
    transform-origin: right center;
    z-index: -1;
}
.theme-btn1:hover::after {
    transform: scaleY(1);
    transform-origin: bottom center;
    transition-delay: 200ms;
    transition: all 0.4s linear;
}
.theme-btn1:hover {
    color: var(--vtc-text-text-white-text-1);
}
.theme-btn1 span {
    position: relative;
    margin-left: 4px;
    font-size: 16px;
    transition: all 0.4s linear;
    transform: rotate(-45deg);
    display: inline-block;
}

.theme-btn2 {
    position: relative;
    display: inline-block;
    vertical-align: middle;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border: none;
    outline: none !important;
    font-weight: var(--f-fw-bold);
    font-size: var(--f-fs-font-fs16);
    line-height: var(--f-fs-font-fs16);
    color: var(--vtc-text-heading-text-1);
    border: 2px solid var(--vtc-text-heading-text-1);
    text-transform: capitalize;
    padding: 16px 22px 16px 22px;
    background: transparent;
    overflow: hidden;
    z-index: 1;
    border-radius: 4px;
    transition: all 0.4s;
    margin-left: 20px;
}
@media (max-width: 767px) {
    .theme-btn2 {
        margin-left: 0;
        margin-right: 30px;
        margin-top: 20px;
    }
}
.theme-btn2::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-color: #ffaa60;
    background-position: left center;
    transition-delay: 0.1s;
    transition-timing-function: ease-in-out;
    transition-duration: 0.5s;
    transition-property: all;
    transform-origin: left;
    transform-style: preserve-3d;
    transform: scaleX(0);
    z-index: -1;
}
.theme-btn2:hover::before {
    transform: scaleX(1);
}
.theme-btn2::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-color: rgb(3, 0, 0);
    transform: scaleY(0);
    transform-origin: center;
    transform-style: preserve-3d;
    transition: all 0.4s linear;
    transform-origin: right center;
    z-index: -1;
}
.theme-btn2:hover::after {
    transform: scaleY(1);
    transform-origin: bottom center;
    transition-delay: 200ms;
    transition: all 0.4s linear;
}
.theme-btn2:hover {
    color: var(--vtc-text-text-white-text-1);
}
.theme-btn2 span {
    position: relative;
    margin-left: 4px;
    font-size: 16px;
    transition: all 0.4s linear;
    transform: rotate(-45deg);
    display: inline-block;
}

.theme-btn3 {
    position: relative;
    display: inline-block;
    vertical-align: middle;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border: none;
    outline: none !important;
    font-weight: var(--f-fw-bold);
    font-size: var(--f-fs-font-fs16);
    line-height: var(--f-fs-font-fs16);
    color: var(--vtc-text-text-white-text-1);
    text-transform: capitalize;
    padding: 18px 22px 18px 22px;
    background-color: var(--vtc-bg-main-bg-1);
    overflow: hidden;
    z-index: 1;
    border-radius: 4px;
}
.theme-btn3::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-color: rgb(3, 0, 0);
    background-position: left center;
    transition-delay: 0.1s;
    transition-timing-function: ease-in-out;
    transition-duration: 0.5s;
    transition-property: all;
    transform-origin: left;
    transform-style: preserve-3d;
    transform: scaleX(0);
    z-index: -1;
}
.theme-btn3:hover::before {
    transform: scaleX(1);
}
.theme-btn3::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-color: #ffaa60;
    transform: scaleY(0);
    transform-origin: center;
    transform-style: preserve-3d;
    transition: all 0.6s linear;
    transform-origin: right center;
    z-index: -1;
}
.theme-btn3:hover::after {
    transform: scaleY(1);
    transform-origin: bottom center;
    transition-delay: 200ms;
    transition: all 0.4s linear;
}
.theme-btn3:hover {
    color: var(--vtc-text-text-white-text-1);
}
.theme-btn3 span {
    position: relative;
    margin-left: 4px;
    font-size: 16px;
    transition: all 0.4s linear;
    transform: rotate(-45deg);
    display: inline-block;
}

.theme-btn4 {
    position: relative;
    display: inline-block;
    vertical-align: middle;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border: none;
    outline: none !important;
    font-weight: var(--f-fw-bold);
    font-size: var(--f-fs-font-fs16);
    line-height: var(--f-fs-font-fs16);
    color: var(--vtc-text-text-white-text-1);
    text-transform: capitalize;
    padding: 18px 22px 18px 22px;
    background-color: var(--vtc-bg-main-bg-2);
    overflow: hidden;
    z-index: 1;
    border-radius: 4px;
    position: relative;
}
.theme-btn4::after {
    content: "";
    position: absolute;
    top: 0px;
    right: -1px;
    height: 100%;
    width: 102%;
    background-color: #cb3d4e;
    transform: rotateY(95deg);
    transition: all 0.6s linear;
    z-index: -1;
    opacity: 0;
    border-radius: 4px;
}
.theme-btn4:hover::after {
    transform: rotateY(0deg);
    transform-origin: bottom center;
    transition-delay: 200ms;
    transition: all 0.4s linear;
    opacity: 1;
}
.theme-btn4:hover {
    color: var(--vtc-text-text-white-text-1);
}
.theme-btn4 span {
    position: relative;
    margin-left: 4px;
    font-size: 16px;
    transition: all 0.4s linear;
    transform: rotate(-45deg);
    display: inline-block;
}

.theme-btn5 {
    position: relative;
    display: inline-block;
    vertical-align: middle;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border: none;
    outline: none !important;
    font-weight: var(--f-fw-bold);
    font-size: var(--f-fs-font-fs16);
    line-height: var(--f-fs-font-fs16);
    color: var(--vtc-text-text-white-text-1);
    border: 2px solid var(--vtc-text-text-white-text-1);
    text-transform: capitalize;
    padding: 16px 22px 16px 22px;
    overflow: hidden;
    z-index: 1;
    border-radius: 4px;
    position: relative;
    margin-left: 20px;
    transition: all 0.4s;
}
@media (max-width: 767px) {
    .theme-btn5 {
        margin-left: 0;
        margin-top: 20px;
        margin-right: 70px;
    }
}
.theme-btn5::after {
    content: "";
    position: absolute;
    top: 0px;
    right: -1px;
    height: 100%;
    width: 102%;
    background-color: var(--vtc-bg-main-bg-2);
    transform: rotateY(95deg);
    transition: all 0.6s linear;
    z-index: -1;
    opacity: 0;
    border-radius: 4px;
}
.theme-btn5:hover::after {
    transform: rotateY(0deg);
    transform-origin: bottom center;
    transition-delay: 200ms;
    transition: all 0.4s linear;
    opacity: 1;
}
.theme-btn5:hover {
    color: var(--vtc-text-text-white-text-1);
    border: 2px solid var(--vtc-bg-main-bg-2);
    transition: all 0.4s;
}
.theme-btn5 span {
    position: relative;
    margin-left: 4px;
    font-size: 16px;
    transition: all 0.4s linear;
    transform: rotate(-45deg);
    display: inline-block;
}

.theme-btn6 {
    position: relative;
    display: inline-block;
    vertical-align: middle;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border: none;
    outline: none !important;
    font-weight: var(--f-fw-bold);
    font-size: var(--f-fs-font-fs16);
    line-height: var(--f-fs-font-fs16);
    color: var(--vtc-text-text-white-text-1);
    text-transform: capitalize;
    padding: 18px 22px 18px 22px;
    background-color: var(--vtc-bg-main-bg-3);
    overflow: hidden;
    z-index: 1;
    border-radius: 4px;
}
.theme-btn6::after {
    content: "";
    position: absolute;
    top: 0px;
    right: -1px;
    height: 100%;
    width: 0%;
    background-color: var(--vtc-text-heading-text-1);
    transition: all 0.4s linear;
    z-index: -1;
    opacity: 0;
    border-radius: 0px;
}
.theme-btn6::before {
    content: "";
    position: absolute;
    top: 0px;
    left: 0;
    height: 100%;
    width: 0%;
    background-color: var(--vtc-text-heading-text-1);
    transition: all 0.4s linear;
    z-index: -1;
    opacity: 0;
    border-radius: 0px;
}
.theme-btn6:hover {
    transform-origin: bottom center;
    transition-delay: 200ms;
    transition: all 0.4s linear;
    opacity: 1;
}
.theme-btn6:hover::after {
    width: 51%;
    opacity: 1;
}
.theme-btn6:hover::before {
    width: 51%;
    opacity: 1;
}
.theme-btn6:hover {
    color: var(--vtc-text-text-white-text-1);
}
.theme-btn6 span {
    position: relative;
    margin-left: 4px;
    font-size: 16px;
    transition: all 0.4s linear;
    transform: rotate(-45deg);
    display: inline-block;
}

.theme-btn7 {
    position: relative;
    display: inline-block;
    vertical-align: middle;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border: none;
    outline: none !important;
    font-weight: var(--f-fw-bold);
    font-size: var(--f-fs-font-fs16);
    line-height: var(--f-fs-font-fs16);
    color: var(--vtc-text-text-white-text-1);
    text-transform: capitalize;
    padding: 16px 22px 16px 22px;
    border: 2px solid var(--vtc-text-text-white-text-1);
    overflow: hidden;
    z-index: 1;
    border-radius: 4px;
    margin-left: 20px;
    transition: all 0.4s;
}
@media (max-width: 767px) {
    .theme-btn7 {
        margin-left: 0;
    }
}
.theme-btn7::after {
    content: "";
    position: absolute;
    top: 0px;
    right: -1px;
    height: 100%;
    width: 0%;
    background-color: var(--vtc-bg-main-bg-3);
    transition: all 0.4s linear;
    z-index: -1;
    opacity: 0;
    border-radius: 0px;
}
.theme-btn7::before {
    content: "";
    position: absolute;
    top: 0px;
    left: 0;
    height: 100%;
    width: 0%;
    background-color: var(--vtc-bg-main-bg-3);
    transition: all 0.4s linear;
    z-index: -1;
    opacity: 0;
    border-radius: 0px;
}
.theme-btn7:hover {
    transform-origin: bottom center;
    transition-delay: 200ms;
    transition: all 0.4s linear;
    opacity: 1;
}
.theme-btn7:hover::after {
    width: 51%;
    opacity: 1;
}
.theme-btn7:hover::before {
    width: 51%;
    opacity: 1;
}
.theme-btn7:hover {
    color: var(--vtc-text-text-white-text-1);
    border: 2px solid var(--vtc-bg-main-bg-3);
    transition: all 0.4s;
}
.theme-btn7 span {
    position: relative;
    margin-left: 4px;
    font-size: 16px;
    transition: all 0.4s linear;
    transform: rotate(-45deg);
    display: inline-block;
}

.theme-btn8 {
    position: relative;
    display: inline-block;
    vertical-align: middle;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border: none;
    outline: none !important;
    font-weight: var(--f-fw-bold);
    font-size: var(--f-fs-font-fs16);
    line-height: var(--f-fs-font-fs16);
    color: var(--vtc-text-text-white-text-1);
    text-transform: capitalize;
    padding: 18px 22px 18px 22px;
    background-color: var(--vtc-bg-main-bg-6);
    overflow: hidden;
    z-index: 1;
    border-radius: 4px;
}
.theme-btn8::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-color: #add9ec;
    background-position: left center;
    transition-delay: 0.1s;
    transition-timing-function: ease-in-out;
    transition-duration: 0.5s;
    transition-property: all;
    transform-origin: left;
    transform-style: preserve-3d;
    transform: scaleX(0);
    z-index: -1;
}
.theme-btn8:hover::before {
    transform: scaleX(1);
}
.theme-btn8::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-color: rgb(3, 0, 0);
    transform: scaleY(0);
    transform-origin: center;
    transform-style: preserve-3d;
    transition: all 0.6s linear;
    transform-origin: right center;
    z-index: -1;
}
.theme-btn8:hover::after {
    transform: scaleY(1);
    transform-origin: bottom center;
    transition-delay: 200ms;
    transition: all 0.4s linear;
}
.theme-btn8:hover {
    color: var(--vtc-text-text-white-text-1);
}
.theme-btn8 span {
    position: relative;
    margin-left: 4px;
    font-size: 16px;
    transition: all 0.4s linear;
    transform: rotate(-45deg);
    display: inline-block;
}

.theme-btn10 {
    position: relative;
    display: inline-block;
    vertical-align: middle;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border: none;
    outline: none !important;
    font-weight: var(--f-fw-bold);
    font-size: var(--f-fs-font-fs16);
    line-height: var(--f-fs-font-fs16);
    color: var(--vtc-text-text-white-text-1);
    text-transform: capitalize;
    padding: 18px 22px 18px 22px;
    background-color: var(--vtc-bg-main-bg-5);
    overflow: hidden;
    z-index: 1;
    border-radius: 4px;
}
.theme-btn10::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-color: #a8dbf1;
    background-position: left center;
    transition-delay: 0.1s;
    transition-timing-function: ease-in-out;
    transition-duration: 0.5s;
    transition-property: all;
    transform-origin: left;
    transform-style: preserve-3d;
    transform: scaleX(0);
    z-index: -1;
}
.theme-btn10:hover::before {
    transform: scaleX(1);
}
.theme-btn10::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-color: var(--vtc-bg-main-bg-6);
    transform: scaleY(0);
    transform-origin: center;
    transform-style: preserve-3d;
    transition: all 0.6s linear;
    transform-origin: right center;
    z-index: -1;
}
.theme-btn10:hover::after {
    transform: scaleY(1);
    transform-origin: bottom center;
    transition-delay: 200ms;
    transition: all 0.4s linear;
}
.theme-btn10:hover {
    color: var(--vtc-text-text-white-text-1);
}
.theme-btn10 span {
    position: relative;
    margin-left: 4px;
    font-size: 16px;
    transition: all 0.4s linear;
    transform: rotate(-45deg);
    display: inline-block;
}

.theme-btn9 {
    position: relative;
    display: inline-block;
    vertical-align: middle;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border: 2px solid var(--vtc-text-heading-text-2);
    outline: none !important;
    font-weight: var(--f-fw-bold);
    font-size: var(--f-fs-font-fs16);
    line-height: var(--f-fs-font-fs16);
    color: var(--vtc-text-heading-text-2);
    text-transform: capitalize;
    padding: 16px 22px 16px 22px;
    overflow: hidden;
    z-index: 1;
    border-radius: 4px;
    margin-left: 16px;
    transition: all 0.4s;
}
@media (max-width: 767px) {
    .theme-btn9 {
        margin-left: 0;
        margin-right: 30px;
    }
}
.theme-btn9::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-color: rgb(3, 0, 0);
    background-position: left center;
    transition-delay: 0.1s;
    transition-timing-function: ease-in-out;
    transition-duration: 0.5s;
    transition-property: all;
    transform-origin: left;
    transform-style: preserve-3d;
    transform: scaleX(0);
    z-index: -1;
}
.theme-btn9:hover::before {
    transform: scaleX(1);
}
.theme-btn9::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-color: var(--vtc-bg-main-bg-6);
    transform: scaleY(0);
    transform-origin: center;
    transform-style: preserve-3d;
    transition: all 0.6s linear;
    transform-origin: right center;
    z-index: -1;
}
.theme-btn9:hover::after {
    transform: scaleY(1);
    transform-origin: bottom center;
    transition-delay: 200ms;
    transition: all 0.4s linear;
}
.theme-btn9:hover {
    color: var(--vtc-text-text-white-text-1);
    border: 2px solid var(--vtc-bg-main-bg-6);
    transition: all 0.4s;
}
.theme-btn9 span {
    position: relative;
    margin-left: 4px;
    font-size: 16px;
    transition: all 0.4s linear;
    transform: rotate(-45deg);
    display: inline-block;
}

.theme-btn11 {
    color: #111;
    font-size: 18px;
    font-style: normal;
    font-weight: 700;
    line-height: 18px; /* 100% */
    text-transform: uppercase;
    display: inline-block;
    background-color: #f1c832;
    border-radius: 8px;
    padding: 18px 22px;
    transition: all 0.4s;
    border: none;
}
.theme-btn11 span {
    display: inline-block;
    transform: rotate(0deg);
    transition: all 0.4s;
}
.theme-btn11:hover {
    color: #111;
    transition: all 0.4s;
    transform: translateY(-5px);
    background-color: #ffd84d;
}
.theme-btn11:hover span {
    transition: all 0.4s;
    transform: rotate(-45deg);
}

.theme-btn12 {
    color: #fff;
    font-size: 18px;
    font-style: normal;
    font-weight: 700;
    line-height: 18px; /* 100% */
    text-transform: uppercase;
    display: inline-block;
    background-color: #5957e5;
    border-radius: 8px;
    padding: 19px 22px;
    transition: all 0.4s;
    border: none;
}
.theme-btn12:hover {
    color: #fff;
    transition: all 0.4s;
    transform: translateY(-5px);
    background-color: #6e6cfc;
}

.theme-btn13 {
    color: #081120;
    font-size: 18px;
    font-style: normal;
    font-weight: 700;
    line-height: 18px; /* 100% */
    text-transform: uppercase;
    display: inline-block;
    background-color: #fff;
    border-radius: 8px;
    padding: 19px 22px;
    transition: all 0.4s;
    border: none;
}
.theme-btn13:hover {
    color: #fff;
    transition: all 0.4s;
    transform: translateY(-5px);
    background-color: #6e6cfc;
}

.theme-btn14 {
    color: #081120;
    font-size: 18px;
    font-style: normal;
    font-weight: 700;
    line-height: 18px; /* 100% */
    text-transform: uppercase;
    display: inline-block;
    background-color: #f6aa32;
    border-radius: 8px;
    padding: 19px 22px;
    transition: all 0.4s;
    border: none;
}
.theme-btn14:hover {
    color: #081120;
    transition: all 0.4s;
    transform: translateY(-5px);
    background-color: #f7b752;
}

.theme-btn15 {
    color: #fff;
    font-size: 18px;
    font-style: normal;
    font-weight: 700;
    line-height: 18px; /* 100% */
    text-transform: uppercase;
    display: inline-block;
    background-color: #2a9134;
    border-radius: 8px;
    padding: 18px 22px;
    transition: all 0.4s;
    border: none;
}
.theme-btn15 span {
    display: inline-block;
    transform: rotate(0deg);
    transition: all 0.4s;
}
.theme-btn15:hover {
    color: #fff;
    transition: all 0.4s;
    transform: translateY(-5px);
    background-color: #081120;
}
.theme-btn15:hover span {
    transition: all 0.4s;
    transform: rotate(-45deg);
}

.theme-btn16 {
    color: #fff;
    font-size: 18px;
    font-style: normal;
    font-weight: 700;
    line-height: 18px; /* 100% */
    text-transform: uppercase;
    display: inline-block;
    background-color: #fa6444;
    border-radius: 8px;
    padding: 18px 22px;
    transition: all 0.4s;
    border: none;
}
.theme-btn16 span {
    display: inline-block;
    transform: rotate(0deg);
    transition: all 0.4s;
}
.theme-btn16:hover {
    color: #fff;
    transition: all 0.4s;
    transform: translateY(-5px);
    background-color: #081120;
}
.theme-btn16:hover span {
    transition: all 0.4s;
    transform: rotate(-45deg);
}

/*
::::::::::::::::::::::::::
    BUTOTNS AREA CSS
::::::::::::::::::::::::::
*/
/*
::::::::::::::::::::::::::
 ABOUT AREA CSS
::::::::::::::::::::::::::
*/
.about2 .about1-box {
    display: flex;
    align-items: start;
    margin-top: 32px;
}
.about2 .about1-box .icon {
    height: 70px;
    width: 70px;
    background-color: rgba(255, 255, 255, 0.2392156863);
    border-radius: 50%;
    text-align: center;
    line-height: 70px;
    margin-right: 20px;
    transition: all 0.4s;
}
.about2 .about1-box:hover .icon {
    background-color: var(--vtc-bg-main-bg-2);
    transition: all 0.4s;
}
.about2 .image {
    margin-right: 30px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .about2 .image {
        margin-right: 0;
        margin-bottom: 40px;
    }
}
@media (max-width: 767px) {
    .about2 .image {
        margin-right: 0;
        margin-bottom: 40px;
    }
}
.about2 .image img {
    width: 100%;
}

.about3 .image img {
    width: 100%;
    border-radius: 4px;
}
.about3 .conter-box {
    border-radius: 4px;
    padding: 32px;
}
.about3 .conter-box h3 {
    font-size: var(--f-fs-font-fs44);
    line-height: var(--f-fs-font-fs54);
    color: var(--vtc-text-text-white-text-1);
    font-weight: var(--f-fw-bold);
}
.about3 .conter-box p {
    color: rgba(255, 255, 255, 0.8);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-medium);
}
.about3 .conter-box1 {
    background-color: var(--vtc-bg-main-bg-4);
    margin-top: 30px;
}
.about3 .conter-box2 {
    background-color: var(--vtc-bg-main-bg-3);
    margin-bottom: 30px;
}
.about3 .about3-icon-box {
    display: flex;
    align-items: start;
    margin-top: 32px;
}
.about3 .about3-icon-box .icon {
    background-color: var(--vtc-bg-common-bg6);
    border-radius: 50%;
    height: 70px;
    width: 70px;
    line-height: 70px;
    text-align: center;
    transition: all 0.4s;
}
.about3 .about3-icon-box .icon img {
    transition: all 0.4s;
}
.about3 .about3-icon-box .heading3 {
    padding-left: 20px;
}
.about3 .about3-icon-box .heading3 p {
    padding-top: 12px;
}
.about3 .about3-icon-box:hover .icon {
    background-color: var(--vtc-bg-main-bg-3);
}
.about3 .about3-icon-box:hover .icon img {
    filter: brightness(0) invert(1);
    transition: all 0.4s;
}
.about3 .about3-heading {
    padding-left: 30px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .about3 .about3-heading {
        padding-left: 0;
    }
}
@media (max-width: 767px) {
    .about3 .about3-heading {
        padding-left: 0;
    }
}

.about4 .images {
    position: relative;
    text-align: start;
}
.about4 .images .image2 {
    position: absolute;
    right: 30px;
    bottom: 100px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .about4 .about4-heading {
        margin-top: 30px;
    }
}
@media (max-width: 767px) {
    .about4 .about4-heading {
        margin-top: 30px;
    }
}
.about4 .heading4 .about4-border {
    background-color: var(--vtc-text-pera-text-3);
    width: 100%;
    height: 1px;
    opacity: 0.2;
    margin: 24px 0px;
}
.about4 .heading4 .about4-box {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
}
.about4 .heading4 .about4-box .icon {
    height: 70px;
    width: 70px;
    border-radius: 50%;
    background-color: var(--vtc-bg-common-bg7);
    text-align: center;
    line-height: 70px;
    margin-right: 20px;
    transition: all 0.4s;
}
.about4 .heading4 .about4-box .icon img {
    transition: all 0.4s;
}
.about4 .heading4 .about4-box:hover .icon {
    background-color: var(--vtc-bg-main-bg-5);
    transition: all 0.4s;
}
.about4 .heading4 .about4-box:hover .icon img {
    transition: all 0.4s;
    filter: brightness(0) invert(1);
}

.about5 .images {
    position: relative;
    min-height: 575px;
}
.about5 .images .image1 {
    position: absolute;
    right: 0;
    top: 0;
}
.about5 .images .image2 {
    position: absolute;
    left: 0;
    bottom: 0;
}
.about5 .images .image3 {
    position: absolute;
    left: 0;
    top: 100px;
}
.about5 .about5-heading {
    padding-left: 30px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .about5 .about5-heading {
        padding-left: 0;
        margin-top: 40px;
    }
}
@media (max-width: 767px) {
    .about5 .about5-heading {
        padding-left: 0;
        margin-top: 40px;
    }
}
.about5 .about5-heading .icon-box {
    margin-top: 30px;
}
.about5 .about5-heading .icon-box .icon {
    height: 70px;
    width: 70px;
    background-color: var(--vtc-bg-common-bg8);
    border-radius: 50%;
    line-height: 70px;
    text-align: center;
    margin-bottom: 20px;
    transition: all 0.4s;
}
.about5 .about5-heading .icon-box .icon img {
    transition: all 0.4s;
}
.about5 .about5-heading .icon-box:hover .icon {
    background-color: var(--vtc-bg-main-bg-2);
    transition: all 0.4s;
}
.about5 .about5-heading .icon-box:hover .icon img {
    transition: all 0.4s;
    filter: brightness(0) invert(1);
}
.about5 .buttons-about5 {
    display: flex;
    align-items: center;
}
.about5 .buttons-about5 .phone-btn {
    display: flex;
    align-items: center;
    margin-left: 24px;
}
.about5 .buttons-about5 .phone-btn .icon a {
    display: inline-block;
    height: 50px;
    width: 50px;
    line-height: 50px;
    text-align: center;
    font-size: 20px;
    color: var(--vtc-bg-main-bg-2);
    border-radius: 4px;
    border: 2px solid var(--vtc-bg-main-bg-2);
}
.about5 .buttons-about5 .phone-btn .heading {
    padding-left: 20px;
}
.about5 .buttons-about5 .phone-btn .heading p {
    color: var(--vtc-text-heading-text-3);
    font-size: var(--f-fs-font-fs16);
    line-height: var(--f-fs-font-fs16);
    font-weight: var(--f-fw-medium);
    padding-bottom: 8px;
}
.about5 .buttons-about5 .phone-btn .heading h4 a {
    display: inline-block;
    color: var(--vtc-text-heading-text-3);
    font-size: var(--f-fs-font-fs24);
    line-height: var(--f-fs-font-fs24);
    font-weight: var(--f-fw-bold);
    transition: all 0.4s;
}
.about5 .buttons-about5 .phone-btn .heading h4 a:hover {
    color: var(--vtc-bg-main-bg-2);
    transition: all 0.4s;
}

.about-page-sec1 .image img {
    width: 100%;
    border-radius: 4px;
}
.about-page-sec1 .conter-box {
    border-radius: 4px;
    padding: 32px;
}
.about-page-sec1 .conter-box h3 {
    font-size: var(--f-fs-font-fs44);
    line-height: var(--f-fs-font-fs54);
    color: var(--vtc-text-text-white-text-1);
    font-weight: var(--f-fw-bold);
}
.about-page-sec1 .conter-box p {
    color: rgba(255, 255, 255, 0.8);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-medium);
}
.about-page-sec1 .conter-box1 {
    background-color: #081120;
    margin-top: 30px;
}
.about-page-sec1 .conter-box2 {
    background-color: var(--vtc-bg-main-bg-1);
    margin-bottom: 30px;
}
.about-page-sec1 .about3-icon-box {
    display: flex;
    align-items: start;
    margin-top: 32px;
}
.about-page-sec1 .about3-icon-box .icon {
    background-color: #fff2e6;
    border-radius: 50%;
    height: 70px;
    width: 70px;
    line-height: 70px;
    text-align: center;
    transition: all 0.4s;
}
.about-page-sec1 .about3-icon-box .icon img {
    transition: all 0.4s;
}
.about-page-sec1 .about3-icon-box .heading1 {
    padding-left: 20px;
}
.about-page-sec1 .about3-icon-box .heading1 p {
    padding-top: 12px;
}
.about-page-sec1 .about3-icon-box:hover .icon {
    background-color: var(--vtc-bg-main-bg-1);
}
.about-page-sec1 .about3-icon-box:hover .icon img {
    filter: brightness(0) invert(1);
    transition: all 0.4s;
}
.about-page-sec1 .about3-heading {
    padding-left: 30px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .about-page-sec1 .about3-heading {
        padding-left: 0;
    }
}
@media (max-width: 767px) {
    .about-page-sec1 .about3-heading {
        padding-left: 0;
    }
}

.mission {
    background-color: var(--vtc-bg-common-bg2);
}
.mission .mission-box {
    background-color: var(--vtc-text-text-white-text-1);
    border-radius: 4px;
    padding: 24px;
    margin-top: 34px;
    transition: all 0.4s;
}
.mission .mission-box .icon {
    height: 60px;
    width: 60px;
    background-color: rgba(255, 124, 1, 0.1294117647);
    text-align: center;
    line-height: 60px;
    border-radius: 50%;
    margin-bottom: 20px;
    transition: all 0.4s;
}
.mission .mission-box:hover {
    transition: all 0.4s;
    transform: translateY(-10px);
    background-color: var(--vtc-bg-main-bg-1);
}
.mission .mission-box:hover .icon {
    background-color: #fff;
    transition: all 0.4s;
}
.mission .mission-box:hover .heading1 h5 a {
    color: var(--vtc-text-text-white-text-1);
    transition: all 0.4s;
}
.mission .mission-box:hover .heading1 p {
    color: rgba(255, 255, 255, 0.8352941176);
    transition: all 0.4s;
}

.team1.about-page-team {
    background-color: #fff;
}
.team1.about-page-team .team1-box .heading-area {
    background-color: #f5f3f4;
}

.tes1.about-page-testimonial {
    background-color: var(--vtc-bg-common-bg2);
}
.tes1.about-page-testimonial .tes1-slider .single-slider {
    background-color: #fff;
}
.tes1.about-page-testimonial .tes7-buttons button {
    background-color: #f6e7dc;
}

.about6 .images-all {
    position: relative;
    height: 580px;
}
.about6 .images-all .image2 {
    position: absolute;
    bottom: 0;
    right: 0;
}
.about6 .images-all .image3 {
    position: absolute;
    top: 40px;
    right: 60px;
}
.about6 .heading6 {
    padding-left: 30px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .about6 .heading6 {
        padding-left: 0;
        padding-top: 40px;
    }
}
@media (max-width: 767px) {
    .about6 .heading6 {
        padding-left: 0;
        padding-top: 40px;
    }
}
.about6 .heading6 .list li {
    color: #081120;
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 18px; /* 100% */
    display: flex;
    align-items: center;
    padding-top: 18px;
}
.about6 .heading6 .list li .check {
    display: inline-block;
    height: 22px;
    width: 22px;
    text-align: center;
    line-height: 22px;
    background-color: #31572c;
    border-radius: 50%;
    font-size: 12px;
    color: #fff;
    margin-right: 5px;
}

.about7 .images-all {
    position: relative;
}
.about7 .images-all .shape {
    position: absolute;
    top: -50px;
    left: -40px;
}
.about7 .images-all .image {
    margin-bottom: 30px;
}
.about7 .images-all .image img {
    width: 100%;
}
.about7 .images-all .review-area {
    background-color: #5957e5;
    border-radius: 7px;
    padding: 28px;
    margin: 0px 20px;
}
@media (max-width: 767px) {
    .about7 .images-all .review-area {
        margin: 0px 0px;
    }
}
.about7 .images-all .review-area h3 {
    color: #fff;
    font-size: 48px;
    font-style: normal;
    font-weight: 600;
    line-height: 48px; /* 100% */
}
.about7 .images-all .review-area p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 19.193px;
    font-style: normal;
    font-weight: 500;
    line-height: 19.193px; /* 100% */
    padding-top: 12px;
}
.about7 .images-all .review-area img {
    padding-top: 12px;
}
.about7 .heading7 {
    padding-left: 40px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .about7 .heading7 {
        padding-left: 0;
        padding-top: 30px;
    }
}
@media (max-width: 767px) {
    .about7 .heading7 {
        padding-left: 0;
        padding-top: 30px;
    }
}
.about7 .heading7 .counter-box {
    padding-top: 30px;
    text-align: center;
    padding-right: 30px;
}
.about7 .heading7 .counter-box h3 {
    color: #081120;
    font-size: 36px;
    font-style: normal;
    font-weight: 600;
    line-height: 36px; /* 100% */
}
.about7 .heading7 .counter-box p {
    padding-top: 16px;
}

.about8 .heading8 .list li {
    color: #081120;
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 18px; /* 100% */
    display: flex;
    align-items: center;
    padding-top: 18px;
}
.about8 .heading8 .list li .check {
    display: inline-block;
    height: 22px;
    width: 22px;
    text-align: center;
    line-height: 22px;
    background-color: #141339;
    border-radius: 50%;
    font-size: 12px;
    color: #fff;
    margin-right: 5px;
}
.about8 .about8-images {
    margin-right: -70px;
    margin-left: 50px;
    position: relative;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .about8 .about8-images {
        margin-left: 0;
        margin-bottom: 50px;
        margin-top: 50px;
    }
}
@media (max-width: 767px) {
    .about8 .about8-images {
        margin-left: 0;
    }
}
.about8 .about8-images .shape {
    position: absolute;
    bottom: -100px;
    left: -100px;
    z-index: 1;
    opacity: 0.4;
}
.about8 .about8-images .cs_case_study_1_list {
    display: flex;
    position: relative;
    z-index: 2;
}
@media (max-width: 767px) {
    .about8 .about8-images .cs_case_study_1_list {
        flex-direction: column;
    }
}
.about8 .about8-images .cs_case_study_1_list > .cs_case_study.cs_style_1 {
    flex: 1;
}
.about8 .about8-images .cs_case_study_1_list .cs_case_study.cs_style_1 {
    min-height: 400px;
}
.about8 .about8-images .cs_case_study_1_list .cs_case_study.cs_style_1 .cs_case_study_in {
    transition: all 0.2s ease;
    left: 80px;
    opacity: 0;
}
@media (max-width: 767px) {
    .about8 .about8-images .cs_case_study_1_list .cs_case_study.cs_style_1 .cs_case_study_in {
        left: 0;
        opacity: 1;
    }
}
.about8 .about8-images .cs_case_study_1_list .cs_case_study.cs_style_1.active {
    flex: 3;
}
.about8 .about8-images .cs_case_study_1_list .cs_case_study.cs_style_1.active .cs_case_study_in {
    opacity: 1;
    left: 0;
    transition: all 0.6s ease;
    transition-delay: 0.3s;
}
.about8 .about8-images .cs_case_study_1_list .cs_case_study.cs_style_1.active .cs_case_study_icon {
    transform: scale(0) rotate(360deg);
    transition-delay: 0.1s;
}
.about8 .about8-images .cs_case_study.cs_style_1 {
    height: 700px;
    display: flex;
    align-items: flex-end;
    position: relative;
    overflow: hidden;
    transition: all 0.4s ease;
}
@media (max-width: 1400px) {
    .about8 .about8-images .cs_case_study.cs_style_1 {
        height: 600px;
    }
}
.about8 .about8-images .cs_case_study.cs_style_1 .cs_case_study_title a {
    text-decoration: none;
    background-image: linear-gradient(currentColor, currentColor);
    background-repeat: no-repeat;
    background-position: bottom left;
    background-size: 100% 3px;
}
.about8 .about8-images .cs_case_study.cs_style_1 .cs_case_study_number {
    left: 70px;
    top: 70px;
    z-index: 1;
}
@media (max-width: 767px) {
    .about8 .about8-images .cs_case_study.cs_style_1 .cs_case_study_number {
        left: 30px;
        top: 40px;
    }
}
.about8 .about8-images .cs_case_study.cs_style_1 .cs_case_study_category {
    margin-bottom: 15px;
}
.about8 .about8-images .cs_case_study.cs_style_1 .cs_case_study_thumb {
    position: absolute;
    height: 100%;
    width: 100%;
    left: 0;
    top: 0;
}
@media (max-width: 767px) {
    .about8 .about8-images .cs_case_study.cs_style_1 .cs_case_study_thumb {
        margin-bottom: 30px;
        top: auto;
        bottom: 30px;
    }
}
.about8 .about8-images .cs_case_study.cs_style_1 .cs_case_study_in {
    position: relative;
    z-index: 2;
    width: 100%;
    padding: 88px 72px;
}
@media (max-width: 1400px) {
    .about8 .about8-images .cs_case_study.cs_style_1 .cs_case_study_in {
        padding: 50px;
    }
}
@media (max-width: 767px) {
    .about8 .about8-images .cs_case_study.cs_style_1 .cs_case_study_in {
        padding: 30px;
    }
}
.about8 .about8-images .cs_case_study.cs_style_1 .cs_case_study_icon {
    left: 70px;
    bottom: 86px;
    z-index: 1;
    background-color: #4f4747;
    color: #fff;
    height: 55px;
    width: 55px;
    font-size: 20px;
    transition: all 0.6s ease;
}
@media (max-width: 1400px) {
    .about8 .about8-images .cs_case_study.cs_style_1 .cs_case_study_icon {
        left: 40px;
        bottom: 50px;
    }
}
@media (max-width: 767px) {
    .about8 .about8-images .cs_case_study.cs_style_1 .cs_case_study_icon {
        display: none;
    }
}
.about8 .about8-images .cs_case_study_thumb {
    background-image: url(../img/hero/hero7-image1.png);
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
    cursor: pointer;
}
.about8 .about8-images .cs_case_study_thumb.cs_case_study_thumb2 {
    background-image: url(../img/hero/hero7-image2.png);
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
    cursor: pointer;
}
.about8 .about8-images .cs_case_study_thumb.cs_case_study_thumb3 {
    background-image: url(../img/hero/hero7-image3.png);
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
    cursor: pointer;
}
.about8 .about8-images .cs_case_study.cs_style_1.cs_hover_active {
    margin: 0px 12px;
    border-radius: 8px;
    height: 460px;
}

.about9 .heading9 {
    padding-left: 40px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .about9 .heading9 {
        padding-left: 0px;
        margin-top: 40px;
    }
}
@media (max-width: 767px) {
    .about9 .heading9 {
        padding-left: 0px;
        margin-top: 40px;
    }
}
.about9 .heading9 .list li {
    color: #081120;
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 18px; /* 100% */
    display: flex;
    align-items: center;
    padding-top: 18px;
}
.about9 .heading9 .list li .check {
    display: inline-block;
    height: 22px;
    width: 22px;
    text-align: center;
    line-height: 22px;
    background-color: #2a9134;
    border-radius: 50%;
    font-size: 12px;
    color: #fff;
    margin-right: 5px;
}
@media (max-width: 767px) {
    .about9 .images-all .image {
        margin-top: 30px;
    }
}
.about9 .images-all .image img {
    width: 100%;
}

.about10 .about10-images {
    margin-right: 30px;
    margin-left: 50px;
    position: relative;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .about10 .about10-images {
        margin-right: 0;
    }
}
@media (max-width: 767px) {
    .about10 .about10-images {
        margin-right: 0;
    }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .about10 .about10-images {
        margin-left: 0;
        margin-bottom: 50px;
        margin-top: 50px;
    }
}
@media (max-width: 767px) {
    .about10 .about10-images {
        margin-left: 0;
    }
}
.about10 .about10-images .shape {
    position: absolute;
    bottom: -100px;
    left: -100px;
    z-index: 1;
    opacity: 0.4;
}
.about10 .about10-images .cs_case_study_1_list {
    display: flex;
    position: relative;
    z-index: 2;
}
@media (max-width: 767px) {
    .about10 .about10-images .cs_case_study_1_list {
        flex-direction: column;
    }
}
.about10 .about10-images .cs_case_study_1_list > .cs_case_study.cs_style_1 {
    flex: 1;
}
.about10 .about10-images .cs_case_study_1_list .cs_case_study.cs_style_1 {
    min-height: 400px;
}
.about10 .about10-images .cs_case_study_1_list .cs_case_study.cs_style_1 .cs_case_study_in {
    transition: all 0.2s ease;
    left: 80px;
    opacity: 0;
}
@media (max-width: 767px) {
    .about10 .about10-images .cs_case_study_1_list .cs_case_study.cs_style_1 .cs_case_study_in {
        left: 0;
        opacity: 1;
    }
}
.about10 .about10-images .cs_case_study_1_list .cs_case_study.cs_style_1.active {
    flex: 3;
}
.about10 .about10-images .cs_case_study_1_list .cs_case_study.cs_style_1.active .cs_case_study_in {
    opacity: 1;
    left: 0;
    transition: all 0.6s ease;
    transition-delay: 0.3s;
}
.about10 .about10-images .cs_case_study_1_list .cs_case_study.cs_style_1.active .cs_case_study_icon {
    transform: scale(0) rotate(360deg);
    transition-delay: 0.1s;
}
.about10 .about10-images .cs_case_study.cs_style_1 {
    height: 700px;
    display: flex;
    align-items: flex-end;
    position: relative;
    overflow: hidden;
    transition: all 0.4s ease;
}
@media (max-width: 1400px) {
    .about10 .about10-images .cs_case_study.cs_style_1 {
        height: 600px;
    }
}
.about10 .about10-images .cs_case_study.cs_style_1 .cs_case_study_title a {
    text-decoration: none;
    background-image: linear-gradient(currentColor, currentColor);
    background-repeat: no-repeat;
    background-position: bottom left;
    background-size: 100% 3px;
}
.about10 .about10-images .cs_case_study.cs_style_1 .cs_case_study_number {
    left: 70px;
    top: 70px;
    z-index: 1;
}
@media (max-width: 767px) {
    .about10 .about10-images .cs_case_study.cs_style_1 .cs_case_study_number {
        left: 30px;
        top: 40px;
    }
}
.about10 .about10-images .cs_case_study.cs_style_1 .cs_case_study_category {
    margin-bottom: 15px;
}
.about10 .about10-images .cs_case_study.cs_style_1 .cs_case_study_thumb {
    position: absolute;
    height: 100%;
    width: 100%;
    left: 0;
    top: 0;
}
@media (max-width: 767px) {
    .about10 .about10-images .cs_case_study.cs_style_1 .cs_case_study_thumb {
        margin-bottom: 30px;
        top: auto;
        bottom: 30px;
    }
}
.about10 .about10-images .cs_case_study.cs_style_1 .cs_case_study_in {
    position: relative;
    z-index: 2;
    width: 100%;
    padding: 88px 72px;
}
@media (max-width: 1400px) {
    .about10 .about10-images .cs_case_study.cs_style_1 .cs_case_study_in {
        padding: 50px;
    }
}
@media (max-width: 767px) {
    .about10 .about10-images .cs_case_study.cs_style_1 .cs_case_study_in {
        padding: 30px;
    }
}
.about10 .about10-images .cs_case_study.cs_style_1 .cs_case_study_icon {
    left: 70px;
    bottom: 86px;
    z-index: 1;
    background-color: #4f4747;
    color: #fff;
    height: 55px;
    width: 55px;
    font-size: 20px;
    transition: all 0.6s ease;
}
@media (max-width: 1400px) {
    .about10 .about10-images .cs_case_study.cs_style_1 .cs_case_study_icon {
        left: 40px;
        bottom: 50px;
    }
}
@media (max-width: 767px) {
    .about10 .about10-images .cs_case_study.cs_style_1 .cs_case_study_icon {
        display: none;
    }
}
.about10 .about10-images .cs_case_study_thumb {
    background-image: url(../img/hero/hero7-image1.png);
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
    cursor: pointer;
}
.about10 .about10-images .cs_case_study_thumb.cs_case_study_thumb2 {
    background-image: url(../img/hero/hero7-image2.png);
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
    cursor: pointer;
}
.about10 .about10-images .cs_case_study_thumb.cs_case_study_thumb3 {
    background-image: url(../img/hero/hero7-image3.png);
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
    cursor: pointer;
}
.about10 .about10-images .cs_case_study.cs_style_1.cs_hover_active {
    margin: 0px 12px;
    border-radius: 8px;
    height: 460px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .about10 .heading10 {
        padding-top: 30px;
    }
}
@media (max-width: 767px) {
    .about10 .heading10 {
        padding-top: 30px;
    }
}
.about10 .heading10 .counter-box {
    padding-top: 30px;
    text-align: center;
}
.about10 .heading10 .counter-box h3 {
    color: #081120;
    font-size: 36px;
    font-style: normal;
    font-weight: 600;
    line-height: 36px; /* 100% */
}
.about10 .heading10 .counter-box p {
    padding-top: 16px;
}

/*
::::::::::::::::::::::::::
 ABOUT AREA CSS
::::::::::::::::::::::::::
*/
/*
::::::::::::::::::::::::::
 SERVICE AREA CSS
::::::::::::::::::::::::::
*/
.service1 {
    background-color: var(--vtc-text-heading-text-1);
}

.service1-box {
    margin-top: 30px;
    overflow: hidden;
    border-radius: 4px;
    position: relative;
    transition: all 0.4s;
}
.service1-box .image img {
    width: 100%;
    transition: all 0.4s;
}
.service1-box .hover-area {
    position: absolute;
    bottom: 0;
    margin: 24px;
    transform: translateY(60px);
    opacity: 0;
    transition: all 0.4s;
    z-index: 2;
}
.service1-box .hover-area p {
    color: rgba(255, 255, 255, 0.7882352941);
}
.service1-box::after {
    content: "";
    position: absolute;
    bottom: -500px;
    right: -30px;
    height: 600px;
    width: 600px;
    background-color: var(--vtc-bg-main-bg-1);
    border-radius: 50%;
    opacity: 0;
    transition: all 0.4s;
}
.service1-box:hover::after {
    opacity: 1;
    transition: all 0.4s;
    bottom: -350px;
}
.service1-box:hover .image img {
    transition: all 0.4s;
    transform: scale(1.1) rotate(2deg);
}
.service1-box:hover .hover-area {
    opacity: 1;
    transform: translateY(0);
    transition: all 0.4s;
}

.service1-box.active .hover-area {
    position: absolute;
    bottom: 0;
    margin: 24px;
    transform: translateY(0);
    opacity: 1;
    transition: all 0.4s;
    z-index: 2;
}
.service1-box.active .hover-area p {
    color: rgba(255, 255, 255, 0.7882352941);
}
.service1-box.active::after {
    content: "";
    position: absolute;
    bottom: -350px;
    right: -30px;
    height: 600px;
    width: 600px;
    background-color: var(--vtc-bg-main-bg-1);
    border-radius: 50%;
    opacity: 1;
    transition: all 0.4s;
}

.service2-box {
    margin-top: 60px;
    padding: 70px 32px 32px 32px;
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.1019607843);
    position: relative;
    transition: all 0.4s;
}
.service2-box::after {
    content: "";
    position: absolute;
    height: 100%;
    width: 100%;
    border-radius: 4px;
    top: 0;
    left: 0;
    transform: rotateY(95deg);
    transition: all 0.6s linear;
    background-color: var(--vtc-bg-main-bg-2);
    z-index: -1;
    opacity: 0;
}
.service2-box .icon {
    height: 70px;
    width: 70px;
    line-height: 70px;
    text-align: center;
    border-radius: 50%;
    background-color: #46466b;
    position: absolute;
    top: -35px;
    left: 30px;
    transition: all 0.4s;
}
.service2-box .icon img {
    transition: all 0.4s;
    filter: brightness(0) invert(1);
}
.service2-box .learn {
    display: inline-block;
    position: relative;
    display: inline-block;
    vertical-align: middle;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border: none;
    outline: none !important;
    font-weight: var(--f-fw-bold);
    font-size: var(--f-fs-font-fs16);
    line-height: var(--f-fs-font-fs16);
    color: var(--vtc-text-text-white-text-1);
    text-transform: capitalize;
    padding: 16px 22px 16px 22px;
    background-color: var(--vtc-bg-main-bg-2);
    overflow: hidden;
    z-index: 1;
    border-radius: 4px;
    position: relative;
    margin-top: 24px;
    transition: all 0.4s;
}
.service2-box .learn span {
    position: relative;
    margin-left: 4px;
    font-size: 16px;
    transition: all 0.4s linear;
    transform: rotate(-45deg);
    display: inline-block;
}
.service2-box:hover {
    transform: translateY(-10px);
    transition: all 0.4s;
}
.service2-box:hover::after {
    transform: rotateY(0deg);
    transition: all 0.4s linear;
    background-color: var(--vtc-bg-main-bg-2);
    z-index: -1;
    opacity: 1;
}
.service2-box:hover .icon {
    background-color: var(--vtc-text-text-white-text-1);
    transition: all 0.4s;
}
.service2-box:hover .icon img {
    transition: all 0.4s;
    filter: none;
}
.service2-box:hover .learn {
    color: var(--vtc-bg-main-bg-2);
    background-color: var(--vtc-text-text-white-text-1);
    transition: all 0.4s;
}

.service-benefits .benefits-box {
    padding: 32px;
    border-radius: 4px;
    margin-top: 30px;
    transition: all 0.4s;
    position: relative;
}
.service-benefits .benefits-box::after {
    content: "";
    position: absolute;
    height: 100%;
    width: 100%;
    border-radius: 4px;
    top: 0;
    left: 0;
    transform: rotateY(95deg);
    transition: all 0.6s linear;
    background-color: var(--vtc-bg-main-bg-2);
    z-index: -1;
    opacity: 0;
}
.service-benefits .benefits-box::before {
    content: "";
    position: absolute;
    height: 100%;
    width: 100%;
    border-radius: 4px;
    top: 0;
    left: 0;
    transition: all 0.6s linear;
    background-color: #282a5e;
    z-index: -3;
}
.service-benefits .benefits-box h4 a {
    line-height: var(--f-fs-font-fs32);
}
.service-benefits .benefits-box .icon {
    height: 70px;
    width: 70px;
    line-height: 70px;
    text-align: center;
    border-radius: 50%;
    background-color: #46466b;
    transition: all 0.4s;
    margin-bottom: 30px;
}
.service-benefits .benefits-box .icon img {
    transition: all 0.4s;
    filter: brightness(0) invert(1);
}
.service-benefits .benefits-box a.learn {
    display: inline-block;
    color: var(--vtc-bg-common-bg1);
    font-size: var(--f-fs-font-fs16);
    line-height: var(--f-fs-font-fs16);
    font-weight: var(--f-fw-medium);
}
.service-benefits .benefits-box a.learn span {
    display: inline-block;
    transform: rotate(-45deg);
    margin-left: 3px;
}
.service-benefits .benefits-box:hover {
    transform: translateY(-10px);
    transition: all 0.4s;
}
.service-benefits .benefits-box:hover::after {
    opacity: 1;
    transform: rotateY(0deg);
    transition: all 0.4s;
}
.service-benefits .benefits-box:hover .icon {
    background-color: var(--vtc-text-text-white-text-1);
    transition: all 0.4s;
}
.service-benefits .benefits-box:hover .icon img {
    filter: none;
    transition: all 0.4s;
}

.service3 {
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    position: relative;
}
.service3 .service3-box {
    background-color: var(--vtc-text-text-white-text-1);
    border-radius: 4px;
    padding: 32px;
    margin-top: 30px;
    transition: all 0.4s;
}
.service3 .service3-box .icon {
    height: 70px;
    width: 70px;
    line-height: 70px;
    text-align: center;
    border-radius: 5px;
    background-color: var(--vtc-bg-common-bg5);
    margin-bottom: 30px;
    transition: all 0.4s;
}
.service3 .service3-box .icon img {
    transition: all 0.4s linear;
}
.service3 .service3-box .learn {
    display: inline-block;
    font-size: var(--f-fs-font-fs16);
    line-height: var(--f-fs-font-fs16);
    color: var(--vtc-text-heading-text-1);
    font-weight: var(--f-fw-bold);
    transition: all 0.4s;
}
.service3 .service3-box .learn:hover {
    color: var(--vtc-bg-main-bg-3);
    transition: all 0.4s;
}
.service3 .service3-box .learn span {
    display: inline-block;
    transform: rotate(-45deg);
}
.service3 .service3-box:hover {
    transition: all 0.4s;
    transform: translateY(-10px);
}
.service3 .service3-box:hover .icon {
    height: 70px;
    width: 70px;
    line-height: 70px;
    text-align: center;
    border-radius: 5px;
    background-color: var(--vtc-bg-main-bg-3);
    margin-bottom: 30px;
    transition: all 0.4s;
}
.service3 .service3-box:hover .icon img {
    transition: all 0.4s linear;
    filter: brightness(0) invert(1);
    transform: rotateY(180deg);
}
.service3 .shape1 {
    position: absolute;
    top: 0;
    right: 0;
}

.service4 {
    background-color: var(--vtc-bg-main-bg-5);
}

.service4-box {
    position: relative;
    margin-top: 34px;
    transition: all 0.4s;
}
.service4-box .image {
    overflow: hidden;
    border-radius: 4px;
}
.service4-box .image img {
    width: 100%;
    transition: all 0.4s;
}
.service4-box .icon {
    display: inline-block;
    height: 50px;
    width: 50px;
    text-align: center;
    line-height: 50px;
    font-size: 22px;
    color: var(--vtc-bg-main-bg-6);
    background-color: var(--vtc-text-text-white-text-1);
    border-radius: 50%;
    position: absolute;
    top: 24px;
    right: 24px;
    transform: rotate(-45deg) scale(1.4);
    transition: all 0.4s;
    opacity: 0;
}
.service4-box .heading4-w {
    margin: 0px 30px;
    position: absolute;
    bottom: 30px;
}
.service4-box:hover {
    transition: all 0.4s;
    transform: translateY(-10px);
}
.service4-box:hover .image img {
    transform: rotate(2deg) scale(1.1);
    transition: all 0.4s;
}
.service4-box:hover .icon {
    opacity: 1;
    transition: all 0.4s;
    transform: rotate(-45deg) scale(1);
}

.service5 {
    background-color: var(--vtc-bg-common-bg9);
}

.service5-box {
    margin-top: 60px;
    padding: 70px 32px 32px 32px;
    border-radius: 4px;
    background-color: var(--vtc-bg-bg-white);
    position: relative;
    transition: all 0.4s;
}
.service5-box::after {
    content: "";
    position: absolute;
    height: 100%;
    width: 100%;
    border-radius: 4px;
    top: 0;
    left: 0;
    transform: rotateY(95deg);
    transition: all 0.6s linear;
    background-color: var(--vtc-bg-main-bg-2);
    z-index: -1;
    opacity: 0;
}
.service5-box .icon {
    height: 70px;
    width: 70px;
    line-height: 70px;
    text-align: center;
    border-radius: 50%;
    background-color: #f3dfe5;
    position: absolute;
    top: -35px;
    left: 30px;
    transition: all 0.4s;
}
.service5-box .icon img {
    transition: all 0.4s;
}
.service5-box .learn {
    display: inline-block;
    position: relative;
    display: inline-block;
    vertical-align: middle;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border: none;
    outline: none !important;
    font-weight: var(--f-fw-bold);
    font-size: var(--f-fs-font-fs16);
    line-height: var(--f-fs-font-fs16);
    color: var(--vtc-text-text-white-text-1);
    text-transform: capitalize;
    padding: 16px 22px 16px 22px;
    background-color: var(--vtc-bg-main-bg-2);
    overflow: hidden;
    z-index: 1;
    border-radius: 4px;
    position: relative;
    margin-top: 24px;
    transition: all 0.4s;
}
.service5-box .learn span {
    position: relative;
    margin-left: 4px;
    font-size: 16px;
    transition: all 0.4s linear;
    transform: rotate(-45deg);
    display: inline-block;
}
.service5-box:hover {
    transform: translateY(-10px);
    transition: all 0.4s;
}
.service5-box:hover .heading5 h4 a {
    color: var(--vtc-text-text-white-text-1);
    transition: all 0.4s;
}
.service5-box:hover .heading5 p {
    color: rgba(255, 255, 255, 0.7803921569);
    transition: all 0.4s;
}
.service5-box:hover::after {
    transform: rotateY(0deg);
    transition: all 0.4s linear;
    background-color: var(--vtc-bg-main-bg-2);
    z-index: -1;
    opacity: 1;
}
.service5-box:hover .icon {
    background-color: var(--vtc-text-text-white-text-1);
    transition: all 0.4s;
}
.service5-box:hover .icon img {
    transition: all 0.4s;
    filter: none;
}
.service5-box:hover .learn {
    color: var(--vtc-bg-main-bg-2);
    background-color: var(--vtc-text-text-white-text-1);
    transition: all 0.4s;
}

.service-benefits5 .benefits-box {
    padding: 32px;
    border-radius: 4px;
    margin-top: 30px;
    transition: all 0.4s;
    position: relative;
}
.service-benefits5 .benefits-box::after {
    content: "";
    position: absolute;
    height: 100%;
    width: 100%;
    border-radius: 4px;
    top: 0;
    left: 0;
    transform: rotateY(95deg);
    transition: all 0.6s linear;
    background-color: var(--vtc-bg-main-bg-2);
    z-index: -1;
    opacity: 0;
}
.service-benefits5 .benefits-box::before {
    content: "";
    position: absolute;
    height: 100%;
    width: 100%;
    border-radius: 4px;
    top: 0;
    left: 0;
    transition: all 0.6s linear;
    background-color: #f2f4f7;
    z-index: -3;
}
.service-benefits5 .benefits-box h4 a {
    line-height: var(--f-fs-font-fs32);
    color: var(--vtc-text-heading-text-3);
}
.service-benefits5 .benefits-box p {
    color: rgba(0, 20, 49, 0.8431372549);
}
.service-benefits5 .benefits-box .icon {
    height: 70px;
    width: 70px;
    line-height: 70px;
    text-align: center;
    border-radius: 50%;
    background-color: #f3dfe5;
    transition: all 0.4s;
    margin-bottom: 30px;
}
.service-benefits5 .benefits-box .icon img {
    transition: all 0.4s;
}
.service-benefits5 .benefits-box a.learn {
    display: inline-block;
    color: var(--vtc-text-heading-text-3);
    font-size: var(--f-fs-font-fs16);
    line-height: var(--f-fs-font-fs16);
    font-weight: var(--f-fw-medium);
    transition: all 0.4s;
}
.service-benefits5 .benefits-box a.learn span {
    display: inline-block;
    transform: rotate(-45deg);
    margin-left: 3px;
}
.service-benefits5 .benefits-box:hover {
    transform: translateY(-10px);
    transition: all 0.4s;
}
.service-benefits5 .benefits-box:hover::after {
    opacity: 1;
    transform: rotateY(0deg);
    transition: all 0.4s;
}
.service-benefits5 .benefits-box:hover h4 a {
    line-height: var(--f-fs-font-fs32);
    color: #fff;
    transition: all 0.4s;
}
.service-benefits5 .benefits-box:hover p {
    color: rgba(255, 255, 255, 0.8431372549);
    transition: all 0.4s;
}
.service-benefits5 .benefits-box:hover a.learn {
    display: inline-block;
    color: var(--vtc-text-text-white-text-1);
    font-size: var(--f-fs-font-fs16);
    line-height: var(--f-fs-font-fs16);
    font-weight: var(--f-fw-medium);
    transition: all 0.4s;
}
.service-benefits5 .benefits-box:hover a.learn span {
    display: inline-block;
    transform: rotate(-45deg);
    margin-left: 3px;
}
.service-benefits5 .benefits-box:hover .icon {
    background-color: var(--vtc-text-text-white-text-1);
    transition: all 0.4s;
}
.service-benefits5 .benefits-box:hover .icon img {
    filter: none;
    transition: all 0.4s;
}

.service1.service-page-service {
    background-color: #fff;
}

.service6 {
    background-position: center center;
    background-size: cover;
    background-repeat: no-repeat;
}
.service6 .service-box {
    margin-top: 30px;
    background-color: #fff;
    border-radius: 8px;
    padding: 24px;
    position: relative;
    text-align: center;
    transition: all 0.4s;
}
.service6 .service-box .icon {
    height: 90px;
    width: 90px;
    text-align: center;
    line-height: 90px;
    background-color: #f3f5f2;
    position: relative;
    left: 50%;
    margin-left: -45px;
    border-radius: 50%;
    margin-top: -45px;
    z-index: 9;
}
.service6 .service-box .icon img {
    transition: all 0.4s;
}
.service6 .service-box .heading6 h5 a {
    display: inline-block;
    color: #081120;
    font-size: 20px;
    font-style: normal;
    font-weight: 700;
    line-height: 20px; /* 100% */
    text-transform: uppercase;
    padding-top: 24px;
    padding-bottom: 12px;
    transition: all 0.4s;
}
.service6 .service-box .heading6 h5 a:hover {
    color: #f1c832;
}
.service6 .service-box .image {
    border-radius: 8px;
    position: relative;
    overflow: hidden;
}
.service6 .service-box .image img {
    width: 100%;
    transition: all 0.4s;
}
.service6 .service-box .image .arrow {
    position: absolute;
    top: 50%;
    left: 50%;
    background-color: #f1c832;
    height: 48px;
    width: 48px;
    text-align: center;
    line-height: 48px;
    border-radius: 50%;
    font-size: 20px;
    margin-top: -24px;
    margin-left: -24px;
    color: #081120;
    transform: rotate(-45deg) scale(1.5);
    z-index: 3;
    opacity: 0;
    transition: all 0.4s;
}
.service6 .service-box .image::after {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
    background-color: #081120;
    opacity: 0;
    border-radius: 8px;
    z-index: 1;
    transition: all 0.4s;
    transform: scale(0.6);
}
.service6 .service-box:hover {
    transform: translateY(-10px);
    transition: all 0.4s;
}
.service6 .service-box:hover .icon img {
    transition: all 0.4s;
    transform: rotateY(180deg);
}
.service6 .service-box:hover .image img {
    transition: all 0.4s;
    transform: scale(1.1) rotate(2deg);
}
.service6 .service-box:hover .image::after {
    opacity: 0.4;
    transform: scale(1);
}
.service6 .service-box:hover .image .arrow {
    transition: all 0.4s;
    transform: rotate(-45deg) scale(1);
    opacity: 1;
}

.service7 .service7-slider {
    margin-top: 60px;
}
.service7 .service7-slider .single-slider {
    margin: 0px 15px;
    position: relative;
}
.service7 .service7-slider .single-slider .image {
    border-radius: 8px;
    overflow: hidden;
    position: relative;
}
.service7 .service7-slider .single-slider .image img {
    width: 100%;
    transition: all 0.4s;
}
.service7 .service7-slider .single-slider .image::after {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
    border-radius: 8px;
    background-color: #081120;
    opacity: 0;
    transition: all 0.4s;
    transform: scale(0.6);
}
.service7 .service7-slider .single-slider .image .hover-icon {
    display: inline-block;
    font-size: 20px;
    color: #fff;
    height: 48px;
    width: 48px;
    text-align: center;
    line-height: 48px;
    background-color: #5957e5;
    border-radius: 50%;
    transform: rotate(-45deg) scale(1.5);
    position: absolute;
    top: 120px;
    left: 50%;
    margin-left: -24px;
    z-index: 2;
    opacity: 0;
    transition: all 0.4s;
}
.service7 .service7-slider .single-slider .hover-area {
    background-color: #fff;
    padding: 24px;
    border-radius: 8px;
    position: absolute;
    bottom: 24px;
    margin: 0px 20px 0px 30px;
    left: 0;
    width: 85%;
    display: flex;
    align-items: center;
}
.service7 .service7-slider .single-slider .hover-area h3 a {
    display: inline-block;
    color: #081120;
    font-size: 20px;
    font-style: normal;
    font-weight: 700;
    line-height: 20px; /* 100% */
    text-transform: uppercase;
    padding-left: 20px;
    transition: all 0.4s;
}
.service7 .service7-slider .single-slider .hover-area h3 a:hover {
    color: #5957e5;
    transition: all 0.4s;
}
.service7 .service7-slider .single-slider:hover {
    transition: all 0.4s;
}
.service7 .service7-slider .single-slider:hover .image img {
    transition: all 0.4s;
    transform: scale(1.1) rotate(2deg);
}
.service7 .service7-slider .single-slider:hover .image .hover-icon {
    opacity: 1;
    transform: rotate(-45deg);
    transition: all 0.4s;
}
.service7 .service7-slider .single-slider:hover .image::after {
    transition: all 0.4s;
    opacity: 0.4;
    transform: scale(1);
}
.service7 .service7-slider .single-slider.slick-current.slick-active {
    transition: all 0.4s;
}
.service7 .service7-slider .single-slider.slick-current.slick-active .image img {
    transition: all 0.4s;
    transform: scale(1.1) rotate(2deg);
}
.service7 .service7-slider .single-slider.slick-current.slick-active .image .hover-icon {
    opacity: 1;
    transform: rotate(-45deg);
    transition: all 0.4s;
}
.service7 .service7-slider .single-slider.slick-current.slick-active .image::after {
    transition: all 0.4s;
    opacity: 0.4;
    transform: scale(1);
}
.service7 .arrows-button {
    text-align: end;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .service7 .arrows-button {
        text-align: center;
        margin-top: 20px;
    }
}
@media (max-width: 767px) {
    .service7 .arrows-button {
        text-align: center;
        margin-top: 20px;
    }
}
.service7 .arrows-button button {
    font-size: 20px;
    background-color: #fff;
    height: 56px;
    width: 56px;
    text-align: center;
    line-height: 56px;
    border: none;
    border-radius: 50%;
    margin-left: 10px;
    color: #081120;
    transition: all 0.4s;
}
.service7 .arrows-button button:hover {
    background-color: #5957e5;
    color: #fff;
    transition: all 0.4s;
}

.service8 .service-box {
    margin-top: 70px;
    background-color: #fff;
    border-radius: 8px;
    padding: 64px 28px 32px 28px;
    position: relative;
    transition: all 0.4s;
}
.service8 .service-box .icon {
    background-color: #fff;
    box-shadow: 0px 0px 40px rgba(0, 0, 0, 0.09);
    height: 80px;
    width: 80px;
    text-align: center;
    line-height: 80px;
    border-radius: 50%;
    position: absolute;
    top: -40px;
}
.service8 .service-box .icon img {
    transition: all 0.4s;
}
.service8 .service-box .heading h4 a {
    display: inline-block;
    color: #081120;
    font-size: 20px;
    font-style: normal;
    font-weight: 700;
    line-height: 20px; /* 100% */
    text-transform: uppercase;
    transition: all 0.4s;
}
.service8 .service-box .heading h4 a:hover {
    transition: all 0.4s;
    color: #f6aa32;
}
.service8 .service-box .heading p {
    color: #555;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px; /* 150% */
    padding: 16px 0px 20px 0px;
}
.service8 .service-box .heading .learn {
    display: inline-block;
    color: #081120;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: 16px; /* 100% */
    text-transform: uppercase;
    transition: all 0.4s;
}
.service8 .service-box .heading .learn:hover {
    transition: all 0.4s;
    color: #f6aa32;
}
.service8 .service-box:hover {
    background-color: #081120;
    transform: translateY(-10px);
    transition: all 0.4s;
}
.service8 .service-box:hover .icon img {
    transform: rotateY(180deg);
    transition: all 0.4s;
}
.service8 .service-box:hover .heading h4 a {
    transition: all 0.4s;
    color: #fff;
}
.service8 .service-box:hover .heading p {
    transition: all 0.4s;
    color: rgba(255, 255, 255, 0.7607843137);
}
.service8 .service-box:hover .heading .learn {
    transition: all 0.4s;
    color: #fff;
}

.service9 {
    position: relative;
}
.service9::after {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
    background-color: #f3f5f2;
    border-radius: 8px;
    z-index: -4;
}
.service9 .service-box {
    text-align: center;
    padding: 32px 28px;
    position: relative;
    z-index: 4;
    margin-top: 6px;
    transition: all 0.4s;
}
.service9 .service-box:hover {
    transform: translateY(-10px);
    transition: all 0.4s;
}
.service9 .service-box::after {
    content: "";
    position: absolute;
    left: 0;
    bottom: 0;
    height: 70%;
    width: 100%;
    background-color: #fff;
    border-radius: 8px;
    z-index: -1;
}
.service9 .service-box span.text {
    display: inline-block;
    color: #2a9134;
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: 18px; /* 100% */
    text-transform: uppercase;
    padding: 28px 0px 24px 0px;
}
.service9 .service-box h4 a {
    display: inline-block;
    color: #081120;
    font-size: 20px;
    font-style: normal;
    font-weight: 700;
    line-height: 20px; /* 100% */
    text-transform: uppercase;
    transition: all 0.4s;
}
.service9 .service-box h4 a:hover {
    color: #2a9134;
    transition: all 0.4s;
}
.service9 .service-box p {
    color: #555;
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 26px; /* 144.444% */
    padding: 14px 0px 20px 0px;
}
.service9 .service-box .learn {
    display: inline-block;
    color: #081120;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: 16px; /* 100% */
    text-transform: uppercase;
    transition: all 0.4s;
}
.service9 .service-box .learn:hover {
    color: #2a9134;
    transition: all 0.4s;
}

.service10 .service-box {
    margin-top: 30px;
    background-color: #fff;
    border-radius: 8px;
    padding: 28px 36px;
    text-align: center;
    transition: all 0.4s;
}
.service10 .service-box .icon {
    background-color: #f7f5fb;
    height: 80px;
    width: 80px;
    text-align: center;
    line-height: 80px;
    border-radius: 50%;
    margin: auto;
}
.service10 .service-box .heading h4 a {
    display: inline-block;
    color: #081120;
    font-size: 20px;
    font-style: normal;
    font-weight: 700;
    line-height: 20px; /* 100% */
    text-transform: uppercase;
    transition: all 0.4s;
    padding-top: 24px;
}
.service10 .service-box .heading p {
    color: #555;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px; /* 150% */
    padding: 16px 0px 22px 0px;
    transition: all 0.4s;
}
.service10 .service-box .heading .learn {
    display: inline-block;
    color: #081120;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: 16px; /* 100% */
    text-transform: uppercase;
    transition: all 0.4s;
}
.service10 .service-box:hover {
    transition: all 0.4s;
    transform: translateY(-10px);
    background-color: #fa6444;
}
.service10 .service-box:hover h4 a {
    color: #fff;
    transition: all 0.4s;
}
.service10 .service-box:hover p {
    color: rgba(255, 255, 255, 0.7803921569);
    transition: all 0.4s;
}
.service10 .service-box:hover a.learn {
    color: #fff;
    transition: all 0.4s;
}

/*
::::::::::::::::::::::::::
 SERVICE AREA CSS
::::::::::::::::::::::::::
*/
/*
::::::::::::::::::::::::::
 CHOOSE AREA CSS
::::::::::::::::::::::::::
*/
.chosse1 .icon-box {
    background-color: var(--vtc-bg-common-bg2);
    border-radius: 4px;
    padding: 24px;
    text-align: center;
    transition: all 0.4s;
    margin-bottom: 30px;
}
.chosse1 .icon-box .icon {
    margin-bottom: 20px;
    transition: all 0.4s;
}
.chosse1 .icon-box .icon img {
    transition: all 0.4s;
}
.chosse1 .icon-box:hover {
    background-color: var(--vtc-bg-main-bg-1);
    transition: all 0.4s;
    transform: translateY(-10px);
}
.chosse1 .icon-box:hover .icon {
    transition: all 0.4s;
}
.chosse1 .icon-box:hover .icon img {
    transition: all 0.4s;
    transform: rotateY(180deg);
    filter: brightness(40);
}
.chosse1 .icon-box:hover .heading1 h3 {
    transition: all 0.4s;
    color: var(--vtc-text-text-white-text-1);
}
.chosse1 .icon-box:hover .heading1 p {
    color: var(--vtc-text-text-white-text-1);
    transition: all 0.4s;
}
.chosse1 .icon-box.icon-box2 {
    margin-bottom: 0;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .chosse1 .image {
        margin-top: 30px;
    }
}
@media (max-width: 767px) {
    .chosse1 .image {
        margin-top: 30px;
    }
}
.chosse1 .image img {
    width: 100%;
    border-radius: 4px;
}
.chosse1 .choose1-heading {
    padding-left: 30px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .chosse1 .choose1-heading {
        padding-left: 0;
        padding-top: 30px;
    }
}
@media (max-width: 767px) {
    .chosse1 .choose1-heading {
        padding-left: 0;
        padding-top: 30px;
    }
}
.chosse1 .choose1-heading .icon-list {
    margin-top: 20px;
}
.chosse1 .choose1-heading .icon-list li {
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs18);
    font-weight: var(--f-fw-medium);
    color: var(--vtc-text-heading-text-1);
}
.chosse1 .choose1-heading .icon-list li span {
    display: inline-block;
    height: 20px;
    width: 20px;
    line-height: 20px;
    text-align: center;
    font-size: 12px;
    background-color: var(--vtc-text-heading-text-1);
    color: var(--vtc-text-text-white-text-1);
    border-radius: 50%;
    margin-right: 5px;
}

/*
 ::::::::::::::::::::::::::
  CHOOSE AREA CSS
 ::::::::::::::::::::::::::
 */
/*
::::::::::::::::::::::::::
 CTA AREA CSS
::::::::::::::::::::::::::
*/
.cta {
    background-color: var(--vtc-text-heading-text-1);
    padding-top: 60px;
}
.cta .cta-border {
    border-bottom: 1px solid rgba(255, 255, 255, 0.301);
    padding-bottom: 60px;
}
.cta .subscribe-area {
    text-align: end;
    position: relative;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .cta .subscribe-area {
        margin-top: 24px;
    }
}
@media (max-width: 767px) {
    .cta .subscribe-area {
        margin-top: 24px;
    }
}
.cta .subscribe-area input {
    padding: 18px;
    border: none;
    border-radius: 4px;
    width: 80%;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .cta .subscribe-area input {
        width: 100%;
    }
}
@media (max-width: 767px) {
    .cta .subscribe-area input {
        width: 100%;
    }
}
.cta .subscribe-area input:focus {
    outline: none;
}
.cta .subscribe-area input::-moz-placeholder {
    color: #696b6d;
}
.cta .subscribe-area input::placeholder {
    color: #696b6d;
}
.cta .subscribe-area .button {
    position: absolute;
    top: 5px;
    right: 5px;
}

.cta2 .cta2-bg {
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    position: relative;
    border-radius: 4px;
    margin-bottom: -220px;
    z-index: 2;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .cta2 .cta2-bg {
        min-height: 780px;
    }
}
@media (max-width: 767px) {
    .cta2 .cta2-bg {
        min-height: 730px;
    }
}
.cta2 .cta2-bg .main-image {
    text-align: center;
    position: absolute;
    bottom: 0;
    left: 120px;
}
.cta2 .cta2-bg .heading2 {
    padding: 80px 80px 80px 0px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .cta2 .cta2-bg .heading2 {
        padding: 80px 80px 80px 80px;
    }
}
@media (max-width: 767px) {
    .cta2 .cta2-bg .heading2 {
        padding: 40px 40px 40px 40px;
    }
}
.cta2 .cta2-bg .heading2 p {
    color: rgba(255, 255, 255, 0.8274509804);
}
.cta2 .cta2-bg .subscribe-area {
    text-align: start;
    position: relative;
    margin-right: 100px;
    margin-top: 40px;
}
@media (max-width: 767px) {
    .cta2 .cta2-bg .subscribe-area {
        margin-right: 0;
    }
}
.cta2 .cta2-bg .subscribe-area input {
    padding: 18px;
    border: none;
    border-radius: 4px;
    width: 100%;
}
.cta2 .cta2-bg .subscribe-area input:focus {
    outline: none;
}
.cta2 .cta2-bg .subscribe-area .button {
    position: absolute;
    top: 4px;
    right: 4px;
}

.footer-cta5 {
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
}

.cta5 {
    padding-top: 60px;
}
.cta5 .cta-border {
    border-bottom: 1px solid rgba(255, 255, 255, 0.301);
    padding-bottom: 60px;
}
.cta5 .subscribe-area {
    text-align: end;
    position: relative;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .cta5 .subscribe-area {
        margin-top: 24px;
    }
}
@media (max-width: 767px) {
    .cta5 .subscribe-area {
        margin-top: 24px;
    }
}
.cta5 .subscribe-area input {
    padding: 18px;
    border: none;
    border-radius: 4px;
    width: 80%;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .cta5 .subscribe-area input {
        width: 100%;
    }
}
@media (max-width: 767px) {
    .cta5 .subscribe-area input {
        width: 100%;
    }
}
.cta5 .subscribe-area input:focus {
    outline: none;
}
.cta5 .subscribe-area input::-moz-placeholder {
    color: #696b6d;
}
.cta5 .subscribe-area input::placeholder {
    color: #696b6d;
}
.cta5 .subscribe-area .button {
    position: absolute;
    top: 5px;
    right: 5px;
}

.cta6 {
    background-color: #325d28;
}
.cta6 .images {
    text-align: end;
    position: relative;
}
.cta6 .images .img1 {
    position: absolute;
    top: 75px;
    right: 0;
}
.cta6 .images .img2 {
    position: absolute;
    top: 3px;
    right: 0px;
}
.cta6 .heading6-w {
    padding: 60px 0px;
}
.cta6 .heading6-w .form-area {
    position: relative;
    margin-top: 30px;
    margin-right: 40px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .cta6 .heading6-w .form-area {
        margin-right: 0;
    }
}
@media (max-width: 767px) {
    .cta6 .heading6-w .form-area {
        margin-right: 0;
    }
}
.cta6 .heading6-w .form-area .button {
    position: absolute;
    bottom: 5px;
    right: 5px;
}
.cta6 .heading6-w .form-area input {
    width: 100%;
    border-radius: 8px;
    border: none;
    padding: 20px;
}
.cta6 .heading6-w .form-area input:focus {
    outline: none;
}
.cta6 .heading6-w .form-area input::-moz-placeholder {
    color: var(--Paragraph-Color, #646375);
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 18px; /* 100% */
}
.cta6 .heading6-w .form-area input::placeholder {
    color: var(--Paragraph-Color, #646375);
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 18px; /* 100% */
}

.cta7 {
    background-size: cover;
    background-position: center bottom;
    background-repeat: no-repeat;
    padding: 80px 0px;
}
.cta7 .heading6-w .form-area {
    position: relative;
    margin-top: 30px;
    margin-right: 40px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .cta7 .heading6-w .form-area {
        margin-right: 0;
    }
}
@media (max-width: 767px) {
    .cta7 .heading6-w .form-area {
        margin-right: 0;
    }
}
.cta7 .heading6-w .form-area .button {
    position: absolute;
    bottom: 5px;
    right: 5px;
}
.cta7 .heading6-w .form-area input {
    width: 100%;
    border-radius: 8px;
    border: none;
    padding: 20px;
}
.cta7 .heading6-w .form-area input:focus {
    outline: none;
}
.cta7 .heading6-w .form-area input::-moz-placeholder {
    color: var(--Paragraph-Color, #646375);
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 18px; /* 100% */
}
.cta7 .heading6-w .form-area input::placeholder {
    color: var(--Paragraph-Color, #646375);
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 18px; /* 100% */
}
.cta7 .cta-contact-area .contact-box {
    padding: 24px;
    border-radius: 8px;
    background-color: rgba(255, 255, 255, 0.1294117647);
    display: flex;
    align-items: center;
    transition: all 0.4s;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .cta7 .cta-contact-area .contact-box {
        margin-top: 30px;
    }
}
@media (max-width: 767px) {
    .cta7 .cta-contact-area .contact-box {
        margin-top: 30px;
    }
}
.cta7 .cta-contact-area .contact-box:hover {
    transition: all 0.4s;
    transform: translateY(-5px);
}
.cta7 .cta-contact-area .contact-box .icon {
    height: 60px;
    width: 60px;
    text-align: center;
    line-height: 60px;
    background-color: #fff;
    border-radius: 50%;
}
.cta7 .cta-contact-area .contact-box .heading {
    padding-left: 20px;
}
.cta7 .cta-contact-area .contact-box .heading h6 {
    color: rgba(255, 255, 255, 0.8078431373);
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 16px; /* 100% */
}
.cta7 .cta-contact-area .contact-box .heading a {
    display: inline-block;
    color: #fff;
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px; /* 100% */
    padding-top: 13px;
    transition: all 0.4s;
}
.cta7 .cta-contact-area .contact-box .heading a:hover {
    color: #5957e5;
}

.cta8 {
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
    padding: 80px 0px;
}
.cta8 .heading6-w .form-area {
    position: relative;
    margin: 30px 30px 0px 30px;
}
.cta8 .heading6-w .form-area input {
    padding: 19px;
    border: none;
    border-radius: 8px;
    width: 100%;
}
.cta8 .heading6-w .form-area input:focus {
    outline: none;
}
.cta8 .heading6-w .form-area input::-moz-placeholder {
    color: #555;
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 18px; /* 100% */
}
.cta8 .heading6-w .form-area input::placeholder {
    color: #555;
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 18px; /* 100% */
}
.cta8 .heading6-w .form-area .button {
    position: absolute;
    right: 4px;
    top: 4px;
}

.cta9 {
    background-color: #2a9134;
}
.cta9 .images {
    text-align: end;
    position: relative;
}
.cta9 .images .img1 {
    position: absolute;
    top: 75px;
    right: 0;
}
.cta9 .images .img2 {
    position: absolute;
    top: 3px;
    right: 0px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .cta9 .images .img2 {
        position: static;
    }
}
@media (max-width: 767px) {
    .cta9 .images .img2 {
        position: static;
    }
}
.cta9 .heading6-w {
    padding: 60px 0px;
}
.cta9 .heading6-w .form-area {
    position: relative;
    margin-top: 30px;
    margin-right: 40px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .cta9 .heading6-w .form-area {
        margin-right: 0;
    }
}
@media (max-width: 767px) {
    .cta9 .heading6-w .form-area {
        margin-right: 0;
    }
}
.cta9 .heading6-w .form-area .button {
    position: absolute;
    bottom: 5px;
    right: 5px;
}
.cta9 .heading6-w .form-area input {
    width: 100%;
    border-radius: 8px;
    border: none;
    padding: 20px;
}
.cta9 .heading6-w .form-area input:focus {
    outline: none;
}
.cta9 .heading6-w .form-area input::-moz-placeholder {
    color: var(--Paragraph-Color, #646375);
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 18px; /* 100% */
}
.cta9 .heading6-w .form-area input::placeholder {
    color: var(--Paragraph-Color, #646375);
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 18px; /* 100% */
}

/*
::::::::::::::::::::::::::
 CTA AREA CSS
::::::::::::::::::::::::::
*/
/*
::::::::::::::::::::::::::
 FAQ AREA CSS
::::::::::::::::::::::::::
*/
.faq-all-area .accordion-item.active {
    background-color: var(--vtc-bg-main-bg-1) !important;
    transition: all 0.4s;
}
.faq-all-area .accordion-item.active button {
    background-color: var(--vtc-bg-main-bg-1) !important;
    color: #f5f3f4 !important;
    transition: all 0.4s;
}
.faq-all-area .accordion-item.active .accordion-body {
    color: rgba(255, 255, 255, 0.6980392157) !important;
}
.faq-all-area .accordion-item.active .accordion-button:not(.collapsed)::after {
    background-image: var(--bs-accordion-btn-active-icon);
    transform: var(--bs-accordion-btn-icon-transform);
    filter: brightness(0) invert(1);
}
.faq-all-area .accordion {
    border: none;
}
.faq-all-area .accordion .accordion-item {
    margin-top: 20px;
    border: none;
    border-radius: 7px;
    padding: 2px;
    border: none;
    background-color: #f5f3f4;
}
.faq-all-area .accordion .accordion-item button {
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs18);
    font-weight: var(--f-fw-semibold);
    color: var(--ztc-text-text-1);
    padding: 20px;
    background-color: #f5f3f4;
}
.faq-all-area .accordion .accordion-item button:focus {
    box-shadow: none;
}
.faq-all-area .accordion .accordion-item .accordion-body {
    font-size: var(--f-fs-font-fs16);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-regular);
    color: var(--ztc-text-pera-text-1);
    padding: 0px 16px 16px 16px;
}
.faq-all-area .accordion .accordion-button:not(.collapsed)::after {
    background-image: var(--bs-accordion-btn-active-icon);
    transform: var(--bs-accordion-btn-icon-transform);
    filter: brightness(0);
}
.faq-all-area .accordion-button:not(.collapsed) {
    color: var(--ztc-text-text-1);
    background-color: #f5f3f4;
    border: none;
    box-shadow: inset 0 calc(var(--bs-accordion-border-width) * -1) 0 var(--bs-accordion-border-color);
}
.faq-all-area .accordion {
    --bs-accordion-color: #000;
    --bs-accordion-bg: #fff;
    --bs-accordion-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, border-radius 0.15s ease;
    --bs-accordion-border-color: #dee2e600;
    --bs-accordion-border-width: 1px;
    --bs-accordion-border-radius: 0.375rem;
    --bs-accordion-inner-border-radius: calc(0.375rem - 1px);
    --bs-accordion-btn-padding-x: 1.25rem;
    --bs-accordion-btn-padding-y: 1rem;
    --bs-accordion-btn-color: var(--bs-body-color);
    --bs-accordion-btn-bg: var(--bs-accordion-bg);
    --bs-accordion-btn-icon: url(data:image/svg + xml, %3csvgxmlns="http://www.w3.org/2000/svg"viewBox="0 0 16 16"fill="var%28--bs-body-color%29"%3e%3cpathfill-rule="evenodd"d="M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z"/%3e%3c/svg%3e);
    --bs-accordion-btn-icon-width: 1.25rem;
    --bs-accordion-btn-icon-transform: rotate(-180deg);
    --bs-accordion-btn-icon-transition: transform 0.2s ease-in-out;
    --bs-accordion-btn-active-icon: url(data:image/svg + xml, %3csvgxmlns="http://www.w3.org/2000/svg"viewBox="0 0 16 16"fill="%230c63e4"%3e%3cpathfill-rule="evenodd"d="M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z"/%3e%3c/svg%3e);
    --bs-accordion-btn-focus-border-color: #86b7fe;
    --bs-accordion-btn-focus-box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    --bs-accordion-body-padding-x: 1.25rem;
    --bs-accordion-body-padding-y: 1rem;
    --bs-accordion-active-color: #0c63e4;
    --bs-accordion-active-bg: #e7f1ff;
}

/*
::::::::::::::::::::::::::
 FAQ AREA CSS
::::::::::::::::::::::::::
*/
/*
::::::::::::::::::::::::::
 OTHERS AREA CSS
::::::::::::::::::::::::::
*/
.hero1-slider {
    padding: 50px 0px;
    background-color: var(--vtc-text-heading-text-1);
}

.hero9-slider {
    padding: 50px 0px;
    background-color: #f3f5f2;
}
.hero9-slider .single-slider {
    margin: 0px 20px;
}

.logo-slider3 .single-slider {
    margin: 0px 10px;
}

.logo-slider4 {
    margin-top: 40px;
}
.logo-slider4 .single-slider {
    margin: 0px 10px;
}

.logo-slider-area .slider-pera {
    margin-bottom: 60px;
    position: relative;
    background-color: #fff;
    z-index: 2;
    text-align: center;
}
.logo-slider-area .slider-pera p {
    color: var(--vtc-text-heading-text-1);
    text-align: center;
    font-weight: var(--f-fw-bold);
    font-size: var(--f-fs-font-fs20);
    line-height: var(--f-fs-font-fs20);
    background-color: #fff;
    padding: 0px 40px;
    display: inline-block;
}
.logo-slider-area .slider-pera::after {
    content: "";
    position: absolute;
    top: 10px;
    left: 0;
    height: 2px;
    width: 100%;
    background-color: var(--vtc-bg-common-bg4);
    z-index: -1;
}

.solution4-box {
    margin-top: 34px;
    background-color: var(--vtc-bg-common-bg7);
    border-radius: 4px;
    transition: all 0.4s;
}
.solution4-box .image {
    overflow: hidden;
    border-radius: 4px 4px 0px 0px;
}
.solution4-box .image img {
    width: 100%;
    transition: all 0.4s;
}
.solution4-box .icon {
    height: 60px;
    width: 60px;
    line-height: 60px;
    text-align: center;
    border-radius: 50%;
    background-color: #fff;
    margin-top: -30px;
    margin-left: 24px;
    position: relative;
    z-index: 2;
    transition: all 0.4s;
}
.solution4-box .icon img {
    transition: all 0.4s;
}
.solution4-box .heading4 {
    padding: 24px;
}
.solution4-box:hover {
    transform: translateY(-10px);
    transition: all 0.4s;
}
.solution4-box:hover .image img {
    transition: all 0.4s;
    transform: rotate(2deg) scale(1.1);
}
.solution4-box:hover .icon {
    background-color: var(--vtc-bg-main-bg-6);
    transition: all 0.4s;
    transform: rotateY(180deg);
}
.solution4-box:hover .icon img {
    transition: all 0.4s;
    filter: brightness(40);
}

.video-area5 {
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    height: 800px;
}
.video-area5 .video-area-button {
    margin-top: 200px;
}
.video-area5 .video-area-button a {
    display: inline-block;
    height: 100px;
    width: 100px;
    background-color: #fff;
    border-radius: 50%;
    text-align: center;
    line-height: 100px;
    font-size: 24px;
    color: var(--vtc-bg-main-bg-2);
    transition: all 0.4s;
}
.video-area5 .video-area-button a:hover {
    color: #fff;
    background-color: var(--vtc-bg-main-bg-2);
}
.video-area5 .video-area-button p {
    color: #fff;
    font-size: var(--f-fs-font-fs24);
    line-height: var(--f-fs-font-fs24);
    font-weight: var(--f-fw-bold);
    padding-top: 24px;
}

.others6 {
    position: relative;
}
.others6 .others-boxs {
    padding: 20px;
    margin-top: 30px;
    background-color: #f3f5f2;
    border-radius: 8px;
    display: flex;
    align-items: center;
    transition: all 0.4s;
    margin-right: 30px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .others6 .others-boxs {
        margin-right: 0px;
    }
}
@media (max-width: 767px) {
    .others6 .others-boxs {
        margin-right: 0px;
    }
}
.others6 .others-boxs .icon {
    height: 80px;
    width: 80px;
    text-align: center;
    line-height: 80px;
    background-color: #fff;
    border-radius: 50%;
}
.others6 .others-boxs .icon img {
    transition: all 0.4s;
}
.others6 .others-boxs .heading6 {
    padding-left: 20px;
}
.others6 .others-boxs .heading6 p {
    padding-top: 14px;
}
.others6 .others-boxs:hover {
    transform: translateY(-10px);
}
.others6 .others-boxs:hover .icon img {
    transition: all 0.4s;
    transform: rotateY(180deg);
}
.others6 .images-all {
    height: 530px;
    position: relative;
    text-align: center;
}
.others6 .images-all .image2 {
    position: absolute;
    bottom: 0;
    left: 0;
}
.others6 .sec-shape {
    position: absolute;
    right: 0;
    top: 0;
    z-index: -2;
}

.industries7 .industries-box {
    margin-top: 30px;
    background-color: #f8f7ff;
    padding: 28px;
    border-radius: 8px;
    text-align: center;
    transition: all 0.4s;
}
.industries7 .industries-box h3 {
    color: #081120;
    font-size: 24px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px; /* 100% */
    text-transform: uppercase;
    transition: all 0.4s;
}
.industries7 .industries-box .bottom {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 14px;
}
.industries7 .industries-box .bottom .icons {
    margin-right: 12px;
}
.industries7 .industries-box .bottom .icons img {
    transition: all 0.4s;
}
.industries7 .industries-box .bottom .pera p {
    color: #555;
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: 18px; /* 100% */
    transition: all 0.4s;
}
.industries7 .industries-box:hover {
    transform: translateY(-10px);
    background-color: #5957e5;
    transition: all 0.4s;
}
.industries7 .industries-box:hover h3 {
    transition: all 0.4s;
    color: #fff;
}
.industries7 .industries-box:hover .bottom .icons img {
    transition: all 0.4s;
    filter: brightness(0) invert(1);
}
.industries7 .industries-box:hover .bottom .pera p {
    transition: all 0.4s;
    color: rgba(255, 255, 255, 0.7607843137);
}

.others8 {
    position: relative;
}
.others8 .others-boxs {
    padding: 20px;
    margin-top: 30px;
    background-color: #f7f5fb;
    border-radius: 8px;
    display: flex;
    align-items: center;
    transition: all 0.4s;
    margin-right: 30px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .others8 .others-boxs {
        margin-right: 0px;
    }
}
@media (max-width: 767px) {
    .others8 .others-boxs {
        margin-right: 0px;
    }
}
.others8 .others-boxs .icon {
    height: 80px;
    width: 80px;
    text-align: center;
    line-height: 80px;
    background-color: #fff;
    border-radius: 50%;
}
.others8 .others-boxs .icon img {
    transition: all 0.4s;
}
.others8 .others-boxs .heading8 {
    padding-left: 20px;
}
.others8 .others-boxs .heading8 h4 a {
    display: inline-block;
    color: #081120;
    font-size: 20px;
    font-style: normal;
    font-weight: 700;
    line-height: 20px; /* 100% */
    transition: all 0.4s;
}
.others8 .others-boxs .heading8 h4 a:hover {
    transition: all 0.4s;
    color: #f6aa32;
}
.others8 .others-boxs .heading8 p {
    padding-top: 14px;
}
.others8 .others-boxs:hover {
    transform: translateY(-10px);
}
.others8 .others-boxs:hover .icon img {
    transition: all 0.4s;
    transform: rotateY(180deg);
}
.others8 .images-all {
    height: 530px;
    position: relative;
    text-align: center;
    margin-top: 30px;
    margin-left: 50px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .others8 .images-all {
        margin-left: 0px;
    }
}
@media (max-width: 767px) {
    .others8 .images-all {
        margin-left: 0px;
    }
}
.others8 .images-all .image2 {
    position: absolute;
    bottom: 0;
    left: 0;
}
.others8 .sec-shape {
    position: absolute;
    right: 0;
    top: 0;
    z-index: -2;
}

.solutions9 .solutions-box {
    margin-top: 30px;
    background-color: #fff;
    border-radius: 8px;
    text-align: center;
    transition: all 0.4s;
}
.solutions9 .solutions-box .heading-area {
    position: relative;
}
.solutions9 .solutions-box .heading-area .icon {
    background-color: #f3f5f2;
    height: 90px;
    width: 90px;
    border-radius: 50%;
    text-align: center;
    line-height: 90px;
    margin-top: -45px;
    position: absolute;
    z-index: 2;
    left: 50%;
    margin-left: -45px;
}
.solutions9 .solutions-box .heading-area .icon img {
    transition: all 0.4s;
}
.solutions9 .solutions-box .heading-area .heading {
    padding: 60px 24px 24px 24px;
}
.solutions9 .solutions-box .heading-area .heading h4 a {
    display: inline-block;
    color: #081120;
    font-size: 24px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px; /* 100% */
    transition: all 0.4s;
}
.solutions9 .solutions-box .heading-area .heading h4 a:hover {
    transition: all 0.4s;
    color: #2a9134;
}
.solutions9 .solutions-box .heading-area .heading p {
    color: #555;
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 28px; /* 155.556% */
    padding-top: 14px;
}
.solutions9 .solutions-box .image {
    overflow: hidden;
    border-radius: 8px 8px 0px 0px;
}
.solutions9 .solutions-box .image img {
    width: 100%;
    transition: all 0.4s;
}
.solutions9 .solutions-box:hover {
    transition: all 0.4s;
    transform: translateY(-10px);
}
.solutions9 .solutions-box:hover .image img {
    transform: scale(1.1) rotate(2deg);
    transition: all 0.4s;
}
.solutions9 .solutions-box:hover .heading-area .icon img {
    transition: all 0.4s;
    transform: rotateY(180deg);
}

/*
::::::::::::::::::::::::::
 OTHERS AREA CSS
::::::::::::::::::::::::::
*/
/*
::::::::::::::::::::::::::
 OTHERS AREA CSS
::::::::::::::::::::::::::
*/
/*======== pricing-plan-start ==========*/
.toggle-inner {
    width: 75px;
    margin: 0 auto;
    height: 35px;
    border: 1px solid #0e1124;
    background: var(--vtc-bg-main-bg-2);
    border-radius: 25px;
    position: relative;
}

.toggle-inner input {
    position: absolute;
    left: 0;
    width: 100%;
    height: 100%;
    margin: 0;
    border-radius: 25px;
    right: 0;
    z-index: 1;
    opacity: 0;
    cursor: pointer;
}

.custom-toggle {
    position: absolute;
    height: 25px;
    width: 25px;
    background-color: #ffffff;
    top: 4px;
    left: 5px;
    border-radius: 50%;
    transition: 300ms all;
}

.toggle-inner .t-month,
.toggle-inner .t-year {
    position: absolute;
    left: -70px;
    top: 8px;
    color: #fff;
    transition: 300ms all;
    font-size: 16px;
    font-weight: 500;
    line-height: 16px;
}

.toggle-inner {
    margin-top: 30px;
}

.toggle-inner .t-year {
    left: unset;
    right: -140px;
    opacity: 0.5;
}

.active > .toggle-inner .t-month {
    opacity: 0.5;
}

.active > .toggle-inner .t-year {
    opacity: 1;
}

.toggle-inner input:checked + span {
    left: 43px;
}

.toggle-inner {
    width: 75px;
    margin: 0 auto;
    height: 35px;
    border: 1px solid var(--vtc-bg-main-bg-2);
    border-radius: 25px;
    position: relative;
}
.pricing-box {
    padding: 32px 24px;
    border-radius: 4px;
    border: 1px solid rgba(16, 0, 43, 0.15);
    position: relative;
    margin-top: 40px;
}
.pricing-box h5 {
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs18);
    color: #f6f8f9;
    font-weight: var(--f-fw-bold);
    margin-bottom: 16px;
    transition: all 0.4s;
}
.pricing-box h3 {
    font-size: var(--f-fs-font-fs44);
    line-height: var(--f-fs-font-fs44);
    color: #ffffff;
    font-weight: var(--f-fw-bold);
    padding: 30px 0px 16px 0px;
    transition: all 0.4s;
}
.pricing-box .pricing-btn {
    width: 100%;
    border-radius: 8px;
    padding: 22px;
    background-color: var(--vtc-text-heading-text-1);
    display: inline-block;
    text-align: center;
    font-size: var(--f-fs-font-fs16);
    line-height: var(--f-fs-font-fs16);
    font-weight: var(--f-fw-bold);
    color: var(--vtc-text-text-white-text-1);
    margin: 32px 0px;
    transition: all 0.4s;
}
.pricing-box .pricing-btn:hover {
    transform: translateY(-5px);
    transition: all 0.4s;
}
.pricing-box .h-pera {
    font-size: var(--f-fs-font-fs20);
    line-height: var(--f-fs-font-fs20);
    color: var(--vtc-text-text-white-text-1);
    font-weight: var(--f-fw-medium);
    transition: all 0.4s;
}
.pricing-box .list {
    margin-top: 10px;
}
.pricing-box .list li {
    color: #a9aabf;
    font-size: var(--f-fs-font-fs16);
    line-height: var(--f-fs-font-fs20);
    font-weight: var(--f-fw-medium);
    padding-top: 14px;
    display: flex;
    align-items: center;
    transition: all 0.4s;
}
.pricing-box .list li span {
    display: inline-block;
    height: 14px;
    width: 14px;
    line-height: 12px;
    font-size: 8px;
    border: 1px solid #fff;
    color: #fff;
    text-align: center;
    border-radius: 50%;
    margin-right: 8px;
    transition: all 0.4s;
}
.pricing-box::after {
    content: "";
    position: absolute;
    height: 100%;
    width: 100%;
    border-radius: 4px;
    top: 0;
    left: 0;
    transform: rotateY(95deg);
    transition: all 0.4s linear;
    background-color: #fff;
    z-index: 5;
    opacity: 0;
}

.pricing-box-single {
    position: relative;
    z-index: 31;
}
.pricing-box::before {
    content: "";
    position: absolute;
    height: 100%;
    width: 100%;
    border-radius: 4px;
    top: 0;
    left: 0;
    transition: all 0.6s linear;
    background-color: #282a5e;
    z-index: 1;
}
.pricing-box:hover::after {
    opacity: 1;
    transform: rotateY(0deg);
    transition: all 0.4s linear;
}
.pricing-box:hover h5 {
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs18);
    color: var(--vtc-text-heading-text-1);
    font-weight: var(--f-fw-bold);
    margin-bottom: 16px;
}
.pricing-box:hover p {
    color: var(--vtc-text-pera-text-1);
}
.pricing-box:hover h3 {
    font-size: var(--f-fs-font-fs44);
    line-height: var(--f-fs-font-fs44);
    color: var(--vtc-text-heading-text-1);
    font-weight: var(--f-fw-bold);
    padding: 30px 0px 16px 0px;
}
.pricing-box:hover .h-pera {
    font-size: var(--f-fs-font-fs20);
    line-height: var(--f-fs-font-fs20);
    color: var(--vtc-text-heading-text-1);
    font-weight: var(--f-fw-medium);
    transition: all 0.4s;
}
.pricing-box:hover .pricing-btn {
    width: 100%;
    border-radius: 8px;
    padding: 22px;
    background-color: var(--vtc-bg-main-bg-2);
    display: inline-block;
    text-align: center;
    font-size: var(--f-fs-font-fs16);
    line-height: var(--f-fs-font-fs16);
    font-weight: var(--f-fw-bold);
    color: var(--vtc-text-text-white-text-1);
    margin: 32px 0px;
    transition: all 0.4s;
}
.pricing-box:hover .list {
    margin-top: 10px;
}
.pricing-box:hover .list li {
    color: #5b5d5f;
    font-size: var(--f-fs-font-fs16);
    line-height: var(--f-fs-font-fs20);
    font-weight: var(--f-fw-medium);
    padding-top: 14px;
    display: flex;
    align-items: center;
}
.pricing-box:hover .list li span {
    display: inline-block;
    height: 14px;
    width: 14px;
    line-height: 12px;
    font-size: 8px;
    border: 1px solid var(--vtc-text-heading-text-1);
    color: var(--vtc-text-heading-text-1);
    text-align: center;
    border-radius: 50%;
    margin-right: 8px;
}

.pricing-box.active::after {
    opacity: 1;
    transform: rotateY(0deg);
    transition: all 0.4s linear;
}
.pricing-box.active h5 {
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs18);
    color: var(--vtc-text-heading-text-1);
    font-weight: var(--f-fw-bold);
    margin-bottom: 16px;
}
.pricing-box.active p {
    color: var(--vtc-text-pera-text-1);
}
.pricing-box.active h3 {
    font-size: var(--f-fs-font-fs44);
    line-height: var(--f-fs-font-fs44);
    color: var(--vtc-text-heading-text-1);
    font-weight: var(--f-fw-bold);
    padding: 30px 0px 16px 0px;
}
.pricing-box.active .h-pera {
    font-size: var(--f-fs-font-fs20);
    line-height: var(--f-fs-font-fs20);
    color: var(--vtc-text-heading-text-1);
    font-weight: var(--f-fw-medium);
    transition: all 0.4s;
}
.pricing-box.active .pricing-btn {
    width: 100%;
    border-radius: 8px;
    padding: 22px;
    background-color: var(--vtc-bg-main-bg-2);
    display: inline-block;
    text-align: center;
    font-size: var(--f-fs-font-fs16);
    line-height: var(--f-fs-font-fs16);
    font-weight: var(--f-fw-bold);
    color: var(--vtc-text-text-white-text-1);
    margin: 32px 0px;
    transition: all 0.4s;
}
.pricing-box.active .list {
    margin-top: 10px;
}
.pricing-box.active .list li {
    color: #5b5d5f;
    font-size: var(--f-fs-font-fs16);
    line-height: var(--f-fs-font-fs20);
    font-weight: var(--f-fw-medium);
    padding-top: 14px;
    display: flex;
    align-items: center;
}
.pricing-box.active .list li span {
    display: inline-block;
    height: 14px;
    width: 14px;
    line-height: 12px;
    font-size: 8px;
    border: 1px solid var(--vtc-text-heading-text-1);
    color: var(--vtc-text-heading-text-1);
    text-align: center;
    border-radius: 50%;
    margin-right: 8px;
}

/*======== pricing-plan-end ==========*/
/*======== pricing-plan-2 start ==========*/
.pricing-plan-page2 .toggle-inner .t-month,
.pricing-plan-page2 .toggle-inner .t-year {
    position: absolute;
    left: -70px;
    top: 8px;
    color: #001431;
    transition: 300ms all;
    font-size: 16px;
    font-weight: 500;
    line-height: 16px;
}
.pricing-plan-page2 .toggle-inner .t-year {
    left: unset;
    right: -140px;
    opacity: 0.5;
}
.pricing-plan-page2 .active > .toggle-inner .t-month {
    opacity: 0.5;
}
.pricing-plan-page2 .active > .toggle-inner .t-year {
    opacity: 1;
}

.pricing-box2 {
    padding: 32px 24px;
    border-radius: 4px;
    position: relative;
    margin-top: 40px;
}
.pricing-box2 h5 {
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs18);
    color: var(--vtc-text-heading-text-3);
    font-weight: var(--f-fw-bold);
    margin-bottom: 16px;
    transition: all 0.4s;
}
.pricing-box2 h3 {
    font-size: var(--f-fs-font-fs44);
    line-height: var(--f-fs-font-fs44);
    color: var(--vtc-text-heading-text-3);
    font-weight: var(--f-fw-bold);
    padding: 30px 0px 16px 0px;
    transition: all 0.4s;
}
.pricing-box2 .heading2 p {
    color: var(---vtc-text-pera-text-1);
}
.pricing-box2 .pricing-btn {
    width: 100%;
    border-radius: 8px;
    padding: 22px;
    background-color: var(--vtc-text-heading-text-1);
    display: inline-block;
    text-align: center;
    font-size: var(--f-fs-font-fs16);
    line-height: var(--f-fs-font-fs16);
    font-weight: var(--f-fw-bold);
    color: var(--vtc-text-text-white-text-1);
    margin: 32px 0px;
    transition: all 0.4s;
}
.pricing-box2 .pricing-btn:hover {
    transform: translateY(-5px);
    transition: all 0.4s;
}
.pricing-box2 .h-pera {
    font-size: var(--f-fs-font-fs20);
    line-height: var(--f-fs-font-fs20);
    color: var(---vtc-text-pera-text-3);
    font-weight: var(--f-fw-medium);
    transition: all 0.4s;
}
.pricing-box2 .list {
    margin-top: 10px;
}
.pricing-box2 .list li {
    color: var(--vtc-text-pera-text-3);
    font-size: var(--f-fs-font-fs16);
    line-height: var(--f-fs-font-fs20);
    font-weight: var(--f-fw-medium);
    padding-top: 14px;
    display: flex;
    align-items: center;
    transition: all 0.4s;
}
.pricing-box2 .list li span {
    display: inline-block;
    height: 14px;
    width: 14px;
    line-height: 12px;
    font-size: 8px;
    border: 1px solid var(--vtc-text-heading-text-1);
    color: var(--vtc-text-heading-text-1);
    text-align: center;
    border-radius: 50%;
    margin-right: 8px;
    transition: all 0.4s;
}
.pricing-box2::after {
    content: "";
    position: absolute;
    height: 100%;
    width: 100%;
    border-radius: 4px;
    top: 0;
    left: 0;
    transform: rotateY(95deg);
    transition: all 0.4s linear;
    background-color: #f2f4f7;
    z-index: -2;
    opacity: 0;
}
.pricing-box2::before {
    content: "";
    position: absolute;
    height: 100%;
    width: 100%;
    border-radius: 4px;
    top: 0;
    left: 0;
    transition: all 0.6s linear;
    background-color: var(--vtc-bg-common-bg9);
    z-index: -3;
}
.pricing-box2:hover::after {
    opacity: 1;
    transform: rotateY(0deg);
    transition: all 0.4s linear;
}
.pricing-box2:hover h5 {
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs18);
    color: var(--vtc-text-heading-text-1);
    font-weight: var(--f-fw-bold);
    margin-bottom: 16px;
}
.pricing-box2:hover p {
    color: var(--vtc-text-pera-text-1);
}
.pricing-box2:hover h3 {
    font-size: var(--f-fs-font-fs44);
    line-height: var(--f-fs-font-fs44);
    color: var(--vtc-text-heading-text-1);
    font-weight: var(--f-fw-bold);
    padding: 30px 0px 16px 0px;
}
.pricing-box2:hover .h-pera {
    font-size: var(--f-fs-font-fs20);
    line-height: var(--f-fs-font-fs20);
    color: var(--vtc-text-heading-text-1);
    font-weight: var(--f-fw-medium);
    transition: all 0.4s;
}
.pricing-box2:hover .pricing-btn {
    width: 100%;
    border-radius: 8px;
    padding: 22px;
    background-color: var(--vtc-bg-main-bg-2);
    display: inline-block;
    text-align: center;
    font-size: var(--f-fs-font-fs16);
    line-height: var(--f-fs-font-fs16);
    font-weight: var(--f-fw-bold);
    color: var(--vtc-text-text-white-text-1);
    margin: 32px 0px;
    transition: all 0.4s;
}
.pricing-box2:hover .list {
    margin-top: 10px;
}
.pricing-box2:hover .list li {
    color: #5b5d5f;
    font-size: var(--f-fs-font-fs16);
    line-height: var(--f-fs-font-fs20);
    font-weight: var(--f-fw-medium);
    padding-top: 14px;
    display: flex;
    align-items: center;
}
.pricing-box2:hover .list li span {
    display: inline-block;
    height: 14px;
    width: 14px;
    line-height: 12px;
    font-size: 8px;
    border: 1px solid var(--vtc-text-heading-text-1);
    color: var(--vtc-text-heading-text-1);
    text-align: center;
    border-radius: 50%;
    margin-right: 8px;
}

.pricing-box2.active .pricing-btn {
    width: 100%;
    border-radius: 8px;
    padding: 22px;
    background-color: var(--vtc-bg-main-bg-2);
    display: inline-block;
    text-align: center;
    font-size: var(--f-fs-font-fs16);
    line-height: var(--f-fs-font-fs16);
    font-weight: var(--f-fw-bold);
    color: var(--vtc-text-text-white-text-1);
    margin: 32px 0px;
    transition: all 0.4s;
}

/*======== pricing-plan-2 end ==========*/
/*
::::::::::::::::::::::::::
 OTHERS AREA CSS
::::::::::::::::::::::::::
*/
/*
 ::::::::::::::::::::::::::
  CONTACT AREA CSS
 ::::::::::::::::::::::::::
 */
.contact1 {
    background-color: var(--vtc-text-heading-text-1);
}
.contact1 .heading1-w {
    padding-right: 60px;
}
.contact1 .contact1-box {
    background-color: var(--vtc-bg-bg-white);
    padding: 24px 32px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    margin-top: 30px;
    transition: all 0.4s;
}
.contact1 .contact1-box:hover {
    transition: all 0.4s;
    transform: translateY(-10px);
}
.contact1 .contact1-box .icon {
    height: 70px;
    line-height: 80px;
    width: 70px;
    text-align: center;
    background-color: var(--vtc-bg-common-bg1);
    border-radius: 50%;
    margin-right: 20px;
}
.contact1 .contact1-box .heading p {
    color: var(--vtc-text-pera-text-1);
    font-weight: var(--f-fw-medium);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs18);
}
.contact1 .contact1-box .heading a {
    display: inline-block;
    font-size: var(--f-fs-font-fs24);
    line-height: var(--f-fs-font-fs24);
    font-weight: var(--f-fw-bold);
    color: var(--vtc-text-heading-text-1);
    padding-top: 16px;
    transition: all 0.4s;
}
.contact1 .contact1-box .heading a:hover {
    color: var(--vtc-bg-main-bg-1);
    transition: all 0.4s;
}
.contact1 .contact1-form {
    background-color: var(--vtc-bg-bg-white);
    border-radius: 4px;
    padding: 32px;
}
.contact1 .contact1-form .heading1 h3 {
    font-size: var(--f-fs-font-fs24);
    line-height: var(--f-fs-font-fs24);
    font-weight: var(--f-fw-bold);
    color: var(--vtc-text-heading-text-1);
}
.contact1 .contact1-form .heading1 h5 {
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs18);
    font-weight: var(--f-fw-bold);
    color: var(--vtc-text-heading-text-1);
    padding-top : 16px;
}
.contact1 .contact1-form .single-input input,
.contact1 .contact1-form .single-input textarea {
    border: none;
    border-radius: 4px;
    padding: 16px;
    border: 1px solid #e6e7e9;
    width: 100%;
    margin-top: 20px;
    color: #525863;
    font-weight: var(--f-fw-medium);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs18);
}
.contact1 .contact1-form .single-input input:focus,
.contact1 .contact1-form .single-input textarea:focus {
    outline: none;
}
.contact1 .contact1-form .single-input input::-moz-placeholder,
.contact1 .contact1-form .single-input textarea::-moz-placeholder {
    color: #525863;
    font-weight: var(--f-fw-medium);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs18);
}
.contact1 .contact1-form .single-input input::placeholder,
.contact1 .contact1-form .single-input textarea::placeholder {
    color: #525863;
    font-weight: var(--f-fw-medium);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs18);
}
.contact1 .contact1-form .button {
    text-align: end;
    margin-top: 20px;
}

.contact3 {
    background-color: var(--vtc-bg-common-bg4);
    position: relative;
}
.contact3 .contact3-box {
    background-color: var(--vtc-bg-bg-white);
    border-radius: 4px;
    padding: 24px 32px;
    display: flex;
    align-items: center;
    margin-top: 24px;
}
.contact3 .contact3-box .icon {
    background-color: var(--vtc-bg-common-bg4);
    height: 70px;
    width: 70px;
    line-height: 70px;
    text-align: center;
    border-radius: 50%;
}
.contact3 .contact3-box .heading3 {
    padding-left: 16px;
}
.contact3 .contact3-box .heading3 h6 {
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs18);
    font-weight: var(--f-fw-medium);
    color: #23342e;
    padding-bottom: 12px;
}
.contact3 .contact3-form {
    background-color: var(--vtc-bg-main-bg-4);
    border-radius: 4px;
    padding: 32px;
    margin-left: 30px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .contact3 .contact3-form {
        margin-left: 0;
        margin-top: 30px;
    }
}
@media (max-width: 767px) {
    .contact3 .contact3-form {
        margin-left: 0;
        margin-top: 30px;
    }
}
.contact3 .contact3-form .heading1 h3 {
    font-size: var(--f-fs-font-fs24);
    line-height: var(--f-fs-font-fs24);
    font-weight: var(--f-fw-bold);
    color: var(--vtc-text-heading-text-1);
}
.contact3 .contact3-form .single-input input,
.contact3 .contact3-form .single-input textarea {
    border: none;
    border-radius: 4px;
    padding: 16px;
    border: none;
    width: 100%;
    margin-top: 20px;
    background-color: #394843;
    color: #c4c8c7;
    font-weight: var(--f-fw-medium);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs18);
}
.contact3 .contact3-form .single-input input:focus,
.contact3 .contact3-form .single-input textarea:focus {
    outline: none;
}
.contact3 .contact3-form .single-input input::-moz-placeholder,
.contact3 .contact3-form .single-input textarea::-moz-placeholder {
    color: #c4c8c7;
    font-weight: var(--f-fw-medium);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs18);
}
.contact3 .contact3-form .single-input input::placeholder,
.contact3 .contact3-form .single-input textarea::placeholder {
    color: #c4c8c7;
    font-weight: var(--f-fw-medium);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs18);
}
.contact3 .contact3-form .button {
    text-align: end;
    margin-top: 20px;
}
.contact3 .shape {
    position: absolute;
    top: 0;
    left: 0;
}

.contact4 .contact3-box {
    background-color: var(--vtc-bg-common-bg7);
    border-radius: 4px;
    padding: 24px 32px;
    display: flex;
    align-items: center;
    margin-top: 24px;
    transition: all 0.4s;
}
.contact4 .contact3-box .icon {
    background-color: var(--vtc-bg-bg-white);
    height: 70px;
    width: 70px;
    line-height: 70px;
    text-align: center;
    border-radius: 50%;
}
.contact4 .contact3-box .heading4 {
    padding-left: 16px;
}
.contact4 .contact3-box .heading4 h6 {
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs18);
    font-weight: var(--f-fw-medium);
    color: #23342e;
    padding-bottom: 12px;
}
.contact4 .contact3-box:hover {
    transition: all 0.4s;
    transform: translateY(-5px);
}
.contact4 .contact3-form {
    background-color: var(--vtc-bg-main-bg-5);
    border-radius: 4px;
    padding: 32px;
    margin-left: 30px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .contact4 .contact3-form {
        margin-left: 0;
        margin-top: 30px;
    }
}
@media (max-width: 767px) {
    .contact4 .contact3-form {
        margin-left: 0;
        margin-top: 30px;
    }
}
.contact4 .contact3-form .heading1 h3 {
    font-size: var(--f-fs-font-fs24);
    line-height: var(--f-fs-font-fs24);
    font-weight: var(--f-fw-bold);
    color: var(--vtc-text-heading-text-1);
}
.contact4 .contact3-form .single-input input,
.contact4 .contact3-form .single-input textarea {
    border: none;
    border-radius: 4px;
    padding: 16px;
    border: none;
    width: 100%;
    margin-top: 20px;
    background-color: #304679;
    color: var(--vtc-bg-bg-white);
}
.contact4 .contact3-form .single-input input:focus,
.contact4 .contact3-form .single-input textarea:focus {
    outline: none;
}
.contact4 .contact3-form .single-input input::-moz-placeholder,
.contact4 .contact3-form .single-input textarea::-moz-placeholder {
    color: #c4c8c7;
    font-weight: var(--f-fw-medium);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs18);
}
.contact4 .contact3-form .single-input input::placeholder,
.contact4 .contact3-form .single-input textarea::placeholder {
    color: #c4c8c7;
    font-weight: var(--f-fw-medium);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs18);
}
.contact4 .contact3-form .button {
    text-align: end;
    margin-top: 20px;
}
.contact4 .shape {
    position: absolute;
    top: 0;
    left: 0;
}

.contact5 {
    background-position: center center;
    background-size: cover;
    background-repeat: no-repeat;
}
.contact5 .heading5 span.span {
    background-color: #fff;
}
.contact5 .contact3-box {
    background-color: #fff;
    border-radius: 4px;
    padding: 24px 32px;
    display: flex;
    align-items: center;
    margin-top: 24px;
    transition: all 0.4s;
}
.contact5 .contact3-box .icon {
    background-color: #ffdee2;
    height: 70px;
    width: 70px;
    line-height: 70px;
    text-align: center;
    border-radius: 50%;
}
.contact5 .contact3-box .heading4 {
    padding-left: 16px;
}
.contact5 .contact3-box .heading4 h6 {
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs18);
    font-weight: var(--f-fw-medium);
    color: #23342e;
    padding-bottom: 12px;
}
.contact5 .contact3-box:hover {
    transition: all 0.4s;
    transform: translateY(-5px);
}
.contact5 .contact3-form {
    background-color: var(--vtc-text-heading-text-3);
    border-radius: 4px;
    padding: 32px;
    margin-left: 30px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .contact5 .contact3-form {
        margin-left: 0;
        margin-top: 30px;
    }
}
@media (max-width: 767px) {
    .contact5 .contact3-form {
        margin-left: 0;
        margin-top: 30px;
    }
}
.contact5 .contact3-form .heading1 h3 {
    font-size: var(--f-fs-font-fs24);
    line-height: var(--f-fs-font-fs24);
    font-weight: var(--f-fw-bold);
    color: var(--vtc-text-heading-text-1);
}
.contact5 .contact3-form .single-input input,
.contact5 .contact3-form .single-input textarea {
    border: none;
    border-radius: 4px;
    padding: 16px;
    border: none;
    width: 100%;
    margin-top: 20px;
    background-color: rgba(255, 255, 255, 0.1843137255);
    color: var(--vtc-bg-bg-white);
}
.contact5 .contact3-form .single-input input:focus,
.contact5 .contact3-form .single-input textarea:focus {
    outline: none;
}
.contact5 .contact3-form .single-input input::-moz-placeholder,
.contact5 .contact3-form .single-input textarea::-moz-placeholder {
    color: #c4c8c7;
    font-weight: var(--f-fw-medium);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs18);
}
.contact5 .contact3-form .single-input input::placeholder,
.contact5 .contact3-form .single-input textarea::placeholder {
    color: #c4c8c7;
    font-weight: var(--f-fw-medium);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs18);
}
.contact5 .contact3-form .button {
    text-align: end;
    margin-top: 20px;
}
.contact5 .shape {
    position: absolute;
    top: 0;
    left: 0;
}

.contact-page .contact-page-box {
    border-top: 1px solid #dadbdd;
    margin-top: 30px;
}
.contact-page .contact-page-box .contact-box {
    padding-top: 30px;
}
.contact-page .contact-page-box .contact-box h4 a {
    font-size: var(--f-fs-font-fs24);
    line-height: var(--f-fs-font-fs24);
    padding-top: 10px;
}
.contact-page .contact-page-box .contact-box .icon {
    background-color: #fff2e6;
    height: 70px;
    width: 70px;
    line-height: 70px;
    text-align: center;
    border-radius: 50%;
    margin-bottom: 16px;
}
.contact-page .contact-page-box .contact-box.contact-box2 {
    position: relative;
    padding-left: 0;
}
@media (max-width: 767px) {
    .contact-page .contact-page-box .contact-box.contact-box2 {
        padding-left: 0;
    }
}
.contact-page .contact-page-box .contact-box.contact-box2::after {
    content: "";
    position: absolute;
    top: 0;
    left: -50px;
    height: 100%;
    width: 1px;
    background-color: #dadbdd;
}
@media (max-width: 767px) {
    .contact-page .contact-page-box .contact-box.contact-box2::after {
        display: none;
    }
}
.contact-page .contact1-form {
    background-color: var(--vtc-bg-common-bg2);
    padding: 32px;
    border-radius: 4px;
    margin-left: 30px;
}
.contact-page .contact1-form .single-input input,
.contact-page .contact1-form .single-input textarea {
    background-color: #e9e8e9;
    border: none;
    width: 100%;
    margin-top: 20px;
    padding: 16px;
    color: #60656e;
    font-weight: var(--f-fw-medium);
    font-size: var(--f-fs-font-fs18);
}
.contact-page .contact1-form .single-input input::-moz-placeholder,
.contact-page .contact1-form .single-input textarea::-moz-placeholder {
    color: #60656e;
    font-weight: var(--f-fw-medium);
    font-size: var(--f-fs-font-fs18);
}
.contact-page .contact1-form .single-input input::placeholder,
.contact-page .contact1-form .single-input textarea::placeholder {
    color: #60656e;
    font-weight: var(--f-fw-medium);
    font-size: var(--f-fs-font-fs18);
}
.contact-page .contact1-form .single-input input:focus,
.contact-page .contact1-form .single-input textarea:focus {
    outline: none;
}
.contact-page .contact1-form .button {
    text-align: end;
    margin-top: 20px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .contact-page .contact1-form {
        margin-left: 0;
    }
}
@media (max-width: 767px) {
    .contact-page .contact1-form {
        margin-left: 0;
    }
}

.contact-map-page {
    margin-bottom: -8px;
}
.contact-map-page iframe {
    width: 100%;
}

.contact6 .contact9-content-area {
    border-radius: 8px;
    background: #fff;
    padding: 48px;
    margin-top: 60px;
}
.contact6 .contact9-content-area .form-area h3 {
    color: #0f0a07;
    font-size: 24px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px; /* 100% */
}
.contact6 .contact9-content-area .form-area p {
    color: #5f5f5f;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px; /* 100% */
}
.contact6 .contact9-content-area .form-area .single-input {
    margin-top: 20px;
}
.contact6 .contact9-content-area .form-area .single-input input,
.contact6 .contact9-content-area .form-area .single-input textarea {
    width: 100%;
    background-color: #f1f1f1;
    border-radius: 4px;
    padding: 16px;
    border: none;
}
.contact6 .contact9-content-area .form-area .single-input input::-moz-placeholder,
.contact6 .contact9-content-area .form-area .single-input textarea::-moz-placeholder {
    color: var(--Text-Color, #858585);
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px; /* 100% */
}
.contact6 .contact9-content-area .form-area .single-input input::placeholder,
.contact6 .contact9-content-area .form-area .single-input textarea::placeholder {
    color: var(--Text-Color, #858585);
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px; /* 100% */
}
.contact6 .contact9-content-area .form-area .single-input input:focus,
.contact6 .contact9-content-area .form-area .single-input textarea:focus {
    outline: none;
}
.contact6 .contact9-content-area .form-area .button-area {
    text-align: end;
    margin-top: 24px;
}
.contact6 .contact9-content-area .contact9-box {
    border-radius: 8px;
    background: #fff;
    box-shadow: 0px 4px 40px 0px rgba(0, 0, 0, 0.09);
    padding: 24px;
    display: flex;
    align-items: center;
    margin-left: 40px;
    transition: all 0.4s;
    margin-bottom: 20px;
}
.contact6 .contact9-content-area .contact9-box .heading {
    padding-left: 20px;
}
.contact6 .contact9-content-area .contact9-box .heading h5 {
    color: var(--Text-Color, #181818);
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px; /* 100% */
    transition: all 0.4s;
}
.contact6 .contact9-content-area .contact9-box .heading a {
    display: inline-block;
    color: var(--Paragraph-Color, #646375);
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 26px; /* 162.5% */
    padding-top: 10px;
    transition: all 0.4s;
}
.contact6 .contact9-content-area .contact9-box .icon {
    background-color: #325d28;
    height: 80px;
    width: 80px;
    line-height: 80px;
    text-align: center;
    border-radius: 50%;
    transition: all 0.4s;
}
.contact6 .contact9-content-area .contact9-box .icon img {
    transition: all 0.4s;
    filter: brightness(40);
}
.contact6 .contact9-content-area .contact9-box:hover {
    transform: translateY(-10px);
    transition: all 0.4s;
    background-color: #325d28;
}
.contact6 .contact9-content-area .contact9-box:hover .icon {
    background-color: #fff;
    transition: all 0.4s;
}
.contact6 .contact9-content-area .contact9-box:hover .icon img {
    filter: none;
    transition: all 0.4s;
    transform: rotateY(180deg);
}
.contact6 .contact9-content-area .contact9-box:hover .heading h5 {
    color: #fff;
    transition: all 0.4s;
}
.contact6 .contact9-content-area .contact9-box:hover .heading a {
    color: rgba(255, 255, 255, 0.7529411765);
    transition: all 0.4s;
}

.contact7 .contact-from {
    margin-top: 30px;
    background-color: #f8f7ff;
    padding: 40px;
    border-radius: 8px;
}
.contact7 .contact-from h3 {
    color: #081120;
    font-size: 24px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px; /* 100% */
}
.contact7 .contact-from p {
    color: #555;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px; /* 100% */
    padding-top: 16px;
}
.contact7 .contact-from .form-area {
    margin-top: 8px;
}
.contact7 .contact-from .form-area .single-input {
    margin-top: 20px;
}
.contact7 .contact-from .form-area .single-input input,
.contact7 .contact-from .form-area .single-input textarea {
    padding: 16px;
    width: 100%;
    border: none;
    border-radius: 4px;
}
.contact7 .contact-from .form-area .single-input input::-moz-placeholder,
.contact7 .contact-from .form-area .single-input textarea::-moz-placeholder {
    color: #555;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px; /* 100% */
}
.contact7 .contact-from .form-area .single-input input::placeholder,
.contact7 .contact-from .form-area .single-input textarea::placeholder {
    color: #555;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px; /* 100% */
}
.contact7 .contact-from .form-area .single-input input:focus,
.contact7 .contact-from .form-area .single-input textarea:focus {
    outline: none;
}
.contact7 .contact-from .form-area .button-area {
    text-align: end;
}
.contact7 .contact-map {
    margin-top: 30px;
}
.contact7 .contact-map iframe {
    height: 653px;
    border-radius: 8px;
}

.contact8 .contact9-content-area {
    border-radius: 8px;
    background: #fff;
    padding: 48px;
    margin-top: 60px;
}
.contact8 .contact9-content-area .form-area h3 {
    color: #0f0a07;
    font-size: 24px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px; /* 100% */
}
.contact8 .contact9-content-area .form-area p {
    color: #5f5f5f;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px; /* 100% */
}
.contact8 .contact9-content-area .form-area .single-input {
    margin-top: 20px;
}
.contact8 .contact9-content-area .form-area .single-input input,
.contact8 .contact9-content-area .form-area .single-input textarea {
    width: 100%;
    background-color: #f1f1f1;
    border-radius: 4px;
    padding: 16px;
    border: none;
}
.contact8 .contact9-content-area .form-area .single-input input::-moz-placeholder,
.contact8 .contact9-content-area .form-area .single-input textarea::-moz-placeholder {
    color: var(--Text-Color, #858585);
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px; /* 100% */
}
.contact8 .contact9-content-area .form-area .single-input input::placeholder,
.contact8 .contact9-content-area .form-area .single-input textarea::placeholder {
    color: var(--Text-Color, #858585);
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px; /* 100% */
}
.contact8 .contact9-content-area .form-area .single-input input:focus,
.contact8 .contact9-content-area .form-area .single-input textarea:focus {
    outline: none;
}
.contact8 .contact9-content-area .form-area .button-area {
    text-align: end;
    margin-top: 24px;
}
.contact8 .contact9-content-area .contact9-box {
    border-radius: 8px;
    background: #fff;
    box-shadow: 0px 4px 40px 0px rgba(0, 0, 0, 0.09);
    padding: 24px;
    display: flex;
    align-items: center;
    margin-left: 40px;
    transition: all 0.4s;
    margin-bottom: 20px;
}
.contact8 .contact9-content-area .contact9-box .heading {
    padding-left: 20px;
}
.contact8 .contact9-content-area .contact9-box .heading h5 {
    color: var(--Text-Color, #181818);
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px; /* 100% */
    transition: all 0.4s;
}
.contact8 .contact9-content-area .contact9-box .heading a {
    display: inline-block;
    color: var(--Paragraph-Color, #646375);
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 26px; /* 162.5% */
    padding-top: 10px;
    transition: all 0.4s;
}
.contact8 .contact9-content-area .contact9-box .icon {
    background-color: #141339;
    height: 80px;
    width: 80px;
    line-height: 80px;
    text-align: center;
    border-radius: 50%;
    transition: all 0.4s;
}
.contact8 .contact9-content-area .contact9-box .icon img {
    transition: all 0.4s;
    filter: brightness(40);
}
.contact8 .contact9-content-area .contact9-box:hover {
    transform: translateY(-10px);
    transition: all 0.4s;
    background-color: #141339;
}
.contact8 .contact9-content-area .contact9-box:hover .icon {
    background-color: #fff;
    transition: all 0.4s;
}
.contact8 .contact9-content-area .contact9-box:hover .icon img {
    filter: none;
    transition: all 0.4s;
    transform: rotateY(180deg);
}
.contact8 .contact9-content-area .contact9-box:hover .heading h5 {
    color: #fff;
    transition: all 0.4s;
}
.contact8 .contact9-content-area .contact9-box:hover .heading a {
    color: rgba(255, 255, 255, 0.7529411765);
    transition: all 0.4s;
}

.contact9 .contact9-content-area {
    border-radius: 8px;
    background: #f3f5f2;
    padding: 48px;
    margin-top: 60px;
}
.contact9 .contact9-content-area .form-area h3 {
    color: #0f0a07;
    font-size: 24px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px; /* 100% */
}
.contact9 .contact9-content-area .form-area p {
    color: #5f5f5f;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px; /* 100% */
}
.contact9 .contact9-content-area .form-area .single-input {
    margin-top: 20px;
}
.contact9 .contact9-content-area .form-area .single-input input,
.contact9 .contact9-content-area .form-area .single-input textarea {
    width: 100%;
    background-color: #fff;
    border-radius: 4px;
    padding: 16px;
    border: none;
}
.contact9 .contact9-content-area .form-area .single-input input::-moz-placeholder,
.contact9 .contact9-content-area .form-area .single-input textarea::-moz-placeholder {
    color: var(--Text-Color, #858585);
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px; /* 100% */
}
.contact9 .contact9-content-area .form-area .single-input input::placeholder,
.contact9 .contact9-content-area .form-area .single-input textarea::placeholder {
    color: var(--Text-Color, #858585);
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px; /* 100% */
}
.contact9 .contact9-content-area .form-area .single-input input:focus,
.contact9 .contact9-content-area .form-area .single-input textarea:focus {
    outline: none;
}
.contact9 .contact9-content-area .form-area .button-area {
    text-align: end;
    margin-top: 24px;
}
.contact9 .contact9-content-area .contact9-box {
    border-radius: 8px;
    background: #fff;
    box-shadow: 0px 4px 40px 0px rgba(0, 0, 0, 0.09);
    padding: 24px;
    display: flex;
    align-items: center;
    margin-left: 40px;
    transition: all 0.4s;
    margin-bottom: 20px;
}
.contact9 .contact9-content-area .contact9-box .heading {
    padding-left: 20px;
}
.contact9 .contact9-content-area .contact9-box .heading h5 {
    color: var(--Text-Color, #181818);
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px; /* 100% */
    transition: all 0.4s;
}
.contact9 .contact9-content-area .contact9-box .heading a {
    display: inline-block;
    color: var(--Paragraph-Color, #646375);
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 26px; /* 162.5% */
    padding-top: 10px;
    transition: all 0.4s;
}
.contact9 .contact9-content-area .contact9-box .icon {
    background-color: #2a9134;
    height: 80px;
    width: 80px;
    line-height: 80px;
    text-align: center;
    border-radius: 50%;
    transition: all 0.4s;
}
.contact9 .contact9-content-area .contact9-box .icon img {
    transition: all 0.4s;
    filter: brightness(40);
}
.contact9 .contact9-content-area .contact9-box:hover {
    transform: translateY(-10px);
    transition: all 0.4s;
    background-color: #2a9134;
}
.contact9 .contact9-content-area .contact9-box:hover .icon {
    background-color: #fff;
    transition: all 0.4s;
}
.contact9 .contact9-content-area .contact9-box:hover .icon img {
    filter: none;
    transition: all 0.4s;
    transform: rotateY(180deg);
}
.contact9 .contact9-content-area .contact9-box:hover .heading h5 {
    color: #fff;
    transition: all 0.4s;
}
.contact9 .contact9-content-area .contact9-box:hover .heading a {
    color: rgba(255, 255, 255, 0.7529411765);
    transition: all 0.4s;
}

.contact10 .contact-from {
    margin-top: 30px;
    background-color: #f7f5fb;
    padding: 40px;
    border-radius: 8px;
}
.contact10 .contact-from h3 {
    color: #081120;
    font-size: 24px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px; /* 100% */
}
.contact10 .contact-from p {
    color: #555;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px; /* 100% */
    padding-top: 16px;
}
.contact10 .contact-from .form-area {
    margin-top: 8px;
}
.contact10 .contact-from .form-area .single-input {
    margin-top: 20px;
}
.contact10 .contact-from .form-area .single-input input,
.contact10 .contact-from .form-area .single-input textarea {
    padding: 16px;
    width: 100%;
    border: none;
    border-radius: 4px;
}
.contact10 .contact-from .form-area .single-input input::-moz-placeholder,
.contact10 .contact-from .form-area .single-input textarea::-moz-placeholder {
    color: #555;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px; /* 100% */
}
.contact10 .contact-from .form-area .single-input input::placeholder,
.contact10 .contact-from .form-area .single-input textarea::placeholder {
    color: #555;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px; /* 100% */
}
.contact10 .contact-from .form-area .single-input input:focus,
.contact10 .contact-from .form-area .single-input textarea:focus {
    outline: none;
}
.contact10 .contact-from .form-area .button-area {
    text-align: end;
}
.contact10 .contact-map {
    margin-top: 30px;
}
.contact10 .contact-map iframe {
    height: 653px;
    border-radius: 8px;
}

/*
 ::::::::::::::::::::::::::
  CONTACT AREA CSS
 ::::::::::::::::::::::::::
 */
/*
::::::::::::::::::::::::::
 HERO AREA CSS
::::::::::::::::::::::::::
*/
.hero-area1 {
    min-height: 750px;
    background-color: var(--vtc-bg-common-bg2);
    display: flex;
    align-items: center;
}
.hero-area1 .main-heading {
    padding-top: 60px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .hero-area1 .main-heading {
        padding-top: 140px;
    }
}
@media (max-width: 767px) {
    .hero-area1 .main-heading {
        padding-top: 140px;
    }
}
.hero-area1 .main-heading span.span {
    display: inline-block;
    color: var(--vtc-bg-main-bg-1);
    font-size: var(--f-fs-font-fs16);
    line-height: var(--f-fs-font-fs16);
    font-weight: var(--f-fw-medium);
    border-radius: 4px;
    background-color: rgba(255, 124, 1, 0.1568627451);
    padding: 8px 12px;
    margin-bottom: 16px;
}
.hero-area1 .main-heading h1 {
    font-size: var(--f-fs-font-fs50);
    line-height: var(--f-fs-font-fs60);
    color: var(--vtc-text-heading-text-1);
    font-weight: var(--f-fw-bold);
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .hero-area1 .main-heading h1 {
        font-size: var(--f-fs-font-fs40);
        line-height: var(--f-fs-font-fs48);
    }
}
@media (max-width: 767px) {
    .hero-area1 .main-heading h1 {
        font-size: var(--f-fs-font-fs40);
        line-height: var(--f-fs-font-fs48);
    }
}
.hero-area1 .main-heading p {
    color: var(--vtc-text-pera-text-1);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-medium);
}
.hero-area1 .hero1-images {
    height: 530px;
    text-align: end;
    margin-right: -100px;
    position: relative;
    margin-top: 60px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .hero-area1 .hero1-images {
        margin-right: 0;
        margin-bottom: 60px;
    }
}
@media (max-width: 767px) {
    .hero-area1 .hero1-images {
        margin-right: 0;
        margin-bottom: 60px;
    }
}
.hero-area1 .hero1-images .image2 {
    position: absolute;
    top: 33px;
    right: 75px;
    border-radius: 0 0 50% 50%;
    overflow: hidden;
}
.hero-area1 .hero1-images .image3 {
    position: absolute;
    left: 0;
    bottom: 0px;
}
.hero-area1 .hero1-images .image4 {
    position: absolute;
    top: 70px;
    right: 100px;
}

.hero-area2 {
    min-height: 750px;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    display: flex;
    align-items: center;
    position: relative;
}
.hero-area2 .main-heading {
    padding-top: 80px;
}
.hero-area2 .main-heading span.span {
    display: inline-block;
    color: var(--vtc-text-text-white-text-1);
    font-size: var(--f-fs-font-fs16);
    line-height: var(--f-fs-font-fs16);
    font-weight: var(--f-fw-medium);
    border-radius: 4px;
    background-color: var(--vtc-bg-common-bg3);
    padding: 8px 12px;
    margin-bottom: 16px;
}
.hero-area2 .main-heading h1 {
    font-size: var(--f-fs-font-fs64);
    line-height: var(--f-fs-font-fs70);
    color: var(--vtc-text-text-white-text-1);
    font-weight: var(--f-fw-bold);
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .hero-area2 .main-heading h1 {
        font-size: var(--f-fs-font-fs40);
        line-height: var(--f-fs-font-fs48);
    }
}
@media (max-width: 767px) {
    .hero-area2 .main-heading h1 {
        font-size: var(--f-fs-font-fs40);
        line-height: var(--f-fs-font-fs48);
    }
}
.hero-area2 .main-heading p {
    color: var(--vtc-text-pera-text-2);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-medium);
}
.hero-area2 .shape1 {
    position: absolute;
    top: 150px;
    left: 50px;
}
@media (max-width: 767px) {
    .hero-area2 .shape1 {
        display: none;
    }
}
.hero-area2 .shape2 {
    position: absolute;
    top: 300px;
    right: 50px;
}
@media (max-width: 767px) {
    .hero-area2 .shape2 {
        display: none;
    }
}
.hero-area2 .images {
    position: relative;
    margin-right: -30px;
    height: 600px;
    margin-bottom: -100px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .hero-area2 .images {
        margin-bottom: 30px;
        margin-right: 0;
    }
}
@media (max-width: 767px) {
    .hero-area2 .images {
        height: 450px;
        margin-bottom: 30px;
        margin-right: 0;
    }
}
.hero-area2 .images .image1 {
    position: absolute;
    bottom: -30px;
    right: 0;
}
.hero-area2 .images .image2 {
    position: absolute;
    bottom: -30px;
    right: 30px;
}
.hero-area2 .images .image3 {
    position: absolute;
    bottom: 0;
    right: 0;
}

.hero-area3 {
    min-height: 900px;
    background-position: center bottom;
    background-repeat: no-repeat;
    background-size: cover;
    display: flex;
    align-items: center;
}
.hero-area3 .main-heading {
    position: relative;
    padding-top: 80px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .hero-area3 .main-heading {
        margin: 120px 0px 80px 0px;
        padding-top: 0;
    }
}
@media (max-width: 767px) {
    .hero-area3 .main-heading {
        margin: 120px 0px 80px 0px;
        padding-top: 0;
    }
}
.hero-area3 .main-heading span.span {
    display: inline-block;
    color: var(--vtc-text-text-white-text-1);
    font-size: var(--f-fs-font-fs16);
    line-height: var(--f-fs-font-fs16);
    font-weight: var(--f-fw-medium);
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.1725490196);
    padding: 8px 12px;
    margin-bottom: 16px;
}
.hero-area3 .main-heading h1 {
    font-size: var(--f-fs-font-fs64);
    line-height: var(--f-fs-font-fs70);
    color: var(--vtc-text-text-white-text-1);
    font-weight: var(--f-fw-bold);
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .hero-area3 .main-heading h1 {
        font-size: var(--f-fs-font-fs40);
        line-height: var(--f-fs-font-fs48);
    }
}
@media (max-width: 767px) {
    .hero-area3 .main-heading h1 {
        font-size: var(--f-fs-font-fs40);
        line-height: var(--f-fs-font-fs48);
    }
}
.hero-area3 .main-heading p {
    color: var(--vtc-text-pera-text-2);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-medium);
}
.hero-area3 .main-heading .reating-area {
    margin-top: 32px;
}
.hero-area3 .main-heading .reating-area p.pera {
    color: var(--vtc-text-text-white-text-1);
    font-size: var(--f-fs-font-fs20);
    line-height: var(--f-fs-font-fs20);
    font-weight: var(--f-fw-bold);
}
.hero-area3 .main-heading .reating-area .reating {
    display: flex;
    align-items: center;
    margin-top: 12px;
}
.hero-area3 .main-heading .reating-area .reating .stars ul li {
    display: inline-block;
    color: #ffd600;
    margin: 0px 2px;
}
.hero-area3 .main-heading .reating-area .reating .stars ul li:nth-last-child(1) {
    color: #fff;
}
.hero-area3 .main-heading .arrow-shape {
    position: absolute;
    left: 280px;
    bottom: 0;
}
.hero-area3 .images-all {
    margin-top: 80px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .hero-area3 .images-all {
        margin-bottom: 100px;
        margin-top: 0px;
    }
}
@media (max-width: 767px) {
    .hero-area3 .images-all {
        margin-bottom: 100px;
        margin-top: 0px;
    }
}
@media (max-width: 767px) {
    .hero-area3 .images-all .image {
        margin-bottom: 20px;
    }
}
.hero-area3 .images-all .image img {
    width: 100%;
    border-radius: 4px;
}

/* video button  */
.video-play-button {
    position: relative;
    z-index: 10;
    margin: 0px 30px;
    box-sizing: content-box;
    display: block;
    width: 32px;
    height: 44px;
    /* background: #fa183d; */
    border-radius: 50%;
    padding: 18px 20px 18px 28px;
    cursor: pointer;
}

.video-play-button:before {
    content: "";
    position: absolute;
    z-index: 0;
    left: 50%;
    top: 50%;
    transform: translateX(-50%) translateY(-50%);
    display: block;
    width: 60px;
    height: 60px;
    background: #fd965b;
    border-radius: 50%;
    animation: pulse-border 1500ms ease-out infinite;
}

.video-play-button:after {
    content: "";
    position: absolute;
    z-index: 1;
    left: 50%;
    top: 50%;
    transform: translateX(-50%) translateY(-50%);
    display: block;
    width: 60px;
    height: 60px;
    background: #fd965b;
    border-radius: 50%;
    transition: all 200ms;
}

.video-play-button:hover:after {
    background-color: #fc7629;
}

.video-play-button img {
    position: relative;
    z-index: 3;
    max-width: 100%;
    width: auto;
    height: auto;
}

.video-play-button span {
    display: block;
    position: relative;
    z-index: 3;
    margin-top: 12px;
    margin-left: 8px;
    width: 0;
    height: 0;
    border-left: 12px solid #fff;
    border-top: 10px solid transparent;
    border-bottom: 10px solid transparent;
}

@keyframes pulse-border {
    0% {
        transform: translateX(-50%) translateY(-50%) translateZ(0) scale(1);
        opacity: 1;
    }
    100% {
        transform: translateX(-50%) translateY(-50%) translateZ(0) scale(1.5);
        opacity: 0;
    }
}
.video-area {
    position: relative;
}
.video-area .video-buttton {
    position: absolute;
    top: 50%;
    left: 50%;
    margin-top: -40px;
    margin-left: -70px;
}

.hero-area4 {
    background-color: var(--vtc-bg-common-bg7);
    min-height: 1150px;
}
.hero-area4 .main-heading {
    padding-top: 180px;
}
.hero-area4 .main-heading span.span {
    display: inline-block;
    font-size: var(--f-fs-font-fs16);
    line-height: var(--f-fs-font-fs16);
    font-weight: var(--f-fw-medium);
    color: var(--vtc-text-heading-text-2);
    padding: 8px 12px;
    border-radius: 4px;
    background-color: var(--vtc-text-text-white-text-1);
    margin-bottom: 16px;
}
.hero-area4 .main-heading h1 {
    font-size: var(--f-fs-font-fs64);
    line-height: var(--f-fs-font-fs70);
    color: var(--vtc-text-heading-text-2);
    font-weight: var(--f-fw-bold);
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .hero-area4 .main-heading h1 {
        font-size: var(--f-fs-font-fs40);
        line-height: var(--f-fs-font-fs48);
    }
}
@media (max-width: 767px) {
    .hero-area4 .main-heading h1 {
        font-size: var(--f-fs-font-fs40);
        line-height: var(--f-fs-font-fs48);
    }
}
.hero-area4 .main-heading p {
    color: #435986;
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-medium);
}
.hero-area4 .images-all {
    position: relative;
    height: 480px;
    margin-top: 60px;
}
.hero-area4 .images-all .image1 {
    position: absolute;
    left: 0;
    bottom: 0;
    z-index: 4;
}
.hero-area4 .images-all .image1:hover {
    z-index: 99;
    transition: all 0.4s;
}
.hero-area4 .images-all .image1:hover img {
    transform: scale(1.1);
    transition: all 0.4s;
}
.hero-area4 .images-all .image2 {
    position: absolute;
    left: 315px;
    top: 0;
    z-index: 3;
    transition: all 0.4s;
}
.hero-area4 .images-all .image2 img {
    transition: all 0.4s;
}
.hero-area4 .images-all .image2:hover {
    z-index: 99;
    transition: all 0.4s;
}
.hero-area4 .images-all .image2:hover img {
    transform: scale(1.1);
    transition: all 0.4s;
}
.hero-area4 .images-all .image3 {
    position: absolute;
    right: 315px;
    bottom: 0;
    z-index: 2;
}
.hero-area4 .images-all .image3 img {
    transition: all 0.4s;
}
.hero-area4 .images-all .image3:hover {
    z-index: 99;
    transition: all 0.4s;
}
.hero-area4 .images-all .image3:hover img {
    transform: scale(1.1);
    transition: all 0.4s;
}
.hero-area4 .images-all .image4 {
    position: absolute;
    right: 0;
    top: 0;
}
.hero-area4 .images-all .image4 img {
    transition: all 0.4s;
}
.hero-area4 .images-all .image4:hover {
    z-index: 99;
    transition: all 0.4s;
}
.hero-area4 .images-all .image4:hover img {
    transform: scale(1.1);
    transition: all 0.4s;
}
.hero-area4 .shape-all-area {
    position: absolute;
    top: 250px;
    left: -70px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .hero-area4 .shape-all-area {
        display: none;
    }
}
@media (max-width: 767px) {
    .hero-area4 .shape-all-area {
        display: none;
    }
}
.hero-area4 .shape-all-area .icon {
    position: absolute;
    left: 35px;
    top: 35px;
    z-index: 2;
}
.hero-area4 .shape-all-area .icon a {
    display: inline-block;
    height: 82px;
    width: 82px;
    border-radius: 50%;
    text-align: center;
    line-height: 82px;
    background-color: var(--vtc-bg-main-bg-1);
    font-size: 30px;
    color: #fff;
    transition: all 0.4s;
}
.hero-area4 .shape-all-area .icon a:hover {
    background-color: var(--vtc-bg-main-bg-5);
}
.hero-area4 .shape2 {
    position: absolute;
    top: 350px;
    right: 0;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .hero-area4 .shape2 {
        display: none;
    }
}
@media (max-width: 767px) {
    .hero-area4 .shape2 {
        display: none;
    }
}
.hero-area4 .images-all-md {
    margin: 60px 0px;
}
.hero-area4 .images-all-md img {
    margin-bottom: 30px;
}

.hero-area5 {
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    min-height: 850px;
    display: flex;
    align-items: center;
    position: relative;
}
.hero-area5 .main-heading {
    padding-top: 140px;
}
.hero-area5 .main-heading span.span {
    display: inline-block;
    color: var(--vtc-text-text-white-text-1);
    font-size: var(--f-fs-font-fs16);
    line-height: var(--f-fs-font-fs16);
    font-weight: var(--f-fw-medium);
    border-radius: 4px;
    background-color: var(--vtc-bg-common-bg3);
    padding: 8px 12px;
    margin-bottom: 16px;
}
.hero-area5 .main-heading h1 {
    font-size: var(--f-fs-font-fs64);
    line-height: var(--f-fs-font-fs70);
    color: var(--vtc-text-text-white-text-1);
    font-weight: var(--f-fw-bold);
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .hero-area5 .main-heading h1 {
        font-size: var(--f-fs-font-fs40);
        line-height: var(--f-fs-font-fs48);
    }
}
@media (max-width: 767px) {
    .hero-area5 .main-heading h1 {
        font-size: var(--f-fs-font-fs40);
        line-height: var(--f-fs-font-fs48);
    }
}
.hero-area5 .main-heading p {
    color: var(--vtc-text-pera-text-2);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-medium);
}
.hero-area5 .images-all {
    position: relative;
    height: 800px;
    margin-bottom: -50px;
    text-align: end;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .hero-area5 .images-all {
        height: 650px;
        margin-bottom: 0px;
    }
}
@media (max-width: 767px) {
    .hero-area5 .images-all {
        height: 500px;
        margin-bottom: 0px;
    }
}
.hero-area5 .images-all .image1 {
    position: absolute;
    bottom: 0;
    right: 100px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .hero-area5 .images-all .image1 {
        display: none;
    }
}
@media (max-width: 767px) {
    .hero-area5 .images-all .image1 {
        display: none;
    }
}
.hero-area5 .images-all .image2 {
    position: absolute;
    bottom: 0;
    right: 0;
}
.hero-area5 .shape {
    position: absolute;
    left: 0;
    bottom: 0;
}

.common-hero {
    background-color: var(--vtc-bg-common-bg2);
    height: 350px;
    display: flex;
    align-items: center;
}
.common-hero .main-heading {
    padding-top: 70px;
}
.common-hero .main-heading h1 {
    font-size: var(--f-fs-font-fs64);
    line-height: var(--f-fs-font-fs64);
    font-weight: var(--f-fw-bold);
    color: var(--vtc-text-heading-text-1);
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .common-hero .main-heading h1 {
        font-size: var(--f-fs-font-fs40);
        line-height: var(--f-fs-font-fs40);
    }
}
@media (max-width: 767px) {
    .common-hero .main-heading h1 {
        font-size: var(--f-fs-font-fs40);
        line-height: var(--f-fs-font-fs40);
    }
}
.common-hero .main-heading .pages-intro {
    display: flex;
    align-items: center;
    justify-content: center;
    padding-top: 16px;
}
.common-hero .main-heading .pages-intro a {
    display: inline-block;
    color: var(--vtc-text-heading-text-1);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-medium);
}
.common-hero .main-heading .pages-intro span {
    display: inline-block;
    color: var(--vtc-text-heading-text-1);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-medium);
    padding: 0px 5px;
}
.common-hero .main-heading .pages-intro p {
    display: inline-block;
    color: var(--vtc-text-heading-text-1);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-bold);
}

.hero6 {
    min-height: 850px;
    background-size: cover;
    background-position: center bottom;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
}
.hero6 .images-all {
    position: relative;
    height: 500px;
    text-align: end;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .hero6 .images-all {
        margin-top: 40px;
        margin-bottom: 40px;
    }
}
@media (max-width: 767px) {
    .hero6 .images-all {
        margin-top: 50px;
    }
}
.hero6 .images-all .image2 {
    position: absolute;
    left: 90px;
    bottom: 0;
}
.hero6 .main-heading {
    padding-top: 50px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .hero6 .main-heading {
        padding-top: 170px;
    }
}
@media (max-width: 767px) {
    .hero6 .main-heading {
        padding-top: 170px;
    }
}
.hero6 .main-heading span.span {
    color: #fff;
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: 18px; /* 100% */
    text-transform: uppercase;
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.1);
    padding: 10px 14px 5px 14px;
    display: inline-block;
    margin-bottom: 16px;
}
.hero6 .main-heading span.span img {
    filter: brightness(0) invert(1);
    transform: translateY(-3px);
    margin-right: 3px;
}
.hero6 .main-heading h1 {
    color: #fff;
    font-size: 56px;
    font-style: normal;
    font-weight: 600;
    line-height: 64px; /* 114.286% */
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .hero6 .main-heading h1 {
        font-size: 40px;
        line-height: 48px;
    }
}
@media (max-width: 767px) {
    .hero6 .main-heading h1 {
        font-size: 40px;
        line-height: 48px;
    }
}
.hero6 .main-heading p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 26px; /* 144.444% */
}

.hero7 {
    min-height: 850px;
    background-size: cover;
    background-position: center bottom;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
}
.hero7 .main-heading {
    margin-top: 100px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .hero7 .main-heading {
        margin-top: 160px;
    }
}
@media (max-width: 767px) {
    .hero7 .main-heading {
        margin-top: 160px;
    }
}
.hero7 .main-heading span.span {
    color: #fff;
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: 18px; /* 100% */
    text-transform: uppercase;
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.1);
    padding: 10px 14px 5px 14px;
    display: inline-block;
    margin-bottom: 16px;
}
.hero7 .main-heading span.span img {
    filter: brightness(0) invert(1);
    transform: translateY(-3px);
    margin-right: 3px;
}
.hero7 .main-heading h1 {
    color: #fff;
    font-size: 56px;
    font-style: normal;
    font-weight: 600;
    line-height: 64px; /* 114.286% */
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .hero7 .main-heading h1 {
        font-size: 40px;
        line-height: 48px;
    }
}
@media (max-width: 767px) {
    .hero7 .main-heading h1 {
        font-size: 40px;
        line-height: 48px;
    }
}
.hero7 .main-heading p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 26px; /* 144.444% */
}
.hero7 .hero7-images {
    margin-top: 100px;
    margin-right: -70px;
    margin-left: 50px;
    position: relative;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .hero7 .hero7-images {
        margin-left: 0;
        margin-bottom: 50px;
        margin-top: 50px;
    }
}
@media (max-width: 767px) {
    .hero7 .hero7-images {
        margin-left: 0;
    }
}
.hero7 .hero7-images .shape {
    position: absolute;
    bottom: -100px;
    left: -100px;
    z-index: 1;
    opacity: 0.4;
}
.hero7 .hero7-images .cs_case_study_1_list {
    display: flex;
    position: relative;
    z-index: 2;
}
@media (max-width: 767px) {
    .hero7 .hero7-images .cs_case_study_1_list {
        flex-direction: column;
    }
}
.hero7 .hero7-images .cs_case_study_1_list > .cs_case_study.cs_style_1 {
    flex: 1;
}
.hero7 .hero7-images .cs_case_study_1_list .cs_case_study.cs_style_1 {
    min-height: 400px;
}
.hero7 .hero7-images .cs_case_study_1_list .cs_case_study.cs_style_1 .cs_case_study_in {
    transition: all 0.2s ease;
    left: 80px;
    opacity: 0;
}
@media (max-width: 767px) {
    .hero7 .hero7-images .cs_case_study_1_list .cs_case_study.cs_style_1 .cs_case_study_in {
        left: 0;
        opacity: 1;
    }
}
.hero7 .hero7-images .cs_case_study_1_list .cs_case_study.cs_style_1.active {
    flex: 3;
}
.hero7 .hero7-images .cs_case_study_1_list .cs_case_study.cs_style_1.active .cs_case_study_in {
    opacity: 1;
    left: 0;
    transition: all 0.6s ease;
    transition-delay: 0.3s;
}
.hero7 .hero7-images .cs_case_study_1_list .cs_case_study.cs_style_1.active .cs_case_study_icon {
    transform: scale(0) rotate(360deg);
    transition-delay: 0.1s;
}
.hero7 .hero7-images .cs_case_study.cs_style_1 {
    height: 700px;
    display: flex;
    align-items: flex-end;
    position: relative;
    overflow: hidden;
    transition: all 0.4s ease;
}
@media (max-width: 1400px) {
    .hero7 .hero7-images .cs_case_study.cs_style_1 {
        height: 600px;
    }
}
.hero7 .hero7-images .cs_case_study.cs_style_1 .cs_case_study_title a {
    text-decoration: none;
    background-image: linear-gradient(currentColor, currentColor);
    background-repeat: no-repeat;
    background-position: bottom left;
    background-size: 100% 3px;
}
.hero7 .hero7-images .cs_case_study.cs_style_1 .cs_case_study_number {
    left: 70px;
    top: 70px;
    z-index: 1;
}
@media (max-width: 767px) {
    .hero7 .hero7-images .cs_case_study.cs_style_1 .cs_case_study_number {
        left: 30px;
        top: 40px;
    }
}
.hero7 .hero7-images .cs_case_study.cs_style_1 .cs_case_study_category {
    margin-bottom: 15px;
}
.hero7 .hero7-images .cs_case_study.cs_style_1 .cs_case_study_thumb {
    position: absolute;
    height: 100%;
    width: 100%;
    left: 0;
    top: 0;
}
@media (max-width: 767px) {
    .hero7 .hero7-images .cs_case_study.cs_style_1 .cs_case_study_thumb {
        margin-bottom: 30px;
        top: auto;
        bottom: 30px;
    }
}
.hero7 .hero7-images .cs_case_study.cs_style_1 .cs_case_study_in {
    position: relative;
    z-index: 2;
    width: 100%;
    padding: 88px 72px;
}
@media (max-width: 1400px) {
    .hero7 .hero7-images .cs_case_study.cs_style_1 .cs_case_study_in {
        padding: 50px;
    }
}
@media (max-width: 767px) {
    .hero7 .hero7-images .cs_case_study.cs_style_1 .cs_case_study_in {
        padding: 30px;
    }
}
.hero7 .hero7-images .cs_case_study.cs_style_1 .cs_case_study_icon {
    left: 70px;
    bottom: 86px;
    z-index: 1;
    background-color: #4f4747;
    color: #fff;
    height: 55px;
    width: 55px;
    font-size: 20px;
    transition: all 0.6s ease;
}
@media (max-width: 1400px) {
    .hero7 .hero7-images .cs_case_study.cs_style_1 .cs_case_study_icon {
        left: 40px;
        bottom: 50px;
    }
}
@media (max-width: 767px) {
    .hero7 .hero7-images .cs_case_study.cs_style_1 .cs_case_study_icon {
        display: none;
    }
}
.hero7 .hero7-images .cs_case_study_thumb {
    background-image: url(../img/hero/hero7-image1.png);
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
    cursor: pointer;
}
.hero7 .hero7-images .cs_case_study_thumb.cs_case_study_thumb2 {
    background-image: url(../img/hero/hero7-image2.png);
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
    cursor: pointer;
}
.hero7 .hero7-images .cs_case_study_thumb.cs_case_study_thumb3 {
    background-image: url(../img/hero/hero7-image3.png);
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
    cursor: pointer;
}
.hero7 .hero7-images .cs_case_study.cs_style_1.cs_hover_active {
    margin: 0px 12px;
    border-radius: 8px;
    height: 500px;
}

.hero8 {
    min-height: 850px;
    background-size: cover;
    background-position: center bottom;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    position: relative;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .hero8 {
        min-height: 1000px;
    }
}
@media (max-width: 767px) {
    .hero8 {
        min-height: 1060px;
    }
}
.hero8 .main-heading {
    margin-top: 100px;
    position: relative;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .hero8 .main-heading {
        margin-top: 0;
    }
}
@media (max-width: 767px) {
    .hero8 .main-heading {
        margin-top: 0px;
    }
}
.hero8 .main-heading span.span {
    color: #fff;
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: 18px; /* 100% */
    text-transform: uppercase;
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.1);
    padding: 10px 14px 5px 14px;
    display: inline-block;
    margin-bottom: 16px;
}
.hero8 .main-heading span.span img {
    filter: brightness(0) invert(1);
    transform: translateY(-3px);
    margin-right: 3px;
}
.hero8 .main-heading h1 {
    color: #fff;
    font-size: 56px;
    font-style: normal;
    font-weight: 600;
    line-height: 64px; /* 114.286% */
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .hero8 .main-heading h1 {
        font-size: 40px;
        line-height: 48px;
    }
}
@media (max-width: 767px) {
    .hero8 .main-heading h1 {
        font-size: 40px;
        line-height: 48px;
    }
}
.hero8 .main-heading p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 26px; /* 144.444% */
}
.hero8 .main-heading .shape {
    position: absolute;
    bottom: 0;
    right: -50px;
}
.hero8 .image1 {
    position: absolute;
    bottom: 0;
    left: 80px;
    z-index: 1;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .hero8 .image1 {
        height: 300px;
    }
}
@media (max-width: 767px) {
    .hero8 .image1 {
        height: 300px;
    }
}
.hero8 .image2 {
    position: absolute;
    top: 0;
    right: 80px;
    z-index: 1;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .hero8 .image2 {
        height: 300px;
    }
}
@media (max-width: 767px) {
    .hero8 .image2 {
        height: 300px;
    }
}

.hero9 {
    min-height: 850px;
    background-size: cover;
    background-position: center bottom;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    position: relative;
}
.hero9 .main-images {
    padding-top: 90px;
    position: relative;
    text-align: end;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .hero9 .main-images {
        padding-top: 40px;
        margin-bottom: 40px;
    }
}
@media (max-width: 767px) {
    .hero9 .main-images {
        padding-top: 40px;
        margin-bottom: 40px;
    }
}
.hero9 .main-images .image2 {
    position: absolute;
    bottom: 100px;
    left: 0;
}
.hero9 .main-heading {
    padding-right: 70px;
    padding-top: 80px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .hero9 .main-heading {
        padding-top: 160px;
    }
}
@media (max-width: 767px) {
    .hero9 .main-heading {
        padding-top: 160px;
    }
}
.hero9 .main-heading span.span {
    color: #2a9134;
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: 18px; /* 100% */
    text-transform: uppercase;
    border-radius: 4px;
    background: #eaf4eb;
    padding: 10px 14px 5px 14px;
    display: inline-block;
    margin-bottom: 16px;
}
.hero9 .main-heading span.span img {
    transform: translateY(-3px);
    margin-right: 3px;
}
.hero9 .main-heading h1 {
    color: #111;
    font-size: 56px;
    font-style: normal;
    font-weight: 600;
    line-height: 64px; /* 114.286% */
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .hero9 .main-heading h1 {
        font-size: 40px;
        line-height: 40px;
    }
}
@media (max-width: 767px) {
    .hero9 .main-heading h1 {
        font-size: 40px;
        line-height: 40px;
    }
}
.hero9 .main-heading p {
    color: rgba(17, 17, 17, 0.9);
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 26px; /* 144.444% */
}

.hero10-sliders .hero10-single {
    min-height: 850px;
    position: relative;
    display: flex;
    align-items: center;
}
.hero10-sliders .hero10-single::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    background-image: url(../img/bg/hero10-bg.jpg);
    z-index: -3;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
    transition: all 4s;
}
.hero10-sliders .hero10-single.swiper-slide.swiper-slide-active::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    background-image: url(../img/bg/hero10-bg.jpg);
    z-index: -3;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
    transition: all 4s;
    transform: scale(1.2);
}
.hero10-sliders .hero10-single.swiper-slide.swiper-slide-active .main-heading {
    padding-top: 80px;
}
@media (max-width: 767px) {
    .hero10-sliders .hero10-single.swiper-slide.swiper-slide-active .main-heading {
        padding-top: 20px;
    }
}
.hero10-sliders .hero10-single.swiper-slide.swiper-slide-active .main-heading span.span {
    color: #fff;
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: 18px; /* 100% */
    text-transform: uppercase;
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.1);
    padding: 10px 14px 10px 40px;
    display: inline-block;
    margin-bottom: 16px;
    position: relative;
    transform: translateY(0);
    opacity: 1;
    transition: all 4s;
}
.hero10-sliders .hero10-single.swiper-slide.swiper-slide-active .main-heading span.span img {
    filter: brightness(0) invert(1);
    transform: translateY(-3px);
    margin-right: 3px;
    position: absolute;
    left: 10px;
    top: 12px;
}
.hero10-sliders .hero10-single.swiper-slide.swiper-slide-active .main-heading h1 {
    color: #fff;
    font-size: 56px;
    font-style: normal;
    font-weight: 600;
    line-height: 64px; /* 114.286% */
    transform: translateY(0);
    opacity: 1;
    transition: all 4s;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .hero10-sliders .hero10-single.swiper-slide.swiper-slide-active .main-heading h1 {
        font-size: 40px;
        line-height: 48px;
    }
}
@media (max-width: 767px) {
    .hero10-sliders .hero10-single.swiper-slide.swiper-slide-active .main-heading h1 {
        font-size: 40px;
        line-height: 48px;
    }
}
.hero10-sliders .hero10-single.swiper-slide.swiper-slide-active .main-heading p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 26px; /* 144.444% */
    transform: translateY(0);
    opacity: 1;
    transition: all 4s;
}
.hero10-sliders .hero10-single.swiper-slide.swiper-slide-active .main-heading .button {
    transform: translateY(0);
    opacity: 1;
    transition: all 4s;
}
.hero10-sliders .hero10-single .main-heading {
    padding-top: 80px;
}
.hero10-sliders .hero10-single .main-heading span.span {
    color: #fff;
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: 18px; /* 100% */
    text-transform: uppercase;
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.1);
    padding: 10px 14px 10px 40px;
    display: inline-block;
    margin-bottom: 16px;
    position: relative;
    transform: translateY(-100px);
    opacity: 0;
    transition: all 4s;
}
.hero10-sliders .hero10-single .main-heading span.span img {
    filter: brightness(0) invert(1);
    transform: translateY(-3px);
    margin-right: 3px;
    position: absolute;
    left: 10px;
    top: 12px;
}
.hero10-sliders .hero10-single .main-heading h1 {
    color: #fff;
    font-size: 56px;
    font-style: normal;
    font-weight: 600;
    line-height: 64px; /* 114.286% */
    transform: translateY(-100px);
    opacity: 0;
    transition: all 4s;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .hero10-sliders .hero10-single .main-heading h1 {
        font-size: 40px;
        line-height: 48px;
    }
}
@media (max-width: 767px) {
    .hero10-sliders .hero10-single .main-heading h1 {
        font-size: 40px;
        line-height: 48px;
    }
}
.hero10-sliders .hero10-single .main-heading p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 26px; /* 144.444% */
    transform: translateY(50px);
    opacity: 0;
    transition: all 4s;
}
.hero10-sliders .hero10-single .main-heading .button {
    transform: translateY(50px);
    opacity: 0;
    transition: all 4s;
}
.hero10-btns button {
    position: absolute;
    top: 50%;
    right: 70px;
    height: 56px;
    width: 56px;
    text-align: center;
    line-height: 56px;
    border: none;
    border-radius: 50%;
    transition: all 0.4s;
    font-size: 22px;
    background-color: #fff;
    z-index: 1;
}
@media (max-width: 767px) {
    .hero10-btns button {
        top: 83%;
        right: auto;
        left: 40px;
    }
}
.hero10-btns button:hover {
    transition: all 0.4s;
    background-color: #fa6444;
    color: #fff;
}
.hero10-btns .hero10-next-arrow {
    margin-top: -70px;
}
@media (max-width: 767px) {
    .hero10-btns .hero10-next-arrow {
        margin-top: 0;
    }
}
@media (max-width: 767px) {
    .hero10-btns .hero10-prev-arrow {
        margin-left: 70px;
    }
}

.hero10-icons-area {
    margin-top: -60px;
    position: relative;
    z-index: 2;
}
@media (max-width: 767px) {
    .hero10-icons-area {
        margin-top: 30px;
    }
}
.hero10-icons-area .hero10-icon-boxs .single-box {
    border-radius: 8px;
    background: #fff;
    box-shadow: 0px 15px 40px 0px rgba(0, 0, 0, 0.09);
    padding: 28px 24px;
    display: flex;
    align-items: center;
    transition: all 0.4s;
}
.hero10-icons-area .hero10-icon-boxs .single-box:hover {
    transform: translateY(-10px);
    transition: all 0.4s;
}
@media (max-width: 767px) {
    .hero10-icons-area .hero10-icon-boxs .single-box {
        margin-top: 30px;
    }
}
.hero10-icons-area .hero10-icon-boxs .single-box .icon {
    height: 60px;
    width: 60px;
    background: rgba(250, 100, 68, 0.2);
    border-radius: 50%;
    line-height: 60px;
    text-align: center;
}
.hero10-icons-area .hero10-icon-boxs .single-box .heading {
    padding-left: 16px;
}
.hero10-icons-area .hero10-icon-boxs .single-box .heading h5 {
    color: #000;
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px; /* 100% */
}
.hero10-icons-area .hero10-icon-boxs .single-box .heading p {
    color: #555;
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: 18px; /* 100% */
    padding-top: 12px;
}

/*
::::::::::::::::::::::::::
 HERO AREA CSS
::::::::::::::::::::::::::
*/
/*
::::::::::::::::::::::::::
 PRELOADER AREA CSS
::::::::::::::::::::::::::
*/
.preloader {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
}

.preloader.preloader2 {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    background-color: #00002d;
    display: flex;
    align-items: center;
    justify-content: center;
}

.loading-container,
.loading {
    height: 100px;
    position: relative;
    width: 100px;
    border-radius: 100%;
}

.loading-container {
    margin: 40px auto;
}

.loading {
    border: 1px solid transparent;
    border-color: transparent #ff7a01 transparent #ff7a01;
    animation: rotate-loading 1.5s linear 0s infinite normal;
    transform-origin: 50% 50%;
}

.loading.loading2 {
    border: 1px solid transparent;
    border-color: transparent #19326a transparent #19326a;
    animation: rotate-loading 1.5s linear 0s infinite normal;
    transform-origin: 50% 50%;
}

.loading.loading3 {
    border: 1px solid transparent;
    border-color: transparent #fc253f transparent #fc253f;
    animation: rotate-loading 1.5s linear 0s infinite normal;
    transform-origin: 50% 50%;
}

.loading.loading6 {
    border: 1px solid transparent;
    border-color: transparent #3f5f34 transparent #3f5f34;
    animation: rotate-loading 1.5s linear 0s infinite normal;
    transform-origin: 50% 50%;
}

.loading.loading7 {
    border: 1px solid transparent;
    border-color: transparent #5957e5 transparent #5957e5;
    animation: rotate-loading 1.5s linear 0s infinite normal;
    transform-origin: 50% 50%;
}

.loading.loading8 {
    border: 1px solid transparent;
    border-color: transparent #141339 transparent #141339;
    animation: rotate-loading 1.5s linear 0s infinite normal;
    transform-origin: 50% 50%;
}

.loading.loading9 {
    border: 1px solid transparent;
    border-color: transparent #2a9134 transparent #2a9134;
    animation: rotate-loading 1.5s linear 0s infinite normal;
    transform-origin: 50% 50%;
}

.loading.loading10 {
    border: 1px solid transparent;
    border-color: transparent #fa6444 transparent #fa6444;
    animation: rotate-loading 1.5s linear 0s infinite normal;
    transform-origin: 50% 50%;
}

.loading-container:hover .loading,
.loading-container .loading {
    transition: all 0.5s ease-in-out;
}

#loading-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    max-width: 66px;
    transform: translate(-50%, -50%);
}

@keyframes rotate-loading {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}
/*
::::::::::::::::::::::::::
 PRELOADER AREA CSS
::::::::::::::::::::::::::
*/
/*
::::::::::::::::::::::::::
 CASE AREA CSS
::::::::::::::::::::::::::
*/
.case3 .image-area {
    margin-top: 30px;
    text-align: center;
    position: relative;
}
.case3 .image-area .image img {
    width: 100%;
}
.case3 .image-area::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    background-color: var(--vtc-bg-main-bg-3);
    border-radius: 4px;
    transform: scale(1);
    opacity: 0;
    transition: all 0.4s;
}
.case3 .image-area .heading-area {
    position: absolute;
    top: 50%;
    z-index: 9;
    left: -50%;
    right: -50%;
    margin-top: -70px;
    opacity: 0;
    transition: all 0.4s;
}
.case3 .image-area .heading-area .icon {
    display: inline-block;
    height: 48px;
    width: 48px;
    background-color: var(--vtc-bg-bg-white);
    border-radius: 50%;
    transform: rotate(-45deg);
    line-height: 48px;
    text-align: center;
    color: var(--vtc-bg-main-bg-3);
    font-size: 18px;
}
.case3 .image-area .heading-area p {
    color: var(--vtc-text-text-white-text-1);
    font-size: var(--f-fs-font-fs16);
    line-height: var(--f-fs-font-fs16);
    font-weight: var(--f-fw-medium);
    padding: 24px 0px 16px 0px;
}
.case3 .image-area .heading-area h4 a {
    display: inline-block;
    font-size: var(--f-fs-font-fs22);
    line-height: var(--f-fs-font-fs30);
    color: var(--vtc-bg-bg-white);
    font-weight: var(--f-fw-semibold);
    transition: all 0.4s;
}
.case3 .image-area:hover::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    background-color: var(--vtc-bg-main-bg-3);
    border-radius: 4px;
    transform: scale(0.9);
    opacity: 1;
    transition: all 0.4s;
}
.case3 .image-area:hover .heading-area {
    opacity: 1;
    transition: all 0.4s;
}
.case3 .image-area.image-area2 {
    max-height: 370px;
    overflow: hidden;
    border-radius: 4px;
}
.case3 .image-area.image-area3 {
    max-height: 770px;
    overflow: hidden;
    border-radius: 4px;
}
.case3 .image-area.active::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    background-color: var(--vtc-bg-main-bg-3);
    border-radius: 4px;
    transform: scale(0.9);
    opacity: 1;
    transition: all 0.4s;
}
.case3 .image-area.active .heading-area {
    opacity: 1;
    transition: all 0.4s;
}

.case7 .case7-slider {
    margin-top: 60px;
}
.case7 .case7-slider .single-slider {
    margin: 0px 15px;
    position: relative;
}
.case7 .case7-slider .single-slider .image {
    border-radius: 8px;
    overflow: hidden;
}
.case7 .case7-slider .single-slider .image img {
    width: 100%;
    transition: all 0.4s;
}
.case7 .case7-slider .single-slider .hover-area {
    background-color: #fff;
    padding: 24px;
    border-radius: 8px;
    position: absolute;
    bottom: 24px;
    margin: 0px 20px 0px 30px;
    left: 0;
    width: 85%;
}
.case7 .case7-slider .single-slider .hover-area p {
    color: #555;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px; /* 100% */
    text-transform: uppercase;
}
.case7 .case7-slider .single-slider .hover-area h4 a {
    display: inline-block;
    color: #081120;
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px; /* 100% */
    padding-top: 16px;
}
.case7 .case7-slider .single-slider .hover-area h4 a:hover {
    transition: all 0.4s;
    color: #5957e5;
}
.case7 .case7-slider .single-slider .hover-area .arrow {
    position: absolute;
    display: inline-block;
    height: 48px;
    width: 48px;
    background-color: #5957e5;
    line-height: 48px;
    text-align: center;
    border-radius: 50%;
    font-size: 20px;
    color: #fff;
    transform: rotate(-54deg);
    top: -20px;
    right: -20px;
}
.case7 .case7-slider .single-slider:hover {
    transition: all 0.4s;
}
.case7 .case7-slider .single-slider:hover .image img {
    transition: all 0.4s;
    transform: scale(1.1) rotate(2deg);
}
.case7 .arrows-button {
    text-align: end;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .case7 .arrows-button {
        text-align: center;
        margin-top: 20px;
    }
}
@media (max-width: 767px) {
    .case7 .arrows-button {
        text-align: center;
        margin-top: 20px;
    }
}
.case7 .arrows-button button {
    font-size: 20px;
    background-color: #f8f7ff;
    height: 56px;
    width: 56px;
    text-align: center;
    line-height: 56px;
    border: none;
    border-radius: 50%;
    margin-left: 10px;
    color: #081120;
    transition: all 0.4s;
}
.case7 .arrows-button button:hover {
    background-color: #5957e5;
    color: #fff;
    transition: all 0.4s;
}

.case9 .case7-slider {
    margin-top: 60px;
}
.case9 .case7-slider .single-slider {
    position: relative;
}
.case9 .case7-slider .single-slider .image {
    border-radius: 8px;
    overflow: hidden;
}
.case9 .case7-slider .single-slider .image img {
    width: 100%;
    transition: all 0.4s;
}
.case9 .case7-slider .single-slider .hover-area {
    background-color: #fff;
    padding: 24px;
    border-radius: 8px;
    position: absolute;
    bottom: 24px;
    margin: 0px 20px 0px 30px;
    left: 0;
    width: 85%;
}
.case9 .case7-slider .single-slider .hover-area p {
    color: #555;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px; /* 100% */
    text-transform: uppercase;
}
.case9 .case7-slider .single-slider .hover-area h4 a {
    display: inline-block;
    color: #081120;
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px; /* 100% */
    padding-top: 16px;
}
.case9 .case7-slider .single-slider .hover-area h4 a:hover {
    transition: all 0.4s;
    color: #2a9134;
}
.case9 .case7-slider .single-slider .hover-area .arrow {
    position: absolute;
    display: inline-block;
    height: 48px;
    width: 48px;
    background-color: #2a9134;
    line-height: 48px;
    text-align: center;
    border-radius: 50%;
    font-size: 20px;
    color: #fff;
    transform: rotate(-54deg);
    top: -20px;
    right: -20px;
}
.case9 .case7-slider .single-slider:hover {
    transition: all 0.4s;
}
.case9 .case7-slider .single-slider:hover .image img {
    transition: all 0.4s;
    transform: scale(1.1) rotate(2deg);
}
.case9 .arrows-button {
    text-align: end;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .case9 .arrows-button {
        text-align: center;
        margin-top: 20px;
    }
}
@media (max-width: 767px) {
    .case9 .arrows-button {
        text-align: center;
        margin-top: 20px;
    }
}
.case9 .arrows-button button {
    font-size: 20px;
    background-color: #eaf4eb;
    height: 56px;
    width: 56px;
    text-align: center;
    line-height: 56px;
    border: none;
    border-radius: 50%;
    margin-left: 10px;
    color: #2a9134;
    transition: all 0.4s;
}
.case9 .arrows-button button:hover {
    background-color: #2a9134;
    color: #fff;
    transition: all 0.4s;
}

.case10 .case-box {
    margin-top: 30px;
    position: relative;
}
@media (max-width: 767px) {
    .case10 .case-box {
        margin-top: 80px;
    }
}
.case10 .case-box .image {
    border-radius: 8px;
    overflow: hidden;
}
.case10 .case-box .image img {
    width: 100%;
    transition: all 0.4s;
}
.case10 .case-box .hover-area {
    background-color: #fff;
    padding: 24px;
    border-radius: 8px;
    position: absolute;
    bottom: 30px;
    margin: 0px 130px 0px 30px;
}
@media (max-width: 767px) {
    .case10 .case-box .hover-area {
        margin: 0px 0px 0px 0px;
    }
}
.case10 .case-box .hover-area h6 {
    color: #555;
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: 18px; /* 100% */
    text-transform: uppercase;
}
.case10 .case-box .hover-area h3 a {
    display: inline-block;
    color: #081120;
    font-size: 24px;
    font-style: normal;
    font-weight: 600;
    line-height: 30px; /* 125% */
    padding: 14px 0px;
    transition: all 0.4s;
}
.case10 .case-box .hover-area h3 a:hover {
    color: #fa6444;
    transition: all 0.4s;
}
.case10 .case-box .hover-area p {
    color: #555;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 26px; /* 162.5% */
}
.case10 .case-box .hover-area .arrow {
    display: inline-block;
    height: 56px;
    width: 56px;
    line-height: 56px;
    text-align: center;
    background-color: #fa6444;
    color: #fff;
    border-radius: 50%;
    font-size: 20px;
    transform: rotate(-45deg);
    position: absolute;
    right: -20px;
    top: -20px;
}
.case10 .case-box:hover {
    transition: all 0.4s;
}
.case10 .case-box:hover .image img {
    transition: all 0.4s;
    transform: scale(1.1) rotate(2deg);
}

/*
 ::::::::::::::::::::::::::
  CASE AREA CSS
 ::::::::::::::::::::::::::
 */
/*
::::::::::::::::::::::::::
 PROHECT AREA CSS
::::::::::::::::::::::::::
*/
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .project-two__single-box {
        margin-top: 30px;
    }
}
@media (max-width: 767px) {
    .project-two__single-box {
        margin-top: 30px;
    }
}

/*
::::::::::::::::::::::::::
 PROHECT AREA CSS
::::::::::::::::::::::::::
*/
/*
::::::::::::::::::::::::::
 TEAM AREA CSS
::::::::::::::::::::::::::
*/
.team1 {
    background-color: var(--vtc-bg-common-bg2);
}
.team1 .team1-box {
    margin-top: 30px;
    position: relative;
    border-radius: 4px;
    overflow: hidden;
    height: 450px;
}
.team1 .team1-box .image {
    border-radius: 4px;
    overflow: hidden;
}
.team1 .team1-box .image img {
    width: 100%;
    transition: all 0.4s;
}
.team1 .team1-box .heading-area {
    padding: 32px;
    background-color: var(--vtc-bg-bg-white);
    position: absolute;
    bottom: -65px;
    width: 100%;
    text-align: center;
    border-radius: 0px 0px 4px 4px;
    transition: all 0.4s;
}
.team1 .team1-box .heading-area .heading1 h4 a {
    display: inline-block;
    font-size: var(--f-fs-font-fs24);
    line-height: var(--f-fs-font-fs24);
    color: var(--vtc-text-heading-text-1);
    font-weight: var(--f-fw-bold);
    margin-bottom: 6px;
    transition: all 0.4s;
}
.team1 .team1-box .heading-area .heading1 h4 a:hover {
    transition: all 0.4s;
    color: var(--vtc-bg-main-bg-1);
}
.team1 .team1-box .heading-area .heading1 p {
    color: var(--vtc-text-pera-text-1);
}
.team1 .team1-box .heading-area .icons {
    opacity: 0;
    transition: all 0.4s;
}
.team1 .team1-box .heading-area .icons ul {
    margin-top: 20px;
}
.team1 .team1-box .heading-area .icons ul li {
    display: inline-block;
}
.team1 .team1-box .heading-area .icons ul li a {
    display: inline-block;
    height: 40px;
    width: 40px;
    line-height: 40px;
    text-align: center;
    border-radius: 50%;
    background-color: rgba(255, 124, 1, 0.1568627451);
    margin: 0px 3px;
    color: var(--vtc-text-heading-text-1);
    transition: all 0.4s;
}
.team1 .team1-box .heading-area .icons ul li a:hover {
    background-color: var(--vtc-bg-main-bg-1);
    color: var(--vtc-bg-bg-white);
    transition: all 0.4s;
}
.team1 .team1-box:hover .image img {
    transition: all 0.4s;
    transform: scale(1.1) rotate(2deg);
}
.team1 .team1-box:hover .heading-area {
    bottom: 0;
    transition: all 0.4s;
}
.team1 .team1-box:hover .heading-area .icons {
    transition: all 0.4s;
    opacity: 1;
}

.team3 {
    background-color: var(--vtc-bg-common-bg4);
}
.team3 .team3-box {
    margin-top: 30px;
    background-color: var(--vtc-bg-bg-white);
    border-radius: 4px;
}
.team3 .team3-box .image-area {
    position: relative;
}
.team3 .team3-box .image-area .image {
    overflow: hidden;
    border-radius: 4px 4px 0px 0px;
    position: relative;
}
.team3 .team3-box .image-area .image img {
    width: 100%;
    border-radius: 4px 4px 0px 0px;
    transition: all 0.4s;
}
.team3 .team3-box .image-area .image::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    background-color: var(--vtc-text-heading-text-1);
    opacity: 0;
    transform: scale(0.6);
    transition: all 0.4s;
}
.team3 .team3-box .image-area .icons {
    position: absolute;
    top: 50%;
    left: 50%;
    margin-top: -24px;
    margin-left: -100px;
    transform: translateY(60px);
    opacity: 0;
    transition: all 0.4s;
}
.team3 .team3-box .image-area .icons ul li {
    display: inline-block;
}
.team3 .team3-box .image-area .icons ul li a {
    display: inline-block;
    font-size: var(--f-fs-font-fs18);
    line-height: 40px;
    height: 40px;
    width: 40px;
    text-align: center;
    background-color: var(--vtc-bg-bg-white);
    border-radius: 50%;
    color: var(--vtc-text-heading-text-1);
    margin: 0px 3px;
    transition: all 0.4s;
}
.team3 .team3-box .image-area .icons ul li a:hover {
    color: var(--vtc-bg-bg-white);
    background-color: var(--vtc-bg-main-bg-3);
    transition: all 0.4s;
    transform: translateY(-3px);
}
.team3 .team3-box .heading3 {
    padding: 32px 3px;
    text-align: center;
}
.team3 .team3-box .heading3 p {
    padding-top: 6px;
}
.team3 .team3-box:hover .image-area .image img {
    transition: all 0.4s;
    transform: rotate(4deg) scale(1.1);
}
.team3 .team3-box:hover .image-area .image::after {
    opacity: 0.6;
    transform: scale(1);
    transition: all 0.4s;
}
.team3 .team3-box:hover .image-area .icons {
    transform: translateY(0);
    opacity: 1;
    transition: all 0.4s;
}

.team4 {
    background-color: var(--vtc-bg-common-bg7);
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .team4-box {
        margin-top: 30px;
    }
}
@media (max-width: 767px) {
    .team4-box {
        margin-top: 30px;
    }
}
.team4-box .heading4 {
    padding-top: 20px;
}
.team4-box .heading4 p {
    padding-top: 7px;
}
.team4-box .image-area {
    position: relative;
}
.team4-box .image-area .image {
    overflow: hidden;
    border-radius: 4px;
}
.team4-box .image-area .image img {
    width: 100%;
}
.team4-box .image-area::after {
    content: "";
    position: absolute;
    height: 100%;
    width: 100%;
    top: 0;
    left: 0;
    background-color: rgba(25, 51, 106, 0.4196078431);
    border-radius: 4px;
    transition: all 0.4s;
    transform: scale(0.7);
    opacity: 0;
}
.team4-box .image-area .icon {
    display: inline-block;
    border: 1px solid #fff;
    height: 60px;
    width: 60px;
    line-height: 60px;
    text-align: center;
    color: #fff;
    font-size: 24px;
    border-radius: 50%;
    transform: rotate(-45deg) scale(2);
    position: absolute;
    top: 50%;
    left: 50%;
    z-index: 3;
    margin-top: -30px;
    margin-left: -30px;
    opacity: 0;
    transition: all 0.4s;
}
.team4-box:hover .image-area::after {
    content: "";
    position: absolute;
    height: 100%;
    width: 100%;
    top: 0;
    left: 0;
    background-color: rgba(25, 51, 106, 0.4196078431);
    border-radius: 4px;
    transition: all 0.4s;
    transform: scale(1);
    opacity: 1;
}
.team4-box:hover .image-area .icon {
    display: inline-block;
    border: 1px solid #fff;
    height: 60px;
    width: 60px;
    line-height: 60px;
    text-align: center;
    color: #fff;
    font-size: 24px;
    border-radius: 50%;
    transform: rotate(-45deg) scale(1);
    position: absolute;
    top: 50%;
    left: 50%;
    z-index: 3;
    margin-top: -30px;
    margin-left: -30px;
    opacity: 1;
    transition: all 0.4s;
}

.team-page .team1-box {
    margin-bottom: 30px;
    position: relative;
    border-radius: 4px;
    overflow: hidden;
    height: 450px;
}
.team-page .team1-box .image {
    border-radius: 4px;
    overflow: hidden;
}
.team-page .team1-box .image img {
    width: 100%;
    transition: all 0.4s;
}
.team-page .team1-box .heading-area {
    padding: 32px;
    background-color: var(--vtc-bg-common-bg2);
    position: absolute;
    bottom: -65px;
    width: 100%;
    text-align: center;
    border-radius: 0px 0px 4px 4px;
    transition: all 0.4s;
}
.team-page .team1-box .heading-area .heading1 h4 a {
    display: inline-block;
    font-size: var(--f-fs-font-fs24);
    line-height: var(--f-fs-font-fs24);
    color: var(--vtc-text-heading-text-1);
    font-weight: var(--f-fw-bold);
    margin-bottom: 6px;
    transition: all 0.4s;
}
.team-page .team1-box .heading-area .heading1 h4 a:hover {
    transition: all 0.4s;
    color: var(--vtc-bg-main-bg-1);
}
.team-page .team1-box .heading-area .heading1 p {
    color: var(--vtc-text-pera-text-1);
}
.team-page .team1-box .heading-area .icons {
    opacity: 0;
    transition: all 0.4s;
}
.team-page .team1-box .heading-area .icons ul {
    margin-top: 20px;
}
.team-page .team1-box .heading-area .icons ul li {
    display: inline-block;
}
.team-page .team1-box .heading-area .icons ul li a {
    display: inline-block;
    height: 40px;
    width: 40px;
    line-height: 40px;
    text-align: center;
    border-radius: 50%;
    background-color: rgba(255, 124, 1, 0.1568627451);
    margin: 0px 3px;
    color: var(--vtc-text-heading-text-1);
    transition: all 0.4s;
}
.team-page .team1-box .heading-area .icons ul li a:hover {
    background-color: var(--vtc-bg-main-bg-1);
    color: var(--vtc-bg-bg-white);
    transition: all 0.4s;
}
.team-page .team1-box:hover .image img {
    transition: all 0.4s;
    transform: scale(1.1) rotate(2deg);
}
.team-page .team1-box:hover .heading-area {
    bottom: 0;
    transition: all 0.4s;
}
.team-page .team1-box:hover .heading-area .icons {
    transition: all 0.4s;
    opacity: 1;
}

.team6 {
    position: relative;
}
.team6 .team-box {
    margin-top: 30px;
    transition: all 0.4s;
}
.team6 .team-box .image {
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}
.team6 .team-box .image img {
    width: 100%;
    transition: all 0.4s;
}
.team6 .team-box .image .hover-area {
    position: absolute;
    right: 50px;
    bottom: 70px;
    z-index: 9;
    transform: translateX(100px);
    transition: all 0.4s;
}
.team6 .team-box .image .hover-area ul li a {
    display: inline-block;
    height: 40px;
    width: 40px;
    line-height: 40px;
    text-align: center;
    border-radius: 50%;
    background-color: #fff;
    margin-bottom: 8px;
    color: #180d03;
    font-size: 16px;
    transition: all 0.4s;
}
.team6 .team-box .image .hover-area ul li a:hover {
    background: #31572c;
    color: #fff;
    transition: all 0.4s;
}
.team6 .team-box .heading-area {
    border-radius: 4px;
    background: var(--Home-Page-3-white-colors, #fff);
    box-shadow: 0px 10px 48px 0px rgba(0, 0, 0, 0.09);
    padding: 24px;
    margin: -50px 24px 0px 24px;
    position: relative;
    z-index: 2;
    text-align: start;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.team6 .team-box .heading-area h4 a {
    display: inline-block;
    color: var(--Home-Page-3-text-Colors, #180d03);
    font-size: 24px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px; /* 100% */
    transition: all 0.4s;
}
.team6 .team-box .heading-area h4 a:hover {
    transition: all 0.4s;
    color: #f1c832;
}
.team6 .team-box .heading-area p {
    color: var(--Home-Page-3-paragraph, #5b5855);
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px; /* 100% */
    padding-top: 8px;
}
.team6 .team-box .heading-area .shere-icon {
    height: 50px;
    width: 50px;
    text-align: center;
    line-height: 50px;
    border-radius: 50%;
    background-color: #f1c832;
}
.team6 .team-box:hover {
    transform: translateY(-10px);
    transition: all 0.4s;
}
.team6 .team-box:hover .hover-area {
    transform: translateX(0px);
    transition: all 0.4s;
}
.team6 .team-box:hover .image img {
    transition: all 0.4s;
    transform: scale(1.1) rotate(2deg);
}
.team6 .sec-shape {
    position: absolute;
    right: 0;
    top: 0;
    z-index: -2;
}

.team7 {
    background-position: center bottom;
    background-repeat: no-repeat;
    background-size: cover;
    padding-top: 100px;
}
.team7 .main-image {
    margin-top: 100px;
}
@media (max-width: 767px) {
    .team7 .main-image {
        margin-top: 40px;
    }
}
.team7 .main-image img {
    width: 100%;
}
.team7 .team-box {
    position: absolute;
    bottom: 100px;
    padding: 20px 36px;
    border-radius: 8px;
    background-color: #fff;
    transition: all 0.4s;
    text-align: center;
}
@media (max-width: 767px) {
    .team7 .team-box {
        position: static;
        margin-top: 30px;
        background-color: #5957e5;
        padding: 20px 20px;
    }
    .team7 .team-box h5 a {
        color: #fff !important;
        transition: all 0.4s;
    }
    .team7 .team-box p {
        transition: all 0.4s;
        color: rgba(255, 255, 255, 0.7333333333) !important;
    }
}
.team7 .team-box:hover {
    transition: all 0.4s;
    transform: translateY(-10px);
    background-color: #5957e5;
}
.team7 .team-box:hover h5 a {
    color: #fff;
    transition: all 0.4s;
}
.team7 .team-box:hover p {
    transition: all 0.4s;
    color: rgba(255, 255, 255, 0.7333333333);
}
.team7 .team-box h5 a {
    display: inline-block;
    color: #081120;
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px; /* 100% */
    transition: all 0.4s;
}
.team7 .team-box p {
    color: #555;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 16px; /* 100% */
    padding-top: 12px;
    transition: all 0.4s;
}
.team7 .team-box.top {
    bottom: 200px;
}

.team8 {
    position: relative;
}
.team8 .team-box {
    margin-top: 30px;
    transition: all 0.4s;
}
.team8 .team-box .image {
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}
.team8 .team-box .image img {
    width: 100%;
    transition: all 0.4s;
}
.team8 .team-box .image .hover-area {
    position: absolute;
    right: 50px;
    bottom: 70px;
    z-index: 9;
    transform: translateX(100px);
    transition: all 0.4s;
}
.team8 .team-box .image .hover-area ul li a {
    display: inline-block;
    height: 40px;
    width: 40px;
    line-height: 40px;
    text-align: center;
    border-radius: 50%;
    background-color: #fff;
    margin-bottom: 8px;
    color: #180d03;
    font-size: 16px;
    transition: all 0.4s;
}
.team8 .team-box .image .hover-area ul li a:hover {
    background: #141339;
    color: #fff;
    transition: all 0.4s;
}
.team8 .team-box .heading-area {
    border-radius: 4px;
    background: var(--Home-Page-3-white-colors, #fff);
    box-shadow: 0px 10px 48px 0px rgba(0, 0, 0, 0.09);
    padding: 24px;
    margin: -50px 24px 0px 24px;
    position: relative;
    z-index: 2;
    text-align: start;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.team8 .team-box .heading-area h4 a {
    display: inline-block;
    color: var(--Home-Page-3-text-Colors, #180d03);
    font-size: 24px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px; /* 100% */
    transition: all 0.4s;
}
.team8 .team-box .heading-area h4 a:hover {
    transition: all 0.4s;
    color: #f6aa32;
}
.team8 .team-box .heading-area p {
    color: var(--Home-Page-3-paragraph, #5b5855);
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px; /* 100% */
    padding-top: 8px;
}
.team8 .team-box .heading-area .shere-icon {
    height: 50px;
    width: 50px;
    text-align: center;
    line-height: 50px;
    border-radius: 50%;
    background-color: #f6aa32;
}
.team8 .team-box:hover {
    transform: translateY(-10px);
    transition: all 0.4s;
}
.team8 .team-box:hover .hover-area {
    transform: translateX(0px);
    transition: all 0.4s;
}
.team8 .team-box:hover .image img {
    transition: all 0.4s;
    transform: scale(1.1) rotate(2deg);
}
.team8 .sec-shape {
    position: absolute;
    right: 0;
    top: 0;
    z-index: -2;
}

.team9 {
    background-position: center bottom;
    background-repeat: no-repeat;
    background-size: cover;
    padding-top: 100px;
}
.team9 .main-image {
    margin-top: 100px;
}
@media (max-width: 767px) {
    .team9 .main-image {
        margin-top: 40px;
    }
}
.team9 .main-image img {
    width: 100%;
}
.team9 .team-box {
    position: absolute;
    bottom: 100px;
    padding: 20px 36px;
    border-radius: 8px;
    background-color: #fff;
    transition: all 0.4s;
    text-align: center;
}
@media (max-width: 767px) {
    .team9 .team-box {
        position: static;
        margin-top: 30px;
        background-color: #2a9134;
        padding: 20px 20px;
    }
    .team9 .team-box h5 a {
        color: #fff !important;
        transition: all 0.4s;
    }
    .team9 .team-box p {
        transition: all 0.4s;
        color: rgba(255, 255, 255, 0.7333333333) !important;
    }
}
.team9 .team-box:hover {
    transition: all 0.4s;
    transform: translateY(-10px);
    background-color: #2a9134;
}
.team9 .team-box:hover h5 a {
    color: #fff;
    transition: all 0.4s;
}
.team9 .team-box:hover p {
    transition: all 0.4s;
    color: rgba(255, 255, 255, 0.7333333333);
}
.team9 .team-box h5 a {
    display: inline-block;
    color: #081120;
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px; /* 100% */
    transition: all 0.4s;
}
.team9 .team-box p {
    color: #555;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 16px; /* 100% */
    padding-top: 12px;
    transition: all 0.4s;
}
.team9 .team-box.top {
    bottom: 200px;
}

/*
::::::::::::::::::::::::::
 TEAM AREA CSS
::::::::::::::::::::::::::
*/
/*
::::::::::::::::::::::::::
 TESTIMONIAL AREA CSS
::::::::::::::::::::::::::
*/
/*--------------------------------------------------------------
# Project Two
--------------------------------------------------------------*/
.project-two {
    position: relative;
    display: block;
    padding: 140px 0 157px;
}

.project-two__top {
    position: relative;
    display: block;
    margin-bottom: 46px;
}

.project-two__top .section-title {
    margin-bottom: 0;
}

.project-two__bottom {
    position: relative;
    display: block;
}

.project-two__bottom .container {
    max-width: 1370px;
}

.project-two__box {
    position: relative;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin-left: -15px;
    margin-right: -15px;
}

.project-two__box li {
    position: relative;
    flex: 0 0 33.333333%;
    padding-left: 15px;
    padding-right: 15px;
    transition: background-color 0.5s ease;
    transition: all 0.5s ease;
}

.project-two__box li.active {
    flex: 0 0 50%;
}

.project-two__box.project-two__box2 li {
    flex: 0 0 50%;
}

.project-two__box-content {
    position: relative;
    display: block;
    overflow: hidden;
    min-height: 464px;
    z-index: 1;
}

.single-project-two__bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center center;
    transition: all 500ms ease;
    z-index: -1;
    border-radius: 5px;
}

.project-two__box-content-inner-wrapper {
    position: relative;
    display: block;
}

.project-two__box-content-inner {
    position: absolute;
    display: block;
    padding: 37px 50px;
    width: 89%;
    bottom: 30px;
    bottom: 30px;
    height: 120px;
    margin: 0px 30px;
    opacity: 0;
    transform: perspective(540px) rotateY(0deg) translateY(-100px);
    transform-origin: top;
    transition: all 500ms ease;
    z-index: 3;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    overflow: hidden;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: var(--vtc-bg-main-bg-1);
    border-radius: 4px;
}
@media (max-width: 767px) {
    .project-two__box-content-inner {
        height: 120px !important;
    }
}

.project-two__box-content:hover .project-two__box-content-inner {
    opacity: 1;
    transform: perspective(540px) rotateY(0deg) translateY(0px);
    transition-delay: 500ms;
}

.project-two__box-content.project-two__box-content2 .project-two__box-content-inner {
    opacity: 1;
    transform: perspective(540px) rotateY(0deg) translateY(0px);
    transition-delay: 500ms;
}

.project-two__box-content-inner .title-box {
    position: relative;
    display: block;
}

.project-two__box-content .img-holder-img-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: -ms-linear-gradient(90deg, rgb(29, 29, 29) 0%, rgba(29, 29, 29, 0) 100%);
    opacity: 0;
    transition: 0.5s;
    transform: perspective(540px) rotateX(-10deg);
    transform-origin: top;
    z-index: 1;
}

.project-two__box-content:hover .img-holder-img-bg {
    opacity: 1;
    transform: perspective(540px) rotateX(0deg);
}

.project-two__box-content-inner-icon {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: scale(0) translateY(-50%);
    opacity: 0;
    transition: all 500ms ease;
    z-index: 6;
    height: 0;
    line-height: 0;
}

.project-two__box-content:hover .project-two__box-content-inner-icon {
    transform: scale(1) translateY(-50%);
    transition-delay: 500ms;
    opacity: 1;
}

.project-two__box-content-inner-icon > a {
    position: relative;
    display: flex;
    width: 54px;
    height: 54px;
    line-height: 54px;
    text-align: center;
    border-radius: 50%;
    font-size: 21px;
    background-color: var(--bondor-primary);
    color: var(--bondor-black);
    align-items: center;
    justify-content: center;
    transition: all 500ms ease;
    z-index: 5;
}

.project-two__box-content-inner-icon > a:hover {
    background-color: var(--bondor-white);
    color: var(--bondor-black);
}

.project-two__box-content-inner-wrapper p {
    color: var(--vtc-text-text-white-text-1);
    font-size: var(--f-fs-font-fs16);
    line-height: var(--f-fs-font-fs16);
    font-weight: var(--f-fw-medium);
    padding-bottom: 10px;
}

.project-two__box-content-inner-wrapper h4 {
    font-size: 22px;
    font-weight: 700;
    line-height: 28px;
    letter-spacing: -0.016em;
}

.project-two__box-content-inner-wrapper h4 a {
    color: var(--vtc-text-text-white-text-1);
    transition: all 500ms ease;
}

.project-two__box-content-inner-wrapper h4 a:hover {
    color: var(--vtc-text-text-white-text-1);
}

@media (min-width: 768px) {
    .project-two__box li {
        flex: 1;
    }
    .project-two__box li.active {
        flex: 1.66;
    }
    .project-two__box.project-two__box2 li {
        flex: 1.66;
    }
    .project-two__box li.active .project-two__box-content .img-holder-img-bg {
        opacity: 0.8;
        transform: perspective(540px) rotateX(0deg);
    }
    .project-two__box li.active .project-two__box-content .project-two__box-content-inner {
        opacity: 1;
        transform: perspective(540px) rotateY(0deg) translateY(0px);
        transition-delay: 500ms;
    }
    .project-two__box li.active .project-two__box-content .project-two__box-content-inner .icon a {
        display: inline-block;
        color: var(--vtc-bg-main-bg-1);
        height: 48px;
        width: 48px;
        background-color: var(--vtc-text-text-white-text-1);
        border-radius: 50%;
        text-align: center;
        line-height: 48px;
        font-size: 20px;
        transform: rotate(-45deg);
    }
}
.project-two__single-box .icon a {
    display: inline-block;
    color: var(--vtc-bg-main-bg-1);
    height: 48px;
    width: 48px;
    background-color: var(--vtc-text-text-white-text-1);
    border-radius: 50%;
    text-align: center;
    line-height: 48px;
    font-size: 20px;
    transform: rotate(-45deg);
}

/* Mobile Layout: 320px. */
@media only screen and (max-width: 767px) {
    .project-two {
        padding: 100px 0 100px;
    }
    .project-two__box li.active {
        flex: 0 0 100%;
    }
    .project-two__box.project-two__box2 li {
        flex: 0 0 100%;
    }
    .project-two__box-content {
        min-height: 550px;
    }
    .project-two__box-content-inner {
        padding: 32px;
        height: 550px;
        display: flex;
        align-items: center;
    }
    .project-two__box-content-inner-wrapper h4 {
        font-size: 28px;
    }
}
.swiper-pagination .swiper-pagination-bullet {
    /* background: transparent; */
    display: block;
    border: none;
    color: inherit;
    padding: 0 !important;
    font: inherit;
    height: 16px;
    line-height: 10px;
    width: 16px;
    margin: 0 0px;
    border: none;
    border-radius: 50%;
    padding: 5px;
    margin: 3px;
    background-color: #cecfd2;
}
.swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
    background: var(--vtc-bg-main-bg-1);
}
.swiper-pagination {
    position: absolute;
    bottom: 0;
    width: 100%;
    right: 0;
    display: flex;
    justify-content: center;
    z-index: 100;
}
.swiper-pagination .swiper-pagination-bullet {
    position: relative;
    border: 3px solid #fff !important;
}
.swiper-pagination .swiper-pagination-bullet:after {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    background: var(--vtc-bg-main-bg-1);
    z-index: -1;
    border-radius: 50%;
    left: -4px;
    top: -4px;
    transition: all 0.3s;
    visibility: hidden;
    opacity: 0;
}
.swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active:after {
    visibility: visible;
    opacity: 1;
}

.project-two__carousel.owl-theme .owl-nav {
    position: absolute;
    top: -120px;
    right: 0px;
    margin: 0;
    z-index: 2;
}

.project-two__carousel.owl-theme .owl-nav .owl-next {
    height: 60px;
    width: 60px;
    line-height: 60px;
    border-radius: 50%;
    color: var(--bondor-black);
    background-color: rgb(255, 255, 255);
    box-shadow: 0px 0px 40px 0px rgba(18, 14, 14, 0.08);
    font-size: 20px;
    margin: 0;
    text-align: center;
    transition: all 500ms ease;
    position: relative;
    display: inline-block;
}

.project-two__carousel.owl-theme .owl-nav .owl-prev {
    height: 60px;
    width: 60px;
    line-height: 60px;
    border-radius: 50%;
    color: var(--bondor-black);
    background-color: rgb(255, 255, 255);
    box-shadow: 0px 0px 40px 0px rgba(18, 14, 14, 0.08);
    font-size: 20px;
    margin: 0;
    text-align: center;
    transition: all 500ms ease;
    position: relative;
    display: inline-block;
    transform: rotate(-180deg);
}

.project-two__carousel.owl-theme .owl-nav .owl-next {
    margin-left: 10px;
}

.project-two__carousel.owl-theme .owl-nav .owl-prev {
    margin-right: 10px;
}

.project-two__carousel.owl-theme .owl-nav .owl-next span,
.project-two__carousel.owl-theme .owl-nav .owl-prev span {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.project-two__carousel.owl-theme .owl-nav .owl-next:hover,
.project-two__carousel.owl-theme .owl-nav .owl-prev:hover {
    background-color: var(--bondor-base);
    color: var(--bondor-white);
}

.project-two__carousel.owl-theme .owl-nav .disabled {
    opacity: 1;
}

.tes1 .tes1-slider {
    margin-top: 30px;
}
.tes1 .tes1-slider .single-slider {
    padding: 32px;
    background-color: var(--vtc-bg-common-bg2);
    border-radius: 4px;
}
.tes1 .tes1-slider .single-slider ul.stars li {
    display: inline-block;
    background-color: rgba(255, 124, 1, 0.1294117647);
    height: 32px;
    width: 32px;
    line-height: 32px;
    text-align: center;
    border-radius: 4px;
    font-size: 18px;
    color: var(--vtc-bg-main-bg-1);
    margin-right: 5px;
}
.tes1 .tes1-slider .single-slider .pera p {
    color: #696b6d;
    font-size: var(--f-fs-font-fs20);
    line-height: var(--f-fs-font-fs30);
    font-weight: var(--f-fw-medium);
    padding: 24px 0px;
}
.tes1 .tes1-slider .single-slider .bottom-heading h4 a {
    font-size: var(--f-fs-font-fs24);
    line-height: var(--f-fs-font-fs24);
    color: var(--vtc-text-heading-text-1);
    font-weight: var(--f-fw-bold);
}
.tes1 .tes1-slider .single-slider .bottom-heading p {
    color: #696b6d;
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs18);
    font-weight: var(--f-fw-medium);
    padding-top: 10px;
}
.tes1 .tes7-buttons {
    text-align: end;
}
.tes1 .tes7-buttons button {
    height: 48px;
    width: 48px;
    border: none;
    border-radius: 4px;
    background-color: var(--vtc-bg-common-bg1);
    margin-right: 10px;
    color: var(--vtc-text-heading-text-1);
    transition: all 0.4s;
}
.tes1 .tes7-buttons button:hover {
    color: var(--vtc-text-text-white-text-1);
    background-color: var(--vtc-bg-main-bg-1);
    transition: all 0.4s;
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .tes2 {
        padding-bottom: 120px;
    }
}
@media (max-width: 767px) {
    .tes2 {
        padding-bottom: 120px;
    }
}
.tes2 .image {
    margin-right: 40px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .tes2 .image {
        margin-right: 0px;
        margin-bottom: 40px;
    }
}
@media (max-width: 767px) {
    .tes2 .image {
        margin-right: 0px;
        margin-bottom: 40px;
    }
}
.tes2 .image img {
    width: 100%;
}
.tes2 .tes2-slider .tes2-signle-slider p {
    color: var(--vtc-text-text-white-text-1);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-medium);
    padding: 24px 0px;
}
.tes2 .tes2-slider .tes2-signle-slider .icon {
    width: 60px;
}
.tes2 .tes2-slider .tes2-signle-slider .bottom-area {
    display: flex;
    align-items: center;
}
.tes2 .tes2-slider .tes2-signle-slider .bottom-area .image-bottom img {
    width: 50px;
}
.tes2 .tes2-slider .tes2-signle-slider .bottom-area .heading-bottom {
    margin-left: 24px;
}
.tes2 .tes2-slider .tes2-signle-slider .bottom-area .heading-bottom h5 {
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-bold);
    color: var(--vtc-text-text-white-text-1);
}
.tes2 .tes2-slider .tes2-signle-slider .bottom-area .heading-bottom h5 span {
    display: inline-block;
    font-weight: var(--f-fw-regular);
    font-size: 16px;
}
.tes2 .tes2-slider {
    padding-bottom: 65px;
}
.tes2 .swiper-pagination .swiper-pagination-bullet {
    background: transparent;
    border: none;
    color: inherit;
    padding: 0 !important;
    font: inherit;
    height: 16px;
    line-height: 10px;
    width: 16px;
    margin: 0 0px;
    border: none;
    border-radius: 50%;
    padding: 5px;
    margin: 3px;
    background-color: var(--vtc-bg-main-bg-2);
}
.tes2 .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
    background: var(--vtc-bg-main-bg-2);
}
.tes2 .swiper-pagination {
    position: absolute;
    width: 100%;
    right: 0;
    justify-content: start;
}
.tes2 .swiper-pagination .swiper-pagination-bullet {
    position: relative;
    border: 3px solid #000443 !important;
}
.tes2 .swiper-pagination .swiper-pagination-bullet:after {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    background: var(--vtc-bg-main-bg-2);
    z-index: -1;
    border-radius: 50%;
    left: -4px;
    top: -4px;
    transition: all 0.3s;
    visibility: hidden;
    opacity: 0;
}
.tes2 .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active:after {
    visibility: visible;
    opacity: 1;
}

.tes3 .single-slider .single-slider-content {
    background-color: var(--vtc-bg-common-bg4);
    border-radius: 4px;
    padding: 40px;
    position: relative;
    border: 1px solid #fffaec;
    transition: all 0.4s;
}
.tes3 .single-slider .single-slider-content::after {
    content: "";
    position: absolute;
    bottom: -23px;
    left: 60px;
    height: 56px;
    width: 70px;
    background-color: var(--vtc-bg-main-bg-3);
    border-radius: 4px;
    transform: rotate(45deg);
    background-color: var(--vtc-bg-common-bg4);
    z-index: -2;
}
.tes3 .single-slider .single-slider-content ul.stars {
    margin-bottom: 24px;
}
.tes3 .single-slider .single-slider-content ul.stars li {
    display: inline-block;
    background-color: #ffe6cf;
    height: 32px;
    width: 32px;
    line-height: 32px;
    text-align: center;
    border-radius: 4px;
    font-size: 16px;
    color: var(--vtc-bg-main-bg-3);
    margin-right: 5px;
}
.tes3 .single-slider .single-slider-content .pera p {
    color: #696b6d;
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-medium);
}
.tes3 .single-slider .bottom-heading {
    display: flex;
    align-items: center;
    margin-top: 50px;
}
.tes3 .single-slider .bottom-heading .heading3 {
    padding-left: 20px;
}
.tes3 .single-slider.swiper-slide.swiper-slide-active .single-slider-content {
    border: 1px solid var(--vtc-bg-main-bg-3);
    transition: all 0.4s;
}
.tes3 .single-slider.swiper-slide.swiper-slide-active .single-slider-content::after {
    background-color: var(--vtc-bg-main-bg-3);
}
.tes3 .tes7-buttons {
    text-align: end;
}
.tes3 .tes7-buttons button {
    height: 48px;
    width: 48px;
    border: none;
    border-radius: 4px;
    background-color: #e9ebea;
    margin-right: 10px;
    color: var(--vtc-text-heading-text-1);
    transition: all 0.4s;
    font-size: 18px;
}
.tes3 .tes7-buttons button:hover {
    color: var(--vtc-text-text-white-text-1);
    background-color: var(--vtc-bg-main-bg-3);
    transition: all 0.4s;
}

.tes4 {
    background-color: var(--vtc-bg-common-bg7);
}
.tes4 ul.stars {
    margin-bottom: 24px;
}
.tes4 ul.stars li {
    display: inline-block;
    background-color: var(--vtc-bg-common-bg7);
    height: 32px;
    width: 32px;
    line-height: 32px;
    text-align: center;
    border-radius: 4px;
    font-size: 16px;
    color: var(--vtc-bg-main-bg-6);
    margin-right: 5px;
}
.tes4 .tes4-slider .single-slider {
    background-color: var(--vtc-text-text-white-text-1);
    border-radius: 4px;
    padding: 32px;
}
.tes4 .tes4-slider .single-slider .bottom-heading {
    display: flex;
    align-items: center;
    margin-top: 32px;
}
.tes4 .tes4-slider .single-slider .bottom-heading .heading4 {
    padding-left: 20px;
}
.tes4 .tes4-slider .single-slider .bottom-heading .heading4 p {
    padding-top: 6px;
}
.tes4 .tes7-buttons {
    text-align: end;
}
.tes4 .tes7-buttons button {
    height: 48px;
    width: 48px;
    border: none;
    border-radius: 4px;
    background-color: rgba(82, 180, 233, 0.1176470588);
    margin-right: 10px;
    color: var(--vtc-text-heading-text-1);
    transition: all 0.4s;
    font-size: 18px;
}
.tes4 .tes7-buttons button:hover {
    color: var(--vtc-text-text-white-text-1);
    background-color: var(--vtc-bg-main-bg-5);
    transition: all 0.4s;
}

.tes2 .tes5-bg {
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    padding: 100px 30px;
    border-radius: 4px;
    margin-top: -250px;
    position: relative;
    z-index: 2;
}

.testimonial-page .single-slider {
    background-color: var(--vtc-bg-common-bg2);
    border-radius: 4px;
    padding: 32px;
    margin-bottom: 30px;
    transition: all 0.4s;
}
.testimonial-page .single-slider ul.stars {
    margin-bottom: 24px;
}
.testimonial-page .single-slider ul.stars li {
    display: inline-block;
    background-color: #f6e7dc;
    height: 32px;
    width: 32px;
    line-height: 32px;
    text-align: center;
    border-radius: 4px;
    font-size: 16px;
    color: var(--vtc-bg-main-bg-1);
    margin-right: 5px;
}
.testimonial-page .single-slider .bottom-heading {
    display: flex;
    align-items: center;
    margin-top: 32px;
}
.testimonial-page .single-slider .bottom-heading .heading1 {
    padding-left: 20px;
}
.testimonial-page .single-slider .bottom-heading .heading1 p {
    padding-top: 6px;
}
.testimonial-page .single-slider:hover {
    transform: translateY(-10px);
    transition: all 0.4s;
}

.tes6 {
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
}
.tes6 .arrows-button {
    text-align: end;
}
.tes6 .arrows-button button {
    display: inline-block;
    height: 56px;
    width: 56px;
    text-align: center;
    line-height: 56px;
    border-radius: 50%;
    font-size: 20px;
    color: #000443;
    background-color: #fff;
    transition: all 0.4s;
    border: none;
    margin-right: 7px;
}
.tes6 .arrows-button button:hover {
    background-color: #ffa800;
    transition: all 0.4s;
}
.tes6 .tes6-all-slider .single-slider {
    background-color: #fff;
    padding: 24px;
    border-radius: 4px;
}
.tes6 .tes6-all-slider .single-slider p {
    color: #071a1c;
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 28px; /* 155.556% */
    padding-top: 16px;
}
.tes6 .tes6-all-slider .single-slider .stars {
    padding-top: 4px;
}
.tes6 .tes6-all-slider .single-slider .stars li {
    display: inline-block;
    color: #ffa800;
    font-size: 15px;
    margin-right: 4px;
}
.tes6 .tes6-all-slider .single-slider .bottom-area {
    display: flex;
    align-items: center;
    padding-top: 20px;
    margin-top: 20px;
    border-top: 1px solid #e6e8e8;
}
.tes6 .tes6-all-slider .single-slider .bottom-area .heading {
    padding-left: 20px;
}
.tes6 .tes6-all-slider .single-slider .bottom-area .heading a {
    display: inline-block;
    color: #071a1c;
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px; /* 100% */
}
.tes6 .tes6-all-slider .single-slider .bottom-area .heading p {
    color: #4d5050;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px; /* 100% */
    padding-top: 10px;
}

.case6 {
    position: relative;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
    /* Mobile Layout: 320px. */
}
.case6 .project-two {
    position: relative;
    display: block;
    padding: 100px 0 100px;
}
.case6 .project-two .owl-nav {
    position: absolute;
    top: -99px;
    right: 0;
}
.case6 .project-two .owl-nav button {
    height: 56px;
    width: 56px;
    text-align: center;
    line-height: 56px;
    font-size: 20px;
    background-color: #fff;
    border-radius: 50%;
    transition: all 0.4s;
    margin-right: 10px;
}
.case6 .project-two .owl-nav button:hover {
    background-color: #f1c832;
    transition: all 0.4s;
}
.case6 .project-two__top {
    position: relative;
    display: block;
    margin-bottom: 46px;
}
.case6 .project-two__top .section-title {
    margin-bottom: 0;
}
.case6 .project-two__bottom {
    position: relative;
    display: block;
}
.case6 .project-two__bottom .container {
    max-width: 1370px;
}
.case6 .project-two__box {
    position: relative;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin-left: -15px;
    margin-right: -15px;
}
.case6 .project-two__box li {
    position: relative;
    flex: 0 0 33.333333%;
    padding-left: 15px;
    padding-right: 15px;
    transition: background-color 0.5s ease;
    transition: all 0.5s ease;
}
.case6 .project-two__box li.active {
    flex: 0 0 50%;
}
.case6 .project-two__box.project-two__box2 li {
    flex: 0 0 50%;
}
.case6 .project-two__box-content {
    position: relative;
    display: block;
    overflow: hidden;
    min-height: 464px;
    z-index: 1;
}
.case6 .single-project-two__bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center center;
    transition: all 500ms ease;
    z-index: -1;
    border-radius: 5px;
}
.case6 .project-two__box-content-inner-wrapper {
    position: relative;
    display: block;
}
.case6 .project-two__box-content-inner {
    position: absolute;
    display: block;
    padding: 37px 50px;
    width: 89%;
    bottom: 30px;
    bottom: 30px;
    height: 120px;
    margin: 0px 30px;
    opacity: 0;
    transform: perspective(540px) rotateY(0deg) translateY(-100px);
    transform-origin: top;
    transition: all 500ms ease;
    z-index: 3;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    overflow: hidden;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #fff;
    border-radius: 8px;
}
@media (max-width: 767px) {
    .case6 .project-two__box-content-inner {
        height: 120px !important;
    }
}
.case6 .project-two__box-content-inner .icon a {
    background: #f1c832 !important;
    color: #000 !important;
}
.case6 .project-two__box-content:hover .project-two__box-content-inner {
    opacity: 1;
    transform: perspective(540px) rotateY(0deg) translateY(0px);
    transition-delay: 500ms;
}
.case6 .project-two__box-content.project-two__box-content2 .project-two__box-content-inner {
    opacity: 1;
    transform: perspective(540px) rotateY(0deg) translateY(0px);
    transition-delay: 500ms;
}
.case6 .project-two__box-content-inner .title-box {
    position: relative;
    display: block;
}
.case6 .project-two__box-content .img-holder-img-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: -ms-linear-gradient(90deg, rgb(29, 29, 29) 0%, rgba(29, 29, 29, 0) 100%);
    opacity: 0;
    transition: 0.5s;
    transform: perspective(540px) rotateX(-10deg);
    transform-origin: top;
    z-index: 1;
}
.case6 .project-two__box-content:hover .img-holder-img-bg {
    opacity: 1;
    transform: perspective(540px) rotateX(0deg);
}
.case6 .project-two__box-content-inner-icon {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: scale(0) translateY(-50%);
    opacity: 0;
    transition: all 500ms ease;
    z-index: 6;
    height: 0;
    line-height: 0;
}
.case6 .project-two__box-content:hover .project-two__box-content-inner-icon {
    transform: scale(1) translateY(-50%);
    transition-delay: 500ms;
    opacity: 1;
}
.case6 .project-two__box-content-inner-icon > a {
    position: relative;
    display: flex;
    width: 54px;
    height: 54px;
    line-height: 54px;
    text-align: center;
    border-radius: 50%;
    font-size: 21px;
    background-color: var(--bondor-primary);
    color: var(--bondor-black);
    align-items: center;
    justify-content: center;
    transition: all 500ms ease;
    z-index: 5;
}
.case6 .project-two__box-content-inner-icon > a:hover {
    background-color: var(--bondor-white);
    color: var(--bondor-black);
}
.case6 .project-two__box-content-inner-wrapper p {
    color: var(--SEO-Marketing-Paragraph-Color, #5d6369);
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px; /* 100% */
    text-transform: uppercase;
    padding-bottom: 10px;
}
.case6 .project-two__box-content-inner-wrapper h4 {
    font-size: 22px;
    font-weight: 700;
    line-height: 28px;
    letter-spacing: -0.016em;
}
.case6 .project-two__box-content-inner-wrapper h4 a {
    color: var(--SEO-Marketing-Text-Color, #0b0314);
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px; /* 100% */
    transition: all 500ms ease;
}
.case6 .project-two__box-content-inner-wrapper h4 a:hover {
    color: var(--SEO-Marketing-Text-Color, #0b0314);
}
@media (min-width: 768px) {
    .case6 .project-two__box li {
        flex: 1;
    }
    .case6 .project-two__box li.active {
        flex: 1.66;
    }
    .case6 .project-two__box.project-two__box2 li {
        flex: 1.66;
    }
    .case6 .project-two__box li.active .project-two__box-content .img-holder-img-bg {
        opacity: 0.8;
        transform: perspective(540px) rotateX(0deg);
    }
    .case6 .project-two__box li.active .project-two__box-content .project-two__box-content-inner {
        opacity: 1;
        transform: perspective(540px) rotateY(0deg) translateY(0px);
        transition-delay: 500ms;
    }
    .case6 .project-two__box li.active .project-two__box-content .project-two__box-content-inner .icon a {
        display: inline-block;
        color: var(--vtc-bg-main-bg-1);
        height: 48px;
        width: 48px;
        background-color: var(--vtc-text-text-white-text-1);
        border-radius: 50%;
        text-align: center;
        line-height: 48px;
        font-size: 20px;
        transform: rotate(-45deg);
    }
}
.case6 .project-two__single-box .icon a {
    display: inline-block;
    color: var(--vtc-bg-main-bg-1);
    height: 48px;
    width: 48px;
    background-color: var(--vtc-text-text-white-text-1);
    border-radius: 50%;
    text-align: center;
    line-height: 48px;
    font-size: 20px;
    transform: rotate(-45deg);
}
@media only screen and (max-width: 767px) {
    .case6 .project-two {
        padding: 100px 0 100px;
    }
    .case6 .project-two__box li.active {
        flex: 0 0 100%;
    }
    .case6 .project-two__box.project-two__box2 li {
        flex: 0 0 100%;
    }
    .case6 .project-two__box-content {
        min-height: 550px;
    }
    .case6 .project-two__box-content-inner {
        padding: 32px;
        height: 550px;
        display: flex;
        align-items: center;
    }
    .case6 .project-two__box-content-inner-wrapper h4 {
        font-size: 28px;
    }
}
.case6 .owl-carousel.owl-dot-style1 .swiper-pagination .swiper-pagination-bullet {
    background: transparent;
    border: none;
    color: inherit;
    padding: 0 !important;
    font: inherit;
    height: 16px;
    line-height: 10px;
    width: 16px;
    margin: 0 0px;
    border: none;
    border-radius: 50%;
    padding: 5px;
    margin: 3px;
    background-color: #cecfd2;
}
.case6 .owl-carousel.owl-dot-style1 .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
    background: var(--vtc-bg-main-bg-1);
}
.case6 .owl-carousel.owl-dot-style1 {
    padding-bottom: 50px;
}
.case6 .owl-carousel.owl-dot-style1 .swiper-pagination {
    position: absolute;
    width: 100%;
    right: 0;
    text-align: center;
}
.case6 .owl-carousel.owl-dot-style1 .swiper-pagination .swiper-pagination-bullet {
    position: relative;
    border: 3px solid #fff !important;
}
.case6 .owl-carousel.owl-dot-style1 .swiper-pagination .swiper-pagination-bullet:after {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    background: var(--vtc-bg-main-bg-1);
    z-index: -1;
    border-radius: 50%;
    left: -4px;
    top: -4px;
    transition: all 0.3s;
    visibility: hidden;
    opacity: 0;
}
.case6 .owl-carousel.owl-dot-style1 .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active:after {
    visibility: visible;
    opacity: 1;
}
.case6 .project-two__carousel.owl-theme .owl-nav {
    position: absolute;
    top: -120px;
    right: 0px;
    margin: 0;
    z-index: 2;
}
.case6 .project-two__carousel.owl-theme .owl-nav .owl-next {
    height: 60px;
    width: 60px;
    line-height: 60px;
    border-radius: 50%;
    color: var(--bondor-black);
    background-color: rgb(255, 255, 255);
    box-shadow: 0px 0px 40px 0px rgba(18, 14, 14, 0.08);
    font-size: 20px;
    margin: 0;
    text-align: center;
    transition: all 500ms ease;
    position: relative;
    display: inline-block;
}
.case6 .project-two__carousel.owl-theme .owl-nav .owl-prev {
    height: 60px;
    width: 60px;
    line-height: 60px;
    border-radius: 50%;
    color: var(--bondor-black);
    background-color: rgb(255, 255, 255);
    box-shadow: 0px 0px 40px 0px rgba(18, 14, 14, 0.08);
    font-size: 20px;
    margin: 0;
    text-align: center;
    transition: all 500ms ease;
    position: relative;
    display: inline-block;
    transform: rotate(-180deg);
}
.case6 .project-two__carousel.owl-theme .owl-nav .owl-next {
    margin-left: 10px;
}
.case6 .project-two__carousel.owl-theme .owl-nav .owl-prev {
    margin-right: 10px;
}
.case6 .project-two__carousel.owl-theme .owl-nav .owl-next span,
.case6 .project-two__carousel.owl-theme .owl-nav .owl-prev span {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}
.case6 .project-two__carousel.owl-theme .owl-nav .owl-next:hover,
.case6 .project-two__carousel.owl-theme .owl-nav .owl-prev:hover {
    background-color: var(--bondor-base);
    color: var(--bondor-white);
}
.case6 .project-two__carousel.owl-theme .owl-nav .disabled {
    opacity: 1;
}

.case8 {
    position: relative;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
    /* Mobile Layout: 320px. */
}
.case8 .project-two {
    position: relative;
    display: block;
    padding: 100px 0 100px;
}
.case8 .project-two .owl-nav {
    position: absolute;
    top: -99px;
    right: 0;
}
.case8 .project-two .owl-nav button {
    height: 56px;
    width: 56px;
    text-align: center;
    line-height: 56px;
    font-size: 20px;
    background-color: #fff;
    border-radius: 50%;
    transition: all 0.4s;
    margin-right: 10px;
}
.case8 .project-two .owl-nav button:hover {
    background-color: #f6aa32;
    transition: all 0.4s;
}
.case8 .project-two__top {
    position: relative;
    display: block;
    margin-bottom: 46px;
}
.case8 .project-two__top .section-title {
    margin-bottom: 0;
}
.case8 .project-two__bottom {
    position: relative;
    display: block;
}
.case8 .project-two__bottom .container {
    max-width: 1370px;
}
.case8 .project-two__box {
    position: relative;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin-left: -15px;
    margin-right: -15px;
}
.case8 .project-two__box li {
    position: relative;
    flex: 0 0 33.333333%;
    padding-left: 15px;
    padding-right: 15px;
    transition: background-color 0.5s ease;
    transition: all 0.5s ease;
}
.case8 .project-two__box li.active {
    flex: 0 0 50%;
}
.case8 .project-two__box.project-two__box2 li {
    flex: 0 0 50%;
}
.case8 .project-two__box-content {
    position: relative;
    display: block;
    overflow: hidden;
    min-height: 464px;
    z-index: 1;
}
.case8 .single-project-two__bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center center;
    transition: all 500ms ease;
    z-index: -1;
    border-radius: 5px;
}
.case8 .project-two__box-content-inner-wrapper {
    position: relative;
    display: block;
}
.case8 .project-two__box-content-inner {
    position: absolute;
    display: block;
    padding: 37px 50px;
    width: 89%;
    bottom: 30px;
    bottom: 30px;
    height: 120px;
    margin: 0px 30px;
    opacity: 0;
    transform: perspective(540px) rotateY(0deg) translateY(-100px);
    transform-origin: top;
    transition: all 500ms ease;
    z-index: 3;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    overflow: hidden;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #fff;
    border-radius: 8px;
}
@media (max-width: 767px) {
    .case8 .project-two__box-content-inner {
        height: 120px !important;
    }
}
.case8 .project-two__box-content-inner .icon a {
    background: #f6aa32 !important;
    color: #000 !important;
}
.case8 .project-two__box-content:hover .project-two__box-content-inner {
    opacity: 1;
    transform: perspective(540px) rotateY(0deg) translateY(0px);
    transition-delay: 500ms;
}
.case8 .project-two__box-content.project-two__box-content2 .project-two__box-content-inner {
    opacity: 1;
    transform: perspective(540px) rotateY(0deg) translateY(0px);
    transition-delay: 500ms;
}
.case8 .project-two__box-content-inner .title-box {
    position: relative;
    display: block;
}
.case8 .project-two__box-content .img-holder-img-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: -ms-linear-gradient(90deg, rgb(29, 29, 29) 0%, rgba(29, 29, 29, 0) 100%);
    opacity: 0;
    transition: 0.5s;
    transform: perspective(540px) rotateX(-10deg);
    transform-origin: top;
    z-index: 1;
}
.case8 .project-two__box-content:hover .img-holder-img-bg {
    opacity: 1;
    transform: perspective(540px) rotateX(0deg);
}
.case8 .project-two__box-content-inner-icon {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: scale(0) translateY(-50%);
    opacity: 0;
    transition: all 500ms ease;
    z-index: 6;
    height: 0;
    line-height: 0;
}
.case8 .project-two__box-content:hover .project-two__box-content-inner-icon {
    transform: scale(1) translateY(-50%);
    transition-delay: 500ms;
    opacity: 1;
}
.case8 .project-two__box-content-inner-icon > a {
    position: relative;
    display: flex;
    width: 54px;
    height: 54px;
    line-height: 54px;
    text-align: center;
    border-radius: 50%;
    font-size: 21px;
    background-color: var(--bondor-primary);
    color: var(--bondor-black);
    align-items: center;
    justify-content: center;
    transition: all 500ms ease;
    z-index: 5;
}
.case8 .project-two__box-content-inner-icon > a:hover {
    background-color: var(--bondor-white);
    color: var(--bondor-black);
}
.case8 .project-two__box-content-inner-wrapper p {
    color: var(--SEO-Marketing-Paragraph-Color, #5d6369);
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px; /* 100% */
    text-transform: uppercase;
    padding-bottom: 10px;
}
.case8 .project-two__box-content-inner-wrapper h4 {
    font-size: 22px;
    font-weight: 700;
    line-height: 28px;
    letter-spacing: -0.016em;
}
.case8 .project-two__box-content-inner-wrapper h4 a {
    color: var(--SEO-Marketing-Text-Color, #0b0314);
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px; /* 100% */
    transition: all 500ms ease;
}
.case8 .project-two__box-content-inner-wrapper h4 a:hover {
    color: var(--SEO-Marketing-Text-Color, #0b0314);
}
@media (min-width: 768px) {
    .case8 .project-two__box li {
        flex: 1;
    }
    .case8 .project-two__box li.active {
        flex: 1.66;
    }
    .case8 .project-two__box.project-two__box2 li {
        flex: 1.66;
    }
    .case8 .project-two__box li.active .project-two__box-content .img-holder-img-bg {
        opacity: 0.8;
        transform: perspective(540px) rotateX(0deg);
    }
    .case8 .project-two__box li.active .project-two__box-content .project-two__box-content-inner {
        opacity: 1;
        transform: perspective(540px) rotateY(0deg) translateY(0px);
        transition-delay: 500ms;
    }
    .case8 .project-two__box li.active .project-two__box-content .project-two__box-content-inner .icon a {
        display: inline-block;
        color: var(--vtc-bg-main-bg-1);
        height: 48px;
        width: 48px;
        background-color: var(--vtc-text-text-white-text-1);
        border-radius: 50%;
        text-align: center;
        line-height: 48px;
        font-size: 20px;
        transform: rotate(-45deg);
    }
}
.case8 .project-two__single-box .icon a {
    display: inline-block;
    color: var(--vtc-bg-main-bg-1);
    height: 48px;
    width: 48px;
    background-color: var(--vtc-text-text-white-text-1);
    border-radius: 50%;
    text-align: center;
    line-height: 48px;
    font-size: 20px;
    transform: rotate(-45deg);
}
@media only screen and (max-width: 767px) {
    .case8 .project-two {
        padding: 100px 0 100px;
    }
    .case8 .project-two__box li.active {
        flex: 0 0 100%;
    }
    .case8 .project-two__box.project-two__box2 li {
        flex: 0 0 100%;
    }
    .case8 .project-two__box-content {
        min-height: 550px;
    }
    .case8 .project-two__box-content-inner {
        padding: 32px;
        height: 550px;
        display: flex;
        align-items: center;
    }
    .case8 .project-two__box-content-inner-wrapper h4 {
        font-size: 28px;
    }
}
.case8 .owl-carousel.owl-dot-style1 .swiper-pagination button {
    background: transparent;
    border: none;
    color: inherit;
    padding: 0 !important;
    font: inherit;
    height: 16px;
    line-height: 10px;
    width: 16px;
    margin: 0 0px;
    border: none;
    border-radius: 50%;
    padding: 5px;
    margin: 3px;
    background-color: #cecfd2;
}
.case8 .owl-carousel.owl-dot-style1 .swiper-pagination button.active {
    background: var(--vtc-bg-main-bg-1);
}
.case8 .owl-carousel.owl-dot-style1 .swiper-pagination {
    position: absolute;
    bottom: -55px;
    width: 100%;
    right: 0;
    text-align: center;
}
.case8 .owl-carousel.owl-dot-style1 .swiper-pagination button {
    position: relative;
    border: 3px solid #fff !important;
}
.case8 .owl-carousel.owl-dot-style1 .swiper-pagination button:after {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    background: var(--vtc-bg-main-bg-1);
    z-index: -1;
    border-radius: 50%;
    left: -4px;
    top: -4px;
    transition: all 0.3s;
    visibility: hidden;
    opacity: 0;
}
.case8 .owl-carousel.owl-dot-style1 .swiper-pagination button.active:after {
    visibility: visible;
    opacity: 1;
}
.case8 .project-two__carousel.owl-theme .owl-nav {
    position: absolute;
    top: -120px;
    right: 0px;
    margin: 0;
    z-index: 2;
}
.case8 .project-two__carousel.owl-theme .owl-nav .owl-next {
    height: 60px;
    width: 60px;
    line-height: 60px;
    border-radius: 50%;
    color: var(--bondor-black);
    background-color: rgb(255, 255, 255);
    box-shadow: 0px 0px 40px 0px rgba(18, 14, 14, 0.08);
    font-size: 20px;
    margin: 0;
    text-align: center;
    transition: all 500ms ease;
    position: relative;
    display: inline-block;
}
.case8 .project-two__carousel.owl-theme .owl-nav .owl-prev {
    height: 60px;
    width: 60px;
    line-height: 60px;
    border-radius: 50%;
    color: var(--bondor-black);
    background-color: rgb(255, 255, 255);
    box-shadow: 0px 0px 40px 0px rgba(18, 14, 14, 0.08);
    font-size: 20px;
    margin: 0;
    text-align: center;
    transition: all 500ms ease;
    position: relative;
    display: inline-block;
    transform: rotate(-180deg);
}
.case8 .project-two__carousel.owl-theme .owl-nav .owl-next {
    margin-left: 10px;
}
.case8 .project-two__carousel.owl-theme .owl-nav .owl-prev {
    margin-right: 10px;
}
.case8 .project-two__carousel.owl-theme .owl-nav .owl-next span,
.case8 .project-two__carousel.owl-theme .owl-nav .owl-prev span {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}
.case8 .project-two__carousel.owl-theme .owl-nav .owl-next:hover,
.case8 .project-two__carousel.owl-theme .owl-nav .owl-prev:hover {
    background-color: var(--bondor-base);
    color: var(--bondor-white);
}
.case8 .project-two__carousel.owl-theme .owl-nav .disabled {
    opacity: 1;
}

/*
::::::::::::::::::::::::::
 TESTIMONIAL AREA CSS
::::::::::::::::::::::::::
*/
.testimonial4-section-area {
    position: relative;
}
@media (max-width: 767px) {
    .testimonial4-section-area {
        padding: 50px 0px 160px 0px;
    }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .testimonial4-section-area {
        padding: 50px 0px 120px 0px;
    }
}
.testimonial4-section-area .testimonial3-header {
    margin-bottom: 60px;
}
.testimonial4-section-area .testimonial3-header span {
    padding: 8px 10px;
    display: inline-block;
    border-radius: 4px;
    margin-bottom: 24px;
}
.testimonial4-section-area .testimonial3-header h1 {
    line-height: 54px;
}
@media (max-width: 767px) {
    .testimonial4-section-area .testimonial3-header h1 {
        line-height: 42px;
    }
}
.testimonial4-section-area .testiomnial-img3 {
    width: 100%;
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
}
.testimonial4-section-area .slider-galeria-thumbs.text-center.swiper-initialized.swiper-vertical {
    position: relative;
    left: 50px;
}
.testimonial4-section-area .testimonial3-slider-content-area {
    border-radius: 0.933px;
    background: #fff;
    padding: 40px 32px;
}
.testimonial4-section-area .testimonial3-slider-content-area .testimonial3-author-area {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.testimonial4-section-area .testimonial3-slider-content-area .testimonial3-author-area ul li {
    display: inline-block;
}
.testimonial4-section-area .testimonial3-slider-content-area .testimonial3-author-area ul li a {
    height: 40px;
    width: 40px;
    text-align: center;
    display: inline-block;
    background: #f4f3ef;
    color: #ffba00;
    line-height: 40px;
    border-radius: 4px;
    margin-right: 4px;
}
.testimonial4-section-area .testimonial3-slider-content-area p {
    color: rgba(8, 17, 32, 0.7607843137);
    font-size: 28px;
    font-style: normal;
    font-weight: 500;
    line-height: 43px; /* 153.571% */
    padding-top: 10px;
}
@media (max-width: 767px) {
    .testimonial4-section-area .testimonial3-slider-content-area p {
        font-size: 20px;
        line-height: 28px;
    }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .testimonial4-section-area .testimonial3-slider-content-area p {
        font-size: 20px;
        line-height: 28px;
    }
}
.testimonial4-section-area .testimonial3-slider-content-area .testimonial3-man-info-area {
    display: flex;
    align-items: center;
    margin-top: 20px;
}
.testimonial4-section-area .testimonial3-slider-content-area .testimonial3-man-info-area .mans-img img {
    height: 100px;
    width: 100px;
    -o-object-fit: cover;
    object-fit: cover;
}
.testimonial4-section-area .testimonial3-slider-content-area .testimonial3-man-info-area .man3-text {
    margin-left: 16px;
}
.testimonial4-section-area .testimonial3-slider-content-area .testimonial3-man-info-area .man3-text p {
    color: var(--visa-immigrant-consulting-paragraph-color, #585452);
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 18px; /* 100% */
    padding: 0;
    margin-top: 10px;
}
.testimonial4-section-area .testimonial3-slider-content-area .testimonial3-man-info-area .man3-text a {
    display: inline-block;
    color: var(--visa-immigrant-consulting-text-color, #100906);
    font-size: 27.988px;
    font-style: normal;
    font-weight: 600;
    line-height: 27.988px; /* 100% */
}
.testimonial4-section-area .slider-galeria-thumbs {
    width: 100%;
}
.testimonial4-section-area .slider-galeria-thumbs .testimonial3-sliders-img {
    margin-bottom: 20px;
}
@media (max-width: 767px) {
    .testimonial4-section-area .slider-galeria-thumbs .testimonial3-sliders-img {
        text-align: center;
        position: relative;
        left: 26%;
        right: 26%;
        top: 20px;
    }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .testimonial4-section-area .slider-galeria-thumbs .testimonial3-sliders-img {
        text-align: center;
        position: relative;
        left: 26%;
        right: 26%;
        top: 20px;
    }
}
.testimonial4-section-area .slider-galeria-thumbs .testimonial3-sliders-img img {
    width: 80px;
    height: 80px;
    -o-object-fit: cover;
    object-fit: cover;
}
.testimonial4-section-area .testimonial-sliders {
    margin-bottom: 100px;
    position: relative;
}
@media (max-width: 767px) {
    .testimonial4-section-area .testimonial-sliders {
        margin-bottom: 50px;
    }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .testimonial4-section-area .testimonial-sliders {
        margin-bottom: 50px;
    }
}
.testimonial4-section-area .testimonial-sliders .testimonial-arrows2 {
    position: absolute;
    bottom: 0px;
    right: 268px;
    display: flex;
    align-items: center;
    z-index: 1;
}
@media (max-width: 767px) {
    .testimonial4-section-area .testimonial-sliders .testimonial-arrows2 {
        right: 50%;
        top: auto;
        position: absolute;
        bottom: -145px;
        margin-right: -80px;
    }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .testimonial4-section-area .testimonial-sliders .testimonial-arrows2 {
        right: 50px;
        bottom: 0;
    }
}
.testimonial4-section-area .testimonial-sliders .testimonial-arrows2 button {
    height: 60px;
    width: 60px;
    text-align: center;
    display: inline-block;
    line-height: 60px;
    border-radius: 4px;
    background: rgba(89, 87, 229, 0.1529411765);
    transition: all 0.4s ease-in-out;
    color: #081120;
    border: none;
    outline: none;
    margin: 0 8px 0 0;
    font-size: 22px;
    border-radius: 50%;
}
.testimonial4-section-area .testimonial-sliders .testimonial-arrows2 button:hover {
    transition: all 0.4s ease-in-out;
    background: #5957e5;
    color: #fff;
}
.testimonial4-section-area .testimonial-sliders .slider-galeria {
    float: left;
    width: 100%;
}
.testimonial4-section-area .scetion-background {
    position: absolute;
    left: 0;
}
.testimonial4-section-area .slider-galeria {
    position: relative;
    top: 60px;
}
.testimonial4-section-area .slider-galeria-thumbs {
    position: relative;
    top: 60px;
}
.testimonial4-section-area .testimonial3-sliders-img.swiper-slide.swiper-slide-fully-visible.swiper-slide-thumb-active:after {
    position: absolute;
    content: "";
    height: 100px;
    width: 100px;
    z-index: -1;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border-radius: 50%;
    background-color: #5957e5;
}
.testimonial4-section-area .testimonial3-sliders-img.swiper-slide.swiper-slide-fully-visible.swiper-slide-thumb-active {
    position: relative;
}
.testimonial4-section-area .slider-galeria-thumbs.text-center.swiper-initialized.swiper.swiper-vertical img {
    position: relative;
    left: 1px;
    top: 12px;
}

.tes8 .tes8-slider-area {
    background-color: #fff;
    border-radius: 8px;
    padding: 32px 50px 32px 32px;
    position: relative;
}
.tes8 .tes8-slider-area .single-slider p {
    color: var(--Home-Page-1-text-colors, #02000e);
    font-size: 24px;
    font-style: normal;
    font-weight: 500;
    line-height: 36px; /* 150% */
    padding: 16px 0px;
}
.tes8 .tes8-slider-area .single-slider .bottom-area {
    display: flex;
    align-items: center;
}
.tes8 .tes8-slider-area .single-slider .bottom-area .heading {
    padding-left: 16px;
}
.tes8 .tes8-slider-area .single-slider .bottom-area .heading h5 a {
    display: inline-block;
    color: var(--Home-Page-1-text-colors, #02000e);
    font-size: 24px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px; /* 100% */
    transition: all 0.4s;
}
.tes8 .tes8-slider-area .single-slider .bottom-area .heading h5 a:hover {
    color: #f6aa32;
    transition: all 0.4s;
}
.tes8 .tes8-slider-area .single-slider .bottom-area .heading span {
    color: var(--Home-Page-1-pargraph, #5c5a64);
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px; /* 100% */
    padding-top: 12px;
    display: inline-block;
}
.tes8 .tes8-slider-area .arrows-button button {
    font-size: 22px;
    height: 60px;
    width: 60px;
    text-align: center;
    line-height: 60px;
    border: none;
    border-radius: 50%;
    background-color: #141339;
    color: #fff;
    transition: all 0.4s;
}
.tes8 .tes8-slider-area .arrows-button button:hover {
    background-color: #f6aa32;
    transition: all 0.4s;
}
.tes8 .tes8-slider-area .arrows-button .tes8-next-arrow {
    position: absolute;
    top: 50%;
    margin-top: -65px;
    right: -30px;
}
.tes8 .tes8-slider-area .arrows-button .tes8-prev-arrow {
    position: absolute;
    top: 50%;
    margin-top: 5px;
    right: -30px;
}

.tes9 {
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
}
.tes9 .arrows-button {
    text-align: end;
}
.tes9 .arrows-button button {
    display: inline-block;
    height: 56px;
    width: 56px;
    text-align: center;
    line-height: 56px;
    border-radius: 50%;
    font-size: 20px;
    color: #2a9134;
    background-color: #fff;
    transition: all 0.4s;
    border: none;
    margin-right: 7px;
}
.tes9 .arrows-button button:hover {
    background-color: #2a9134;
    color: #fff;
    transition: all 0.4s;
}
.tes9 .tes6-all-slider .single-slider {
    background-color: #fff;
    padding: 24px;
    border-radius: 4px;
}
.tes9 .tes6-all-slider .single-slider p {
    color: #071a1c;
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 28px; /* 155.556% */
    padding-top: 16px;
}
.tes9 .tes6-all-slider .single-slider .stars {
    padding-top: 4px;
}
.tes9 .tes6-all-slider .single-slider .stars li {
    display: inline-block;
    color: #ffa800;
    font-size: 15px;
    margin-right: 4px;
}
.tes9 .tes6-all-slider .single-slider .bottom-area {
    display: flex;
    align-items: center;
    padding-top: 20px;
    margin-top: 20px;
    border-top: 1px solid #e6e8e8;
}
.tes9 .tes6-all-slider .single-slider .bottom-area .heading {
    padding-left: 20px;
}
.tes9 .tes6-all-slider .single-slider .bottom-area .heading a {
    display: inline-block;
    color: #071a1c;
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px; /* 100% */
}
.tes9 .tes6-all-slider .single-slider .bottom-area .heading p {
    color: #4d5050;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px; /* 100% */
    padding-top: 10px;
}

.tes10 {
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
}
.tes10 .arrows-button {
    text-align: end;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .tes10 .arrows-button {
        text-align: start;
        margin-top: 30px;
    }
}
.tes10 .arrows-button button {
    display: inline-block;
    height: 56px;
    width: 56px;
    text-align: center;
    line-height: 56px;
    border-radius: 50%;
    font-size: 20px;
    color: #fa6444;
    background-color: #f7f5fb;
    transition: all 0.4s;
    border: none;
    margin-right: 7px;
}
.tes10 .arrows-button button:hover {
    background-color: #fa6444;
    color: #fff;
    transition: all 0.4s;
}
.tes10 .tes6-all-slider .single-slider {
    background-color: #f7f5fb;
    padding: 24px;
    border-radius: 4px;
}
.tes10 .tes6-all-slider .single-slider p {
    color: #071a1c;
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 28px; /* 155.556% */
    padding-top: 16px;
}
.tes10 .tes6-all-slider .single-slider .stars {
    padding-top: 4px;
}
.tes10 .tes6-all-slider .single-slider .stars li {
    display: inline-block;
    color: #ffa800;
    font-size: 15px;
    margin-right: 4px;
}
.tes10 .tes6-all-slider .single-slider .bottom-area {
    display: flex;
    align-items: center;
    padding-top: 20px;
    margin-top: 20px;
    border-top: 1px solid #e6e8e8;
}
.tes10 .tes6-all-slider .single-slider .bottom-area .heading {
    padding-left: 20px;
}
.tes10 .tes6-all-slider .single-slider .bottom-area .heading a {
    display: inline-block;
    color: #071a1c;
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px; /* 100% */
}
.tes10 .tes6-all-slider .single-slider .bottom-area .heading p {
    color: #4d5050;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px; /* 100% */
    padding-top: 10px;
}

/*
::::::::::::::::::::::::::
 WORK AREA CSS
::::::::::::::::::::::::::
*/
.work1 {
    background-color: var(--vtc-bg-common-bg2);
}
.work1 .work1-heading {
    padding-left: 30px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .work1 .work1-heading {
        padding-left: 0;
        margin-top: 40px;
    }
}
@media (max-width: 767px) {
    .work1 .work1-heading {
        padding-left: 0;
        margin-top: 40px;
    }
}
.work1 .work1-box {
    padding: 24px;
    border-radius: 4px;
    background-color: var(--vtc-bg-bg-white);
    margin-top: 20px;
    display: flex;
    transition: all 0.4s;
}
.work1 .work1-box .icon {
    background-color: var(--vtc-bg-main-bg-1);
    height: 60px;
    width: 60px;
    border-radius: 50%;
    text-align: center;
    line-height: 60px;
    margin-right: 16px;
    transition: all 0.4s;
}
.work1 .work1-box .icon img {
    transition: all 0.4s;
    filter: brightness(0) invert(1);
}
.work1 .work1-box .heading1 h4 a {
    padding-bottom: 10px;
}
.work1 .work1-box:hover {
    transition: all 0.4s;
    transform: translateY(-10px);
    background-color: var(--vtc-bg-main-bg-1);
}
.work1 .work1-box:hover .icon {
    background-color: var(--vtc-bg-bg-white);
    transition: all 0.4s;
}
.work1 .work1-box:hover .icon img {
    transition: all 0.4s;
    filter: none;
}
.work1 .work1-box:hover .heading1 h4 a {
    transition: all 0.4s;
    color: var(--vtc-bg-bg-white);
}
.work1 .work1-box:hover .heading1 p {
    color: rgba(255, 255, 255, 0.8431372549);
    transition: all 0.4s;
}
.work1 .work-img img {
    width: 100%;
}

.work3 {
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    position: relative;
}
.work3 .work3-box {
    margin-top: 30px;
    position: relative;
}
.work3 .work3-box .image {
    margin-bottom: 24px;
    position: relative;
    z-index: 2;
}
.work3 .work3-box.work3-box2 {
    position: relative;
}
.work3 .work3-box.work3-box2::after {
    content: "";
    position: absolute;
    top: 19px;
    left: 0;
    height: 1px;
    width: 107%;
    background: white;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .work3 .work3-box.work3-box2::after {
        display: none;
    }
}
@media (max-width: 767px) {
    .work3 .work3-box.work3-box2::after {
        display: none;
    }
}
.work3 .shape {
    position: absolute;
    top: 0;
    left: 0;
}

.work6 {
    position: relative;
}
.work6 .work-box {
    position: relative;
    margin-top: 30px;
    padding: 0px 10px;
}
.work6 .work-box .image-area {
    position: relative;
    text-align: center;
}
.work6 .work-box .image-area .image1 img {
    width: 100%;
}
.work6 .work-box .image-area .image2 {
    position: absolute;
    top: 85px;
    left: 61px;
}
.work6 .work-box .image-area .image2 img {
    width: 100%;
}
.work6 .work-box .heading-area {
    padding: 20px 34px;
    background-color: #f3f5f2;
    margin-top: 32px;
    text-align: center;
    transition: all 0.4s;
    border-radius: 8px;
}
.work6 .work-box .heading-area h3 a {
    display: inline-block;
    color: #000;
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px; /* 100% */
    text-transform: uppercase;
    transition: all 0.4s;
}
.work6 .work-box .heading-area p {
    color: #555;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 26px; /* 162.5% */
    padding-top: 12px;
    transition: all 0.4s;
}
.work6 .work-box:hover .heading-area {
    transition: all 0.4s;
    background-color: #31572c;
    transform: translateY(-10px);
}
.work6 .work-box:hover .heading-area h3 a {
    transition: all 0.4s;
    color: #fff;
}
.work6 .work-box:hover .heading-area p {
    color: rgba(255, 255, 255, 0.8156862745);
    transition: all 0.4s;
}
.work6 .sec-shape {
    position: absolute;
    right: 0;
    z-index: -2;
    top: 0;
}

.work7 .work87-box {
    margin-top: 30px;
    position: relative;
    text-align: center;
    padding: 0px 40px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .work7 .work87-box {
        padding: 0px 0px;
    }
}
@media (max-width: 767px) {
    .work7 .work87-box {
        padding: 0px 0px;
    }
}
.work7 .work87-box .icon {
    height: 70px;
    width: 70px;
    line-height: 70px;
    text-align: center;
    background-color: #fff;
    border-radius: 50%;
    margin: auto;
}
.work7 .work87-box .icon img {
    transition: all 0.4s;
}
.work7 .work87-box .heading {
    padding-top: 16px;
}
.work7 .work87-box .heading h4 a {
    display: inline-block;
    color: #fff;
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px; /* 100% */
    text-transform: uppercase;
}
.work7 .work87-box .heading p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 26px; /* 162.5% */
    padding-top: 10px;
}
.work7 .work87-box .shape1 {
    position: absolute;
    top: 60px;
    right: -120px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .work7 .work87-box .shape1 {
        display: none;
    }
}
@media (max-width: 767px) {
    .work7 .work87-box .shape1 {
        display: none;
    }
}
.work7 .work87-box .shape2 {
    position: absolute;
    top: 60px;
    right: -120px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .work7 .work87-box .shape2 {
        display: none;
    }
}
@media (max-width: 767px) {
    .work7 .work87-box .shape2 {
        display: none;
    }
}
.work7 .work87-box:hover .icon img {
    transition: all 0.4s;
    transform: rotateY(180deg);
}

.work8 .work87-box {
    margin-top: 30px;
    position: relative;
    text-align: center;
    padding: 0px 40px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .work8 .work87-box {
        padding: 0px 0px;
    }
}
@media (max-width: 767px) {
    .work8 .work87-box {
        padding: 0px 0px;
    }
}
.work8 .work87-box .icon {
    height: 70px;
    width: 70px;
    line-height: 70px;
    text-align: center;
    background-color: #f7f5fb;
    border-radius: 50%;
    margin: auto;
}
.work8 .work87-box .icon img {
    transition: all 0.4s;
}
.work8 .work87-box .heading {
    padding-top: 16px;
}
.work8 .work87-box .heading h4 a {
    display: inline-block;
    color: #081120;
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px; /* 100% */
    text-transform: uppercase;
}
.work8 .work87-box .heading p {
    color: #555555;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 26px; /* 162.5% */
    padding-top: 10px;
}
.work8 .work87-box .shape1 {
    position: absolute;
    top: 60px;
    right: -120px;
    filter: brightness(0);
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .work8 .work87-box .shape1 {
        display: none;
    }
}
@media (max-width: 767px) {
    .work8 .work87-box .shape1 {
        display: none;
    }
}
.work8 .work87-box .shape2 {
    position: absolute;
    top: 60px;
    right: -120px;
    filter: brightness(0);
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .work8 .work87-box .shape2 {
        display: none;
    }
}
@media (max-width: 767px) {
    .work8 .work87-box .shape2 {
        display: none;
    }
}
.work8 .work87-box:hover .icon img {
    transition: all 0.4s;
    transform: rotateY(180deg);
}

.work9 {
    position: relative;
}
.work9 .work-box {
    position: relative;
    margin-top: 30px;
    padding: 0px 10px;
}
.work9 .work-box .image-area {
    position: relative;
    text-align: center;
}
.work9 .work-box .image-area .image1 img {
    width: 100%;
}
.work9 .work-box .image-area .image2 {
    position: absolute;
    top: 85px;
    left: 61px;
}
.work9 .work-box .image-area .image2 img {
    width: 100%;
}
.work9 .work-box .heading-area {
    padding: 20px 34px;
    background-color: #f3f5f2;
    margin-top: 32px;
    text-align: center;
    transition: all 0.4s;
    border-radius: 8px;
}
.work9 .work-box .heading-area h3 a {
    display: inline-block;
    color: #000;
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px; /* 100% */
    text-transform: uppercase;
    transition: all 0.4s;
}
.work9 .work-box .heading-area p {
    color: #555;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 26px; /* 162.5% */
    padding-top: 12px;
    transition: all 0.4s;
}
.work9 .work-box:hover .heading-area {
    transition: all 0.4s;
    background-color: #2a9134;
    transform: translateY(-10px);
}
.work9 .work-box:hover .heading-area h3 a {
    transition: all 0.4s;
    color: #fff;
}
.work9 .work-box:hover .heading-area p {
    color: rgba(255, 255, 255, 0.8156862745);
    transition: all 0.4s;
}
.work9 .sec-shape {
    position: absolute;
    right: 0;
    z-index: -2;
    top: 0;
}

.work10 .work-box {
    margin-top: 30px;
    transition: all 0.4s;
}
.work10 .work-box.top {
    margin-top: 60px;
}
.work10 .work-box .image-area {
    position: relative;
}
.work10 .work-box .image-area .image {
    margin: 0px 30px;
    background-color: #f7f5fb;
    border-radius: 8px;
    padding: 10px;
}
.work10 .work-box .image-area .image img {
    width: 100%;
}
.work10 .work-box .image-area .step {
    background-color: #fa6444;
    color: #fff;
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 16px; /* 100% */
    text-transform: uppercase;
    padding: 8px 18px;
    border-radius: 8px;
    position: absolute;
    bottom: 30px;
}
.work10 .work-box .heading {
    padding: 20px 34px;
    background-color: #f7f5fb;
    border-radius: 8px;
    margin-top: 30px;
    text-align: center;
    transition: all 0.4s;
}
.work10 .work-box .heading h4 a {
    color: #000;
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px; /* 100% */
    text-transform: uppercase;
    transition: all 0.4s;
}
.work10 .work-box .heading p {
    color: #555;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 26px; /* 162.5% */
    transition: all 0.4s;
    padding-top: 12px;
}
.work10 .work-box:hover {
    transform: translateY(-10px);
    transition: all 0.4s;
}
.work10 .work-box:hover .heading {
    background-color: #fa6444;
    transition: all 0.4s;
    transform: translateY(-5px);
}
.work10 .work-box:hover .heading h4 a {
    transition: all 0.4s;
    color: #fff;
}
.work10 .work-box:hover .heading p {
    color: rgba(255, 255, 255, 0.8352941176);
    transition: all 0.4s;
}

/*
 ::::::::::::::::::::::::::
  WORK AREA CSS
 ::::::::::::::::::::::::::
*/
/*
::::::::::::::::::::::::::
 BLOG AREA CSS
::::::::::::::::::::::::::
*/
.blog1-box {
    position: relative;
    margin-top: 30px;
}
.blog1-box .heading-area {
    background-color: var(--vtc-bg-common-bg2);
    border-radius: 4px;
    padding: 24px;
    margin-left: 160px;
    position: absolute;
    bottom: 30px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .blog1-box .heading-area {
        margin-left: 0;
        position: static;
    }
}
@media (max-width: 767px) {
    .blog1-box .heading-area {
        margin-left: 0;
        position: static;
    }
}
.blog1-box .heading-area .tags {
    margin-bottom: 10px;
}
.blog1-box .heading-area .tags a {
    display: inline-block;
    color: var(--vtc-text-pera-text-1);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-medium);
    transition: all 0.4s;
    margin-right: 30px;
}
.blog1-box .heading-area .tags a img {
    margin-top: -3px;
    margin-right: 3px;
}
.blog1-box .heading-area .heading1 h4 a {
    line-height: var(--f-fs-font-fs26);
    transition: all 0.4s;
}
.blog1-box .heading-area .heading1 h4 a:hover {
    transition: all 0.4s;
    color: var(--vtc-bg-main-bg-1);
}
.blog1-box .heading-area .heading1 .blog1-border {
    background-color: rgba(255, 124, 1, 0.1647058824);
    width: 100%;
    height: 1px;
    margin-top: 20px;
}
.blog1-box .heading-area .heading1 a.learn {
    display: inline-block;
    color: var(--vtc-text-heading-text-1);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-bold);
    margin-top: 20px;
    transition: all 0.4s;
}
.blog1-box .heading-area .heading1 a.learn:hover {
    transition: all 0.4s;
    color: var(--vtc-bg-main-bg-1);
}
.blog1-box .heading-area .heading1 a.learn span {
    display: inline-block;
    transform: rotate(-45deg);
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .blog1-box .image img {
        width: 100%;
    }
}
@media (max-width: 767px) {
    .blog1-box .image img {
        width: 100%;
    }
}

.blog2 .blog2-box {
    margin-top: 30px;
    background-color: #192b65;
    transition: all 0.4s;
    border-radius: 4px;
}
.blog2 .blog2-box .image {
    overflow: hidden;
    border-radius: 4px 4px 0px 0px;
}
.blog2 .blog2-box .image img {
    width: 100%;
    transition: all 0.4s;
}
.blog2 .blog2-box .heading2 {
    padding: 24px;
}
.blog2 .blog2-box .heading2 h4 a {
    line-height: var(--f-fs-font-fs28);
    font-size: var(--f-fs-font-fs20);
    transition: all 0.4s;
}
.blog2 .blog2-box .heading2 h4 a:hover {
    transition: all 0.4s;
    color: var(--vtc-bg-main-bg-2);
}
.blog2 .blog2-box .heading2 .tags {
    margin-bottom: 12px;
}
.blog2 .blog2-box .heading2 .tags a {
    display: inline-block;
    color: var(--vtc-text-pera-text-2);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-medium);
    margin-right: 20px;
}
.blog2 .blog2-box .heading2 .tags a img {
    margin-top: -3px;
    margin-left: 3px;
}
.blog2 .blog2-box a.learn {
    display: inline-block;
    color: var(--vtc-bg-common-bg1);
    font-size: var(--f-fs-font-fs16);
    line-height: var(--f-fs-font-fs16);
    font-weight: var(--f-fw-medium);
    transition: all 0.4s;
}
.blog2 .blog2-box a.learn span {
    display: inline-block;
    transform: rotate(-45deg);
    margin-left: 3px;
}
.blog2 .blog2-box a.learn:hover {
    transition: all 0.4s;
    color: var(--vtc-bg-main-bg-2);
}
.blog2 .blog2-box:hover {
    transition: all 0.4s;
    transform: translateY(-10px);
}
.blog2 .blog2-box:hover .image img {
    transition: all 0.4s;
    transform: scale(1.1) rotate(4deg);
}

.blog3 .blog3-box {
    margin-top: 30px;
    position: relative;
}
.blog3 .blog3-box .image {
    border-radius: 4px;
    overflow: hidden;
}
.blog3 .blog3-box .image img {
    transition: all 0.4s;
    width: 100%;
}
.blog3 .blog3-box .heading-area {
    background-color: var(--vtc-bg-bg-white);
    border-radius: 0px 4px 4px 0px;
    padding: 24px;
    margin-right: 160px;
    position: absolute;
    bottom: 30px;
    left: 0;
    box-shadow: rgba(0, 0, 0, 0.2) 0px 20px 30px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .blog3 .blog3-box .heading-area {
        margin-right: 0;
        position: static;
    }
}
@media (max-width: 767px) {
    .blog3 .blog3-box .heading-area {
        margin-right: 0;
        position: static;
    }
}
.blog3 .blog3-box .heading-area .tags {
    margin-bottom: 10px;
}
.blog3 .blog3-box .heading-area .tags a {
    display: inline-block;
    color: var(--vtc-text-pera-text-1);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-medium);
    transition: all 0.4s;
    margin-right: 30px;
}
.blog3 .blog3-box .heading-area .tags a img {
    margin-top: -3px;
    margin-right: 3px;
}
.blog3 .blog3-box .heading-area .heading3 h4 a {
    line-height: var(--f-fs-font-fs28);
    transition: all 0.4s;
}
.blog3 .blog3-box .heading-area .heading3 h4 a:hover {
    transition: all 0.4s;
    color: var(--vtc-bg-main-bg-1);
}
.blog3 .blog3-box .heading-area .heading3 .blog1-border {
    background-color: rgba(35, 52, 46, 0.0745098039);
    width: 100%;
    height: 1px;
    margin-top: 20px;
}
.blog3 .blog3-box .heading-area .heading3 a.learn {
    display: inline-block;
    color: var(--vtc-text-heading-text-1);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-bold);
    margin-top: 20px;
    transition: all 0.4s;
}
.blog3 .blog3-box .heading-area .heading3 a.learn:hover {
    transition: all 0.4s;
    color: var(--vtc-bg-main-bg-1);
}
.blog3 .blog3-box .heading-area .heading3 a.learn span {
    display: inline-block;
    transform: rotate(-45deg);
}
.blog3 .blog3-box:hover .image img {
    transform: scale(1.1) rotate(2deg);
    transition: all 0.4s;
}

.blog4-box {
    margin-top: 30px;
    background-color: var(--vtc-bg-common-bg7);
    border-radius: 4px 4px 0px 0px;
    transition: all 0.4s;
}
.blog4-box .image {
    border-radius: 4px;
    overflow: hidden;
}
.blog4-box .image img {
    width: 100%;
    transition: all 0.4s;
}
.blog4-box .heading4 {
    padding: 24px;
}
.blog4-box .heading4 h5 a {
    line-height: 26px;
}
.blog4-box .heading4 .tags {
    margin-bottom: 14px;
}
.blog4-box .heading4 .tags a {
    display: inline-block;
    color: var(--vtc-text-pera-text-1);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-medium);
    transition: all 0.4s;
    margin-right: 30px;
}
.blog4-box .heading4 .tags a img {
    margin-top: -3px;
    margin-right: 3px;
}
.blog4-box .heading4 a.learn {
    display: inline-block;
    color: var(--vtc-text-heading-text-1);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-bold);
    margin-top: 20px;
    transition: all 0.4s;
}
.blog4-box .heading4 a.learn:hover {
    transition: all 0.4s;
    color: var(--vtc-bg-main-bg-6);
}
.blog4-box .heading4 a.learn span {
    display: inline-block;
    transform: rotate(-45deg);
}
.blog4-box:hover {
    transform: translateY(-10px);
    transition: all 0.4s;
}
.blog4-box:hover .image img {
    transform: scale(1.1) rotate(2deg);
    transition: all 0.4s;
}

.blog5 .blog2-box {
    margin-top: 30px;
    background-color: #fff;
    transition: all 0.4s;
    border-radius: 4px;
    border: none;
}
.blog5 .blog2-box .image {
    overflow: hidden;
    border-radius: 4px 4px 0px 0px;
}
.blog5 .blog2-box .image img {
    width: 100%;
    transition: all 0.4s;
}
.blog5 .blog2-box .heading5 {
    padding: 24px;
    box-shadow: rgba(0, 0, 0, 0.068) 0px 20px 30px;
    transition: all 0.3s ease-in-out;
}
.blog5 .blog2-box .heading5 h4 a {
    line-height: var(--f-fs-font-fs28);
    font-size: var(--f-fs-font-fs20);
    transition: all 0.4s;
}
.blog5 .blog2-box .heading5 h4 a:hover {
    transition: all 0.4s;
    color: var(--vtc-bg-main-bg-2);
}
.blog5 .blog2-box .heading5 .tags {
    margin-bottom: 12px;
}
.blog5 .blog2-box .heading5 .tags a {
    display: inline-block;
    color: #081120;
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-medium);
    margin-right: 20px;
}
.blog5 .blog2-box .heading5 .tags a img {
    margin-top: -3px;
    margin-left: 3px;
    filter: brightness(0);
}
.blog5 .blog2-box a.learn {
    display: inline-block;
    color: #081120;
    font-size: var(--f-fs-font-fs16);
    line-height: var(--f-fs-font-fs16);
    font-weight: var(--f-fw-medium);
    transition: all 0.4s;
}
.blog5 .blog2-box a.learn span {
    display: inline-block;
    transform: rotate(-45deg);
    margin-left: 3px;
}
.blog5 .blog2-box a.learn:hover {
    transition: all 0.4s;
    color: var(--vtc-bg-main-bg-2);
}
.blog5 .blog2-box:hover {
    transition: all 0.4s;
    transform: translateY(-10px);
}
.blog5 .blog2-box:hover .image img {
    transition: all 0.4s;
    transform: scale(1.1) rotate(4deg);
}

.blog-page .blog2-box {
    background-color: #fff;
    transition: all 0.4s;
    border-radius: 4px;
    border: none;
    margin-bottom: 30px;
}
.blog-page .blog2-box .image {
    overflow: hidden;
    border-radius: 4px 4px 0px 0px;
}
.blog-page .blog2-box .image img {
    width: 100%;
    transition: all 0.4s;
}
.blog-page .blog2-box .heading5 {
    padding: 24px;
    box-shadow: rgba(0, 0, 0, 0.068) 0px 20px 30px;
    transition: all 0.3s ease-in-out;
}
.blog-page .blog2-box .heading5 h4 a {
    line-height: var(--f-fs-font-fs28);
    font-size: var(--f-fs-font-fs20);
    transition: all 0.4s;
}
.blog-page .blog2-box .heading5 h4 a:hover {
    transition: all 0.4s;
    color: var(--vtc-bg-main-bg-1);
}
.blog-page .blog2-box .heading5 .tags {
    margin-bottom: 12px;
}
.blog-page .blog2-box .heading5 .tags a {
    display: inline-block;
    color: #081120;
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-medium);
    margin-right: 20px;
}
.blog-page .blog2-box .heading5 .tags a img {
    margin-top: -3px;
    margin-left: 3px;
    filter: brightness(0);
}
.blog-page .blog2-box a.learn {
    display: inline-block;
    color: #081120;
    font-size: var(--f-fs-font-fs16);
    line-height: var(--f-fs-font-fs16);
    font-weight: var(--f-fw-bold);
    transition: all 0.4s;
}
.blog-page .blog2-box a.learn span {
    display: inline-block;
    transform: rotate(-45deg);
    margin-left: 3px;
}
.blog-page .blog2-box a.learn:hover {
    transition: all 0.4s;
    color: var(--vtc-bg-main-bg-1);
}
.blog-page .blog2-box:hover {
    transition: all 0.4s;
    transform: translateY(-10px);
}
.blog-page .blog2-box:hover .image img {
    transition: all 0.4s;
    transform: scale(1.1) rotate(4deg);
}

.details-sidebar {
    background-color: var(--vtc-bg-common-bg2);
    border-radius: 4px;
    padding: 32px 24px;
}
.details-sidebar .details-box3 h3 {
    color: var(--vtc-text-heading-text-1);
    font-size: var(--f-fs-font-fs24);
    line-height: var(--f-fs-font-fs24);
    font-weight: var(--f-fw-bold);
    transition: all 0.4s;
    padding-bottom: 16px;
}
.details-sidebar .details-box3 .service-list li a {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: var(--vtc-text-heading-text-1);
    font-size: var(--f-fs-font-fs20);
    line-height: var(--f-fs-font-fs20);
    font-weight: var(--f-fw-bold);
    transition: all 0.4s;
    background-color: var(--vtc-bg-bg-white);
    padding: 20px 24px;
    margin-bottom: 20px;
    border-radius: 4px;
}
.details-sidebar .details-box3 .service-list li a:hover {
    background-color: var(--vtc-bg-main-bg-1);
    transition: all 0.4s;
    color: var(--vtc-bg-bg-white);
}
.details-sidebar .details-box-call {
    background-color: var(--vtc-bg-main-bg-1);
    padding: 32px 24px;
    border-radius: 4px;
    margin-top: 40px;
}
.details-sidebar .details-box-call h3 {
    color: var(--vtc-bg-bg-white);
    font-size: var(--f-fs-font-fs24);
    line-height: var(--f-fs-font-fs30);
    font-weight: var(--f-fw-bold);
    transition: all 0.4s;
    padding-bottom: 16px;
}
.details-sidebar .details-box-call .call-btn {
    display: inline-block;
    background-color: var(--vtc-bg-bg-white);
    border-radius: 4px;
    padding: 19px 16px 16px 16px;
    width: 100%;
    text-align: center;
    color: var(--vtc-bg-main-bg-1);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs18);
    font-weight: var(--f-fw-bold);
    transition: all 0.4s;
}
.details-sidebar .details-box-call .call-btn img {
    margin-top: -3px;
    margin-right: 3px;
}
.details-sidebar .details-box-call .call-btn:hover {
    transition: all 0.4s;
    transform: translateY(-5px);
}
.details-sidebar .details-box2 {
    margin-top: 40px;
    padding: 32px 24px;
    border-radius: 4px;
    background-color: var(--vtc-bg-bg-white);
}
.details-sidebar .details-box2 .service-list li a {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: var(--vtc-text-heading-text-1);
    font-size: var(--f-fs-font-fs20);
    line-height: var(--f-fs-font-fs20);
    font-weight: var(--f-fw-bold);
    transition: all 0.4s;
    background-color: var(--vtc-bg-common-bg2);
    padding: 20px 24px;
    margin-bottom: 20px;
    border-radius: 4px;
}
.details-sidebar .details-box2 .service-list li a:hover {
    background-color: var(--vtc-bg-main-bg-1);
    transition: all 0.4s;
    color: var(--vtc-bg-bg-white);
}
.details-sidebar .details-box2 h3 {
    color: var(--vtc-text-heading-text-1);
    font-size: var(--f-fs-font-fs24);
    line-height: var(--f-fs-font-fs30);
    font-weight: var(--f-fw-bold);
    transition: all 0.4s;
    padding-bottom: 16px;
}
.details-sidebar .details-box2 p {
    color: var(--vtc-text-pera-text-1);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-medium);
    transition: all 0.4s;
}
.details-sidebar .details-box2 .download-btn1 {
    display: inline-block;
    background-color: var(--vtc-bg-main-bg-1);
    border-radius: 4px;
    padding: 21px 16px 18px 16px;
    width: 100%;
    text-align: center;
    color: var(--vtc-bg-bg-white);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs18);
    font-weight: var(--f-fw-bold);
    transition: all 0.4s;
    margin-top: 16px;
}
.details-sidebar .details-box2 .download-btn1 img {
    margin-top: -4px;
    margin-right: 3px;
    filter: brightness(40);
}
.details-sidebar .details-box2 .download-btn1:hover {
    transition: all 0.4s;
    transform: translateY(-5px);
}
.details-sidebar .details-box2 .download-btn2 {
    display: inline-block;
    background-color: #fff2e6;
    border-radius: 4px;
    padding: 19px 16px 18px 16px;
    width: 100%;
    text-align: center;
    color: var(--vtc-text-heading-text-1);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs18);
    font-weight: var(--f-fw-bold);
    transition: all 0.4s;
    margin-top: 22px;
}
.details-sidebar .details-box2 .download-btn2 img {
    margin-top: -4px;
    margin-right: 3px;
}
.details-sidebar .details-box2 .download-btn2:hover {
    transition: all 0.4s;
    transform: translateY(-5px);
}
.details-sidebar .details-box2 .icons li {
    display: inline-block;
}
.details-sidebar .details-box2 .icons li a {
    display: inline-block;
    height: 40px;
    width: 40px;
    text-align: center;
    line-height: 40px;
    border-radius: 50%;
    color: var(--vtc-text-heading-text-1);
    background-color: #fff2e6;
    margin: 0px 2px;
    transition: all 0.4s;
}
.details-sidebar .details-box2 .icons li a:hover {
    background-color: var(--vtc-bg-main-bg-1);
    color: var(--vtc-bg-bg-white);
    transition: all 0.4s;
    transform: translateY(-3px);
}
.details-sidebar .details-box2 .search-area {
    position: relative;
}
.details-sidebar .details-box2 .search-area input {
    width: 100%;
    padding: 16px;
    border: none;
    border-radius: 4px;
    background-color: var(--vtc-bg-common-bg2);
}
.details-sidebar .details-box2 .search-area input:focus {
    outline: none;
}
.details-sidebar .details-box2 .search-area input::-moz-placeholder {
    color: var(--vtc-text-heading-text-1);
    font-size: 18px;
    font-weight: 500;
}
.details-sidebar .details-box2 .search-area input::placeholder {
    color: var(--vtc-text-heading-text-1);
    font-size: 18px;
    font-weight: 500;
}
.details-sidebar .details-box2 .search-area button {
    position: absolute;
    top: 16px;
    right: 16px;
    border: none;
    background: none;
    font-size: 20px;
}
.details-sidebar .details-box2 .tags li {
    display: inline-block;
}
.details-sidebar .details-box2 .tags li a {
    display: inline-block;
    color: var(--vtc-text-heading-text-1);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs18);
    font-weight: var(--f-fw-medium);
    background-color: var(--vtc-bg-common-bg2);
    border-radius: 4px;
    padding: 8px 12px;
    margin: 8px 5px;
    transition: all 0.4s;
}
.details-sidebar .details-box2 .tags li a:hover {
    background-color: var(--vtc-bg-main-bg-1);
    transition: all 0.4s;
    color: var(--vtc-bg-bg-white);
}
.details-sidebar .details-box2 .form input,
.details-sidebar .details-box2 .form textarea {
    background-color: var(--vtc-bg-common-bg2);
    border: none;
    border-radius: 4px;
    padding: 16px;
    width: 100%;
    margin-bottom: 16px;
}
.details-sidebar .details-box2 .form input::-moz-placeholder,
.details-sidebar .details-box2 .form textarea::-moz-placeholder {
    color: #696b6d;
    font-size: 18px;
    font-weight: 500;
}
.details-sidebar .details-box2 .form input::placeholder,
.details-sidebar .details-box2 .form textarea::placeholder {
    color: #696b6d;
    font-size: 18px;
    font-weight: 500;
}
.details-sidebar .details-box2 .form input:focus,
.details-sidebar .details-box2 .form textarea:focus {
    outline: none;
}
.details-sidebar .details-box2.search {
    margin-top: 0;
}

.recent-posts .single-recent-post {
    display: flex;
    align-items: center;
    border-bottom: 1px solid #f3f3f4;
    padding: 12px 0;
}
.recent-posts .single-recent-post .image {
    width: 80px;
}
.recent-posts .single-recent-post .heading {
    padding-left: 20px;
}
.recent-posts .single-recent-post .heading a.date {
    color: #696b6d;
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs18);
    font-weight: var(--f-fw-medium);
    display: inline-block;
}
.recent-posts .single-recent-post .heading a.date img {
    margin-top: -4px;
    margin-right: 4px;
}
.recent-posts .single-recent-post .heading h5 a {
    display: inline-block;
    color: var(--vtc-text-heading-text-1);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs26);
    font-weight: var(--f-fw-bold);
    display: inline-block;
    padding-top: 6px;
    transition: all 0.4s;
}
.recent-posts .single-recent-post .heading h5 a:hover {
    transition: all 0.4s;
    color: var(--vtc-bg-main-bg-1);
}
.recent-posts .single-recent-post:nth-last-child(1) {
    border-bottom: none;
    padding: 12px 0px 0px 0px;
}

.project-details-list ul li {
    color: var(--vtc-text-pera-text-1);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-medium);
    transition: all 0.4s;
    display: flex;
    align-items: center;
    padding: 6px 0px;
}
.project-details-list ul li span {
    display: inline-block;
    height: 17px;
    width: 17px;
    border-radius: 50%;
    text-align: center;
    line-height: 17px;
    background-color: var(--vtc-bg-main-bg-1);
    color: #fff;
    font-size: 10px;
    margin-right: 7px;
}

.service-details-area .image img {
    width: 100%;
}

.left-padding {
    padding-left: 30px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .left-padding {
        padding-left: 0;
    }
}
@media (max-width: 767px) {
    .left-padding {
        padding-left: 0;
    }
}

.right-padding {
    padding-right: 30px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .right-padding {
        padding-right: 0;
    }
}
@media (max-width: 767px) {
    .right-padding {
        padding-right: 0;
    }
}

.porgress-line-all .progress-line {
    position: relative;
    margin-top: 28px;
}
.porgress-line-all .progress-line h6 {
    position: absolute;
    right: 0;
    top: 0;
    font-size: 16px;
    line-height: 16px;
    font-weight: 500;
    color: #081120;
}
.porgress-line-all .progress-line .percentCount {
    margin-bottom: 10px;
    font-size: 16px;
    line-height: 16px;
    font-weight: 500;
    color: #081120;
}

.blog-details-box .image img {
    width: 100%;
}
.blog-details-box .users {
    padding-top: 28px;
    padding-bottom: 16px;
}
.blog-details-box .users li {
    display: inline-block;
    margin-right: 42px;
    position: relative;
}
.blog-details-box .users li img {
    margin-top: -5px;
    margin-right: 3px;
}
.blog-details-box .users li img.author {
    margin-right: 4px;
}
.blog-details-box .users li a {
    display: inline-block;
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs18);
    font-weight: var(--f-fw-medium);
    color: #696b6d;
}
.blog-details-box .users li::after {
    content: "";
    position: absolute;
    top: 4px;
    right: -24px;
    height: 20px;
    width: 2px;
    background-color: rgba(143, 147, 150, 0.7607843137);
}
.blog-details-box .users li:nth-last-child(1)::after {
    display: none;
}
.blog-details-box .project-details-list ul li {
    color: var(--vtc-text-pera-text-1);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-medium);
    transition: all 0.4s;
    display: flex;
    align-items: center;
    padding: 6px 0px;
}
.blog-details-box .project-details-list ul li span {
    display: inline-block;
    height: 17px;
    width: 17px;
    border-radius: 50%;
    text-align: center;
    line-height: 17px;
    background-color: var(--vtc-bg-main-bg-1);
    color: #fff;
    font-size: 10px;
    margin-right: 7px;
}

.blog-details-border {
    background-color: rgba(143, 147, 150, 0.2470588235);
    width: 100%;
    height: 2px;
    margin: 40px 0px;
}

.after-box-details {
    background-color: var(--vtc-bg-common-bg2);
    padding: 30px 32px;
    border-radius: 4px;
    margin: 24px 0px;
    position: relative;
}
.after-box-details::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 10px;
    border-radius: 4px;
    background-color: var(--vtc-bg-main-bg-1);
}

.video-details-area {
    position: relative;
    /* video button  */
}
.video-details-area .video-play-button {
    position: absolute;
    z-index: 10;
    margin: 0px 30px;
    box-sizing: content-box;
    display: block;
    width: 32px;
    height: 44px;
    /* background: #fa183d; */
    border-radius: 50%;
    padding: 18px 20px 18px 28px;
    cursor: pointer;
    top: 50%;
    margin-top: -40px;
    left: 50%;
    margin-left: -40px;
}
.video-details-area .video-play-button:before {
    content: "";
    position: absolute;
    z-index: 0;
    left: 50%;
    top: 50%;
    transform: translateX(-50%) translateY(-50%);
    display: block;
    width: 60px;
    height: 60px;
    background: #ff7a01;
    border-radius: 50%;
    animation: pulse-border 1500ms ease-out infinite;
}
.video-details-area .video-play-button:after {
    content: "";
    position: absolute;
    z-index: 1;
    left: 50%;
    top: 50%;
    transform: translateX(-50%) translateY(-50%);
    display: block;
    width: 60px;
    height: 60px;
    background: #ff7a01;
    border-radius: 50%;
    transition: all 200ms;
}
.video-details-area .video-play-button:hover:after {
    background-color: #cd6200;
}
.video-details-area .video-play-button img {
    position: relative;
    z-index: 3;
    max-width: 100%;
    width: auto;
    height: auto;
}
.video-details-area .video-play-button span {
    display: block;
    position: relative;
    z-index: 3;
    margin-top: 12px;
    margin-left: 8px;
    width: 0;
    height: 0;
    border-left: 12px solid #fff;
    border-top: 10px solid transparent;
    border-bottom: 10px solid transparent;
}
@keyframes pulse-border {
    0% {
        transform: translateX(-50%) translateY(-50%) translateZ(0) scale(1);
        opacity: 1;
    }
    100% {
        transform: translateX(-50%) translateY(-50%) translateZ(0) scale(1.5);
        opacity: 0;
    }
}
.video-details-area .image img {
    width: 100%;
}
.video-details-area .video-area {
    position: relative;
}
.video-details-area .video-area .video-buttton {
    position: absolute;
    top: 50%;
    left: 50%;
    margin-top: -40px;
    margin-left: -40px;
}

.tags-icons {
    padding-top: 60px;
}
.tags-icons li.text {
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs18);
    font-weight: var(--f-fw-bold);
    color: var(--vtc-text-heading-text-1);
    margin-right: 10px;
}
.tags-icons .details-tags ul li {
    display: inline-block;
}
.tags-icons .details-tags ul li a {
    display: inline-block;
    color: var(--vtc-text-heading-text-1);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs18);
    font-weight: var(--f-fw-medium);
    background-color: var(--vtc-bg-common-bg2);
    border-radius: 4px;
    padding: 8px 12px;
    margin: 8px 5px;
    transition: all 0.4s;
}
.tags-icons .details-tags ul li a:hover {
    background-color: var(--vtc-bg-main-bg-1);
    transition: all 0.4s;
    color: var(--vtc-bg-bg-white);
}
.tags-icons .details-icons {
    text-align: end;
}
.tags-icons .details-icons ul li {
    display: inline-block;
}
.tags-icons .details-icons ul li a {
    display: inline-block;
    background-color: #fff2e6;
    height: 32px;
    width: 32px;
    line-height: 32px;
    text-align: center;
    color: var(--vtc-bg-main-bg-1);
    border-radius: 50%;
    margin: 0px 3px;
    transition: all 0.4s;
}
.tags-icons .details-icons ul li a:hover {
    background-color: var(--vtc-bg-main-bg-1);
    transition: all 0.4s;
    color: var(--vtc-bg-bg-white);
}

.blog-details-commnet-boxs .commnet-box {
    padding: 24px 32px;
    background-color: var(--vtc-bg-common-bg2);
    border-radius: 4px;
    margin-top: 30px;
}
.blog-details-commnet-boxs .commnet-box .top-area {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.blog-details-commnet-boxs .commnet-box .top-area .author-area {
    display: flex;
    align-items: center;
}
.blog-details-commnet-boxs .commnet-box .top-area .author-area .heading {
    padding-left: 12px;
}
.blog-details-commnet-boxs .commnet-box .top-area .author-area .heading h5 a {
    display: inline-block;
    font-size: var(--f-fs-font-fs20);
    line-height: var(--f-fs-font-fs20);
    color: var(--vtc-text-heading-text-1);
    font-weight: var(--f-fw-bold);
    transition: all 0.4s;
}
.blog-details-commnet-boxs .commnet-box .top-area .author-area .heading p {
    color: var(--vtc-text-pera-text-1);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-medium);
    transition: all 0.4s;
    padding-top: 6px;
}
.blog-details-commnet-boxs .commnet-box .top-area .reply-btn {
    font-size: var(--f-fs-font-fs20);
    line-height: var(--f-fs-font-fs20);
    color: var(--vtc-text-heading-text-1);
    font-weight: var(--f-fw-bold);
    transition: all 0.4s;
}
.blog-details-commnet-boxs .commnet-box .top-area .reply-btn:hover {
    color: var(--vtc-bg-main-bg-1);
    transition: all 0.4s;
}
.blog-details-commnet-boxs .commnet-box.commnet-box2 {
    margin-left: 60px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .blog-details-commnet-boxs .commnet-box.commnet-box2 {
        margin-left: 0;
    }
}
@media (max-width: 767px) {
    .blog-details-commnet-boxs .commnet-box.commnet-box2 {
        margin-left: 0;
    }
}

.details-contact-form {
    background-color: var(--vtc-bg-common-bg2);
    padding: 32px;
    border-radius: 4px;
    margin-top: 30px;
}
.details-contact-form .button {
    text-align: end;
}
.details-contact-form .single-input {
    margin-top: 20px;
}
.details-contact-form .single-input input,
.details-contact-form .single-input textarea {
    width: 100%;
    border: none;
    padding: 16px;
    font-weight: var(--f-fw-medium);
    border-radius: 4px;
}
.details-contact-form .single-input input::-moz-placeholder,
.details-contact-form .single-input textarea::-moz-placeholder {
    color: var(--vtc-text-pera-text-1);
    font-weight: var(--f-fw-medium);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs18);
}
.details-contact-form .single-input input::placeholder,
.details-contact-form .single-input textarea::placeholder {
    color: var(--vtc-text-pera-text-1);
    font-weight: var(--f-fw-medium);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs18);
}
.details-contact-form .single-input input:focus,
.details-contact-form .single-input textarea:focus {
    outline: none;
}

.blog7 .blog-box {
    margin-bottom: 30px;
    border-radius: 8px;
    padding: 24px;
    display: flex;
    align-items: center;
    background-color: #fff;
    transition: all 0.4s;
}
@media (max-width: 767px) {
    .blog7 .blog-box {
        display: block;
        margin-bottom: 0;
        margin-top: 30px;
    }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .blog7 .blog-box {
        margin-top: 30px;
        margin-bottom: 0;
    }
}
.blog7 .blog-box:hover {
    transform: translateY(-10px);
    transition: all 0.4s;
}
.blog7 .blog-box .image {
    border-radius: 8px;
    width: 240px;
}
.blog7 .blog-box .image img {
    transition: all 0.4s;
}
.blog7 .blog-box .heading {
    padding-left: 20px;
}
@media (max-width: 767px) {
    .blog7 .blog-box .heading {
        padding-left: 0px;
        padding-top: 20px;
    }
}
.blog7 .blog-box .heading a {
    display: inline-block;
    color: #555;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px; /* 100% */
    text-transform: uppercase;
}
.blog7 .blog-box .heading a img {
    transform: translateY(-3px);
    margin-right: 3px;
}
.blog7 .blog-box .heading h4 a {
    display: inline-block;
    color: #081120;
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: 28px; /* 140% */
    padding: 16px 0px;
    transition: all 0.4s;
}
.blog7 .blog-box .heading h4 a:hover {
    color: #5957e5;
    transition: all 0.4s;
}
.blog7 .blog-box .heading .learn {
    display: inline-block;
    color: #081120;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: 16px; /* 100% */
    text-transform: uppercase;
    transition: all 0.4s;
}
.blog7 .blog-box .heading .learn:hover {
    color: #5957e5;
    transition: all 0.4s;
}

.blog9 .blog-box {
    margin-top: 30px;
}
.blog9 .blog-box .image {
    border-radius: 8px 8px 0px 0px;
    overflow: hidden;
}
.blog9 .blog-box .image img {
    transition: all 0.4s;
    width: 100%;
}
.blog9 .blog-box .heading {
    padding: 24px;
    background-color: #fff;
}
.blog9 .blog-box .heading .date {
    color: #555;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px; /* 100% */
    text-transform: uppercase;
    display: inline-block;
}
.blog9 .blog-box .heading .date img {
    transform: translateY(-3px);
    margin-right: 3px;
}
.blog9 .blog-box .heading h4 a {
    display: inline-block;
    color: #081120;
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: 28px; /* 140% */
    transition: all 0.4s;
    padding: 14px 0px 20px 0px;
}
.blog9 .blog-box .heading h4 a:hover {
    transition: all 0.4s;
    color: #2a9134;
}
.blog9 .blog-box .heading .learn {
    display: inline-block;
    color: #081120;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: 16px; /* 100% */
    text-transform: uppercase;
    transition: all 0.4s;
}
.blog9 .blog-box .heading .learn:hover {
    transition: all 0.4s;
    color: #2a9134;
}
.blog9 .blog-box:hover .image img {
    transition: all 0.4s;
    transform: rotate(2deg) scale(1.1);
}

.blog10 .blog-box {
    margin-bottom: 30px;
    border-radius: 8px;
    padding: 24px;
    display: flex;
    align-items: center;
    background-color: #fff;
    transition: all 0.4s;
}
@media (max-width: 767px) {
    .blog10 .blog-box {
        display: block;
        margin-bottom: 0;
        margin-top: 30px;
    }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .blog10 .blog-box {
        margin-top: 30px;
        margin-bottom: 0;
    }
}
.blog10 .blog-box:hover {
    transform: translateY(-10px);
    transition: all 0.4s;
}
.blog10 .blog-box .image {
    border-radius: 8px;
    width: 240px;
}
.blog10 .blog-box .image img {
    transition: all 0.4s;
}
.blog10 .blog-box .heading {
    padding-left: 20px;
}
@media (max-width: 767px) {
    .blog10 .blog-box .heading {
        padding-left: 0px;
        padding-top: 20px;
    }
}
.blog10 .blog-box .heading a {
    display: inline-block;
    color: #555;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px; /* 100% */
    text-transform: capitalize;
}
.blog10 .blog-box .heading a img {
    transform: translateY(-3px);
    margin-right: 3px;
}
.blog10 .blog-box .heading h4 a {
    display: inline-block;
    color: #081120;
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: 28px; /* 140% */
    padding: 16px 0px;
    transition: all 0.4s;
}
.blog10 .blog-box .heading h4 a:hover {
    color: #fa6444;
    transition: all 0.4s;
}
.blog10 .blog-box .heading .learn {
    display: inline-block;
    color: #081120;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: 16px; /* 100% */
    text-transform: uppercase;
    transition: all 0.4s;
}
.blog10 .blog-box .heading .learn:hover {
    color: #fa6444;
    transition: all 0.4s;
}

/*
::::::::::::::::::::::::::
 BLOG AREA CSS
::::::::::::::::::::::::::
*/
/*
::::::::::::::::::::::::::
 FOOTER AREA CSS
::::::::::::::::::::::::::
*/
.footer1 {
    padding-top: 80px;
    background-color: var(--vtc-text-heading-text-1);
}
.footer1 .single-footer-items h3 {
    font-size: var(--f-fs-font-fs20);
    line-height: var(--f-fs-font-fs20);
    font-weight: var(--f-fw-bold);
    color: var(--vtc-text-text-white-text-1);
    padding-bottom: 24px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .footer1 .single-footer-items h3 {
        margin-top: 16px;
        padding-top: 24px;
    }
}
@media (max-width: 767px) {
    .footer1 .single-footer-items h3 {
        margin-top: 16px;
        padding-top: 24px;
    }
}
.footer1 .single-footer-items ul.menu-list li a {
    color: var(--vtc-text-pera-text-2);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-medium);
    display: inline-block;
    padding: 6px 0px;
    transition: all 0.4s;
    position: relative;
}
.footer1 .single-footer-items ul.menu-list li a::after {
    content: "";
    position: absolute;
    top: 10px;
    left: 0;
    height: 20px;
    width: 0px;
    background-color: var(--vtc-bg-main-bg-1);
    opacity: 0;
    transition: all 0.4s;
}
.footer1 .single-footer-items ul.menu-list li a:hover {
    padding-left: 12px;
    transition: all 0.4s;
    color: var(--vtc-bg-main-bg-1);
}
.footer1 .single-footer-items ul.menu-list li a:hover::after {
    width: 3px;
    opacity: 1;
}
.footer1 .single-footer-items .social-icon {
    margin-top: 24px;
}
.footer1 .single-footer-items .social-icon li {
    display: inline-block;
}
.footer1 .single-footer-items .social-icon li a {
    display: inline-block;
    margin: 0px 4px;
    height: 36px;
    width: 36px;
    line-height: 36px;
    border-radius: 36px;
    background-color: rgba(255, 255, 255, 0.2);
    text-align: center;
    color: var(--vtc-text-text-white-text-1);
    transition: all 0.4s;
}
.footer1 .single-footer-items .social-icon li a:hover {
    background-color: var(--vtc-bg-main-bg-1);
}
.footer1 .single-footer-items .contact-box {
    display: flex;
    align-items: start;
    margin-bottom: 16px;
}
.footer1 .single-footer-items .contact-box .icon {
    height: 30px;
    width: 30px;
    text-align: center;
    line-height: 30px;
    background-color: rgba(255, 255, 255, 0.199);
    border-radius: 50%;
    transition: all 0.4s;
}
.footer1 .single-footer-items .contact-box .pera a {
    display: inline-block;
    color: var(--vtc-text-pera-text-2);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-medium);
    margin-left: 16px;
    transition: all 0.4s;
}
.footer1 .single-footer-items .contact-box:hover .pera a {
    color: var(--vtc-bg-main-bg-1);
    transition: all 0.4s;
}
.footer1 .single-footer-items .contact-box:hover .icon {
    background-color: var(--vtc-bg-main-bg-1);
    transition: all 0.4s;
}
.footer1 .coppyright p {
    color: var(--vtc-text-pera-text-2);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-medium);
    text-align: center;
    padding: 20px 0;
    border-top: 1px solid rgba(255, 255, 255, 0.267);
}

.footer-logo-area {
    padding-right: 60px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .footer-logo-area {
        padding-right: 0;
    }
}
@media (max-width: 767px) {
    .footer-logo-area {
        padding-right: 0;
    }
}

.pl-5 {
    padding-left: 24px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .pl-5 {
        padding-left: 0;
    }
}
@media (max-width: 767px) {
    .pl-5 {
        padding-left: 0;
    }
}

.footer2 {
    padding-top: 295px;
    background-color: #001431;
}
.footer2 .footer-logo {
    width: 160px;
}
.footer2 .single-footer-items h3 {
    font-size: var(--f-fs-font-fs20);
    line-height: var(--f-fs-font-fs20);
    font-weight: var(--f-fw-bold);
    color: var(--vtc-text-text-white-text-1);
    padding-bottom: 24px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .footer2 .single-footer-items h3 {
        margin-top: 16px;
        padding-top: 24px;
    }
}
@media (max-width: 767px) {
    .footer2 .single-footer-items h3 {
        margin-top: 16px;
        padding-top: 24px;
    }
}
.footer2 .single-footer-items ul.menu-list li a {
    color: var(--vtc-text-pera-text-2);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-medium);
    display: inline-block;
    padding: 6px 0px;
    transition: all 0.4s;
    position: relative;
}
.footer2 .single-footer-items ul.menu-list li a::after {
    content: "";
    position: absolute;
    top: 10px;
    left: 0;
    height: 20px;
    width: 0px;
    background-color: var(--vtc-bg-main-bg-2);
    opacity: 0;
    transition: all 0.4s;
}
.footer2 .single-footer-items ul.menu-list li a:hover {
    padding-left: 12px;
    transition: all 0.4s;
    color: var(--vtc-bg-main-bg-2);
}
.footer2 .single-footer-items ul.menu-list li a:hover::after {
    width: 3px;
    opacity: 1;
}
.footer2 .single-footer-items .social-icon {
    margin-top: 24px;
}
.footer2 .single-footer-items .social-icon li {
    display: inline-block;
}
.footer2 .single-footer-items .social-icon li a {
    display: inline-block;
    margin: 0px 4px;
    height: 36px;
    width: 36px;
    line-height: 36px;
    border-radius: 36px;
    background-color: rgba(255, 255, 255, 0.2);
    text-align: center;
    color: var(--vtc-text-text-white-text-1);
    transition: all 0.4s;
}
.footer2 .single-footer-items .social-icon li a:hover {
    background-color: var(--vtc-bg-main-bg-2);
    transition: all 0.4s;
}
.footer2 .single-footer-items .contact-box {
    display: flex;
    align-items: start;
    margin-bottom: 16px;
}
.footer2 .single-footer-items .contact-box .icon {
    height: 30px;
    width: 30px;
    text-align: center;
    line-height: 30px;
    background-color: rgba(255, 255, 255, 0.199);
    border-radius: 50%;
    transition: all 0.4s;
}
.footer2 .single-footer-items .contact-box .pera a {
    display: inline-block;
    color: var(--vtc-text-pera-text-2);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-medium);
    margin-left: 16px;
    transition: all 0.4s;
}
.footer2 .single-footer-items .contact-box:hover .pera a {
    color: var(--vtc-bg-main-bg-2);
    transition: all 0.4s;
}
.footer2 .single-footer-items .contact-box:hover .icon {
    background-color: var(--vtc-bg-main-bg-2);
    transition: all 0.4s;
}
.footer2 .coppyright p {
    color: var(--vtc-text-pera-text-2);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-medium);
    text-align: center;
    padding: 20px 0;
    border-top: 1px solid rgba(255, 255, 255, 0.267);
}

.footer-logo-area {
    padding-right: 60px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .footer-logo-area {
        padding-right: 0;
    }
}
@media (max-width: 767px) {
    .footer-logo-area {
        padding-right: 0;
    }
}

.pl-5 {
    padding-left: 24px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .pl-5 {
        padding-left: 0;
    }
}
@media (max-width: 767px) {
    .pl-5 {
        padding-left: 0;
    }
}

.footer3 {
    padding-top: 295px;
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
}
.footer3 .footer-logo {
    width: 160px;
}
.footer3 .single-footer-items h3 {
    font-size: var(--f-fs-font-fs20);
    line-height: var(--f-fs-font-fs20);
    font-weight: var(--f-fw-bold);
    color: var(--vtc-text-text-white-text-1);
    padding-bottom: 24px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .footer3 .single-footer-items h3 {
        margin-top: 16px;
        padding-top: 24px;
    }
}
@media (max-width: 767px) {
    .footer3 .single-footer-items h3 {
        margin-top: 16px;
        padding-top: 24px;
    }
}
.footer3 .single-footer-items ul.menu-list li a {
    color: var(--vtc-text-pera-text-2);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-medium);
    display: inline-block;
    padding: 6px 0px;
    transition: all 0.4s;
    position: relative;
}
.footer3 .single-footer-items ul.menu-list li a::after {
    content: "";
    position: absolute;
    top: 10px;
    left: 0;
    height: 20px;
    width: 0px;
    background-color: var(--vtc-bg-main-bg-3);
    opacity: 0;
    transition: all 0.4s;
}
.footer3 .single-footer-items ul.menu-list li a:hover {
    padding-left: 12px;
    transition: all 0.4s;
    color: var(--vtc-bg-main-bg-3);
}
.footer3 .single-footer-items ul.menu-list li a:hover::after {
    width: 3px;
    opacity: 1;
}
.footer3 .single-footer-items .social-icon {
    margin-top: 24px;
}
.footer3 .single-footer-items .social-icon li {
    display: inline-block;
}
.footer3 .single-footer-items .social-icon li a {
    display: inline-block;
    margin: 0px 4px;
    height: 36px;
    width: 36px;
    line-height: 36px;
    border-radius: 36px;
    background-color: rgba(255, 255, 255, 0.2);
    text-align: center;
    color: var(--vtc-text-text-white-text-1);
    transition: all 0.4s;
}
.footer3 .single-footer-items .social-icon li a:hover {
    background-color: var(--vtc-bg-main-bg-3);
    transition: all 0.4s;
}
.footer3 .single-footer-items .contact-box {
    display: flex;
    align-items: start;
    margin-bottom: 16px;
}
.footer3 .single-footer-items .contact-box .icon {
    height: 30px;
    width: 30px;
    text-align: center;
    line-height: 30px;
    background-color: rgba(255, 255, 255, 0.199);
    border-radius: 50%;
    transition: all 0.4s;
}
.footer3 .single-footer-items .contact-box .pera a {
    display: inline-block;
    color: var(--vtc-text-pera-text-2);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-medium);
    margin-left: 16px;
    transition: all 0.4s;
}
.footer3 .single-footer-items .contact-box:hover .pera a {
    color: var(--vtc-bg-main-bg-3);
    transition: all 0.4s;
}
.footer3 .single-footer-items .contact-box:hover .icon {
    background-color: var(--vtc-bg-main-bg-3);
    transition: all 0.4s;
}
.footer3 .coppyright p {
    color: var(--vtc-text-pera-text-2);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-medium);
    text-align: center;
    padding: 20px 0;
    border-top: 1px solid rgba(255, 255, 255, 0.267);
}

.footer-logo-area {
    padding-right: 60px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .footer-logo-area {
        padding-right: 0;
    }
}
@media (max-width: 767px) {
    .footer-logo-area {
        padding-right: 0;
    }
}

.pl-5 {
    padding-left: 24px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .pl-5 {
        padding-left: 0;
    }
}
@media (max-width: 767px) {
    .pl-5 {
        padding-left: 0;
    }
}

.footer4 {
    padding-top: 295px;
    background-color: var(--vtc-bg-main-bg-5);
}
.footer4 .footer-logo {
    width: 160px;
}
.footer4 .single-footer-items h3 {
    font-size: var(--f-fs-font-fs20);
    line-height: var(--f-fs-font-fs20);
    font-weight: var(--f-fw-bold);
    color: var(--vtc-text-text-white-text-1);
    padding-bottom: 24px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .footer4 .single-footer-items h3 {
        margin-top: 16px;
        padding-top: 24px;
    }
}
@media (max-width: 767px) {
    .footer4 .single-footer-items h3 {
        margin-top: 16px;
        padding-top: 24px;
    }
}
.footer4 .single-footer-items ul.menu-list li a {
    color: var(--vtc-text-pera-text-2);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-medium);
    display: inline-block;
    padding: 6px 0px;
    transition: all 0.4s;
    position: relative;
}
.footer4 .single-footer-items ul.menu-list li a::after {
    content: "";
    position: absolute;
    top: 10px;
    left: 0;
    height: 20px;
    width: 0px;
    background-color: var(--vtc-bg-main-bg-6);
    opacity: 0;
    transition: all 0.4s;
}
.footer4 .single-footer-items ul.menu-list li a:hover {
    padding-left: 12px;
    transition: all 0.4s;
    color: var(--vtc-bg-main-bg-6);
}
.footer4 .single-footer-items ul.menu-list li a:hover::after {
    width: 3px;
    opacity: 1;
}
.footer4 .single-footer-items .social-icon {
    margin-top: 24px;
}
.footer4 .single-footer-items .social-icon li {
    display: inline-block;
}
.footer4 .single-footer-items .social-icon li a {
    display: inline-block;
    margin: 0px 4px;
    height: 36px;
    width: 36px;
    line-height: 36px;
    border-radius: 36px;
    background-color: rgba(255, 255, 255, 0.2);
    text-align: center;
    color: var(--vtc-text-text-white-text-1);
    transition: all 0.4s;
}
.footer4 .single-footer-items .social-icon li a:hover {
    background-color: var(--vtc-bg-main-bg-6);
    transition: all 0.4s;
}
.footer4 .single-footer-items .contact-box {
    display: flex;
    align-items: start;
    margin-bottom: 16px;
}
.footer4 .single-footer-items .contact-box .icon {
    height: 30px;
    width: 30px;
    text-align: center;
    line-height: 30px;
    background-color: rgba(255, 255, 255, 0.199);
    border-radius: 50%;
    transition: all 0.4s;
}
.footer4 .single-footer-items .contact-box .pera a {
    display: inline-block;
    color: var(--vtc-text-pera-text-2);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-medium);
    margin-left: 16px;
    transition: all 0.4s;
}
.footer4 .single-footer-items .contact-box:hover .pera a {
    color: var(--vtc-bg-main-bg-6);
    transition: all 0.4s;
}
.footer4 .single-footer-items .contact-box:hover .icon {
    background-color: var(--vtc-bg-main-bg-6);
    transition: all 0.4s;
}
.footer4 .coppyright p {
    color: var(--vtc-text-pera-text-2);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-medium);
    text-align: center;
    padding: 20px 0;
    border-top: 1px solid rgba(255, 255, 255, 0.267);
}

.footer-logo-area {
    padding-right: 60px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .footer-logo-area {
        padding-right: 0;
    }
}
@media (max-width: 767px) {
    .footer-logo-area {
        padding-right: 0;
    }
}

.pl-5 {
    padding-left: 24px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .pl-5 {
        padding-left: 0;
    }
}
@media (max-width: 767px) {
    .pl-5 {
        padding-left: 0;
    }
}

.footer5 {
    padding-top: 80px;
}
.footer5 .footer-logo {
    width: 160px;
}
.footer5 .single-footer-items h3 {
    font-size: var(--f-fs-font-fs20);
    line-height: var(--f-fs-font-fs20);
    font-weight: var(--f-fw-bold);
    color: var(--vtc-text-text-white-text-1);
    padding-bottom: 24px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .footer5 .single-footer-items h3 {
        margin-top: 16px;
        padding-top: 24px;
    }
}
@media (max-width: 767px) {
    .footer5 .single-footer-items h3 {
        margin-top: 16px;
        padding-top: 24px;
    }
}
.footer5 .single-footer-items ul.menu-list li a {
    color: var(--vtc-text-pera-text-2);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-medium);
    display: inline-block;
    padding: 6px 0px;
    transition: all 0.4s;
    position: relative;
}
.footer5 .single-footer-items ul.menu-list li a::after {
    content: "";
    position: absolute;
    top: 10px;
    left: 0;
    height: 20px;
    width: 0px;
    background-color: var(--vtc-bg-main-bg-2);
    opacity: 0;
    transition: all 0.4s;
}
.footer5 .single-footer-items ul.menu-list li a:hover {
    padding-left: 12px;
    transition: all 0.4s;
    color: var(--vtc-bg-main-bg-2);
}
.footer5 .single-footer-items ul.menu-list li a:hover::after {
    width: 3px;
    opacity: 1;
}
.footer5 .single-footer-items .social-icon {
    margin-top: 24px;
}
.footer5 .single-footer-items .social-icon li {
    display: inline-block;
}
.footer5 .single-footer-items .social-icon li a {
    display: inline-block;
    margin: 0px 4px;
    height: 36px;
    width: 36px;
    line-height: 36px;
    border-radius: 36px;
    background-color: rgba(255, 255, 255, 0.2);
    text-align: center;
    color: var(--vtc-text-text-white-text-1);
    transition: all 0.4s;
}
.footer5 .single-footer-items .social-icon li a:hover {
    background-color: var(--vtc-bg-main-bg-2);
}
.footer5 .single-footer-items .contact-box {
    display: flex;
    align-items: start;
    margin-bottom: 16px;
}
.footer5 .single-footer-items .contact-box .icon {
    height: 30px;
    width: 30px;
    text-align: center;
    line-height: 30px;
    background-color: rgba(255, 255, 255, 0.199);
    border-radius: 50%;
    transition: all 0.4s;
}
.footer5 .single-footer-items .contact-box .pera a {
    display: inline-block;
    color: var(--vtc-text-pera-text-2);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-medium);
    margin-left: 16px;
    transition: all 0.4s;
}
.footer5 .single-footer-items .contact-box:hover .pera a {
    color: var(--vtc-bg-main-bg-2);
    transition: all 0.4s;
}
.footer5 .single-footer-items .contact-box:hover .icon {
    background-color: var(--vtc-bg-main-bg-2);
    transition: all 0.4s;
}
.footer5 .coppyright p {
    color: var(--vtc-text-pera-text-2);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-medium);
    text-align: center;
    padding: 20px 0;
    border-top: 1px solid rgba(255, 255, 255, 0.267);
}

.footer6 {
    padding-top: 80px;
}
.footer6 .footer-logo {
    width: 160px;
}
.footer6 .single-footer-items p {
    color: #111;
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 28px; /* 155.556% */
}
.footer6 .single-footer-items h3 {
    font-size: var(--f-fs-font-fs20);
    line-height: var(--f-fs-font-fs20);
    font-weight: var(--f-fw-bold);
    color: #0b0314;
    padding-bottom: 24px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .footer6 .single-footer-items h3 {
        margin-top: 16px;
        padding-top: 24px;
    }
}
@media (max-width: 767px) {
    .footer6 .single-footer-items h3 {
        margin-top: 16px;
        padding-top: 24px;
    }
}
.footer6 .single-footer-items ul.menu-list li a {
    color: #0b0314;
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-medium);
    display: inline-block;
    padding: 6px 0px;
    transition: all 0.4s;
    position: relative;
}
.footer6 .single-footer-items ul.menu-list li a::after {
    content: "";
    position: absolute;
    top: 10px;
    left: 0;
    height: 20px;
    width: 0px;
    background-color: #31572c;
    opacity: 0;
    transition: all 0.4s;
}
.footer6 .single-footer-items ul.menu-list li a:hover {
    padding-left: 12px;
    transition: all 0.4s;
    color: #31572c;
}
.footer6 .single-footer-items ul.menu-list li a:hover::after {
    width: 3px;
    opacity: 1;
}
.footer6 .single-footer-items .social-icon {
    margin-top: 24px;
}
.footer6 .single-footer-items .social-icon li {
    display: inline-block;
}
.footer6 .single-footer-items .social-icon li a {
    display: inline-block;
    margin: 0px 4px;
    height: 36px;
    width: 36px;
    line-height: 36px;
    border-radius: 36px;
    background-color: #f3f5f2;
    text-align: center;
    color: #001431;
    transition: all 0.4s;
}
.footer6 .single-footer-items .social-icon li a:hover {
    background-color: #31572c;
    color: #fff;
}
.footer6 .single-footer-items .contact-box {
    display: flex;
    align-items: start;
    margin-bottom: 16px;
}
.footer6 .single-footer-items .contact-box .pera a {
    display: inline-block;
    color: #0b0314;
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-medium);
    margin-left: 16px;
    transition: all 0.4s;
}
.footer6 .single-footer-items .contact-box:hover .pera a {
    color: #31572c;
    transition: all 0.4s;
}
.footer6 .coppyright p {
    color: #111;
    text-align: center;
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 18px; /* 100% */
    text-align: center;
    padding: 20px 0;
    border-top: 1px solid rgba(17, 17, 17, 0.0549019608);
}

.footer7 {
    padding-top: 80px;
}
.footer7 .footer-logo {
    width: 160px;
}
.footer7 .single-footer-items p {
    color: #111;
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 28px; /* 155.556% */
}
.footer7 .single-footer-items h3 {
    font-size: var(--f-fs-font-fs20);
    line-height: var(--f-fs-font-fs20);
    font-weight: var(--f-fw-bold);
    color: #0b0314;
    padding-bottom: 24px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .footer7 .single-footer-items h3 {
        margin-top: 16px;
        padding-top: 24px;
    }
}
@media (max-width: 767px) {
    .footer7 .single-footer-items h3 {
        margin-top: 16px;
        padding-top: 24px;
    }
}
.footer7 .single-footer-items ul.menu-list li a {
    color: #0b0314;
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-medium);
    display: inline-block;
    padding: 6px 0px;
    transition: all 0.4s;
    position: relative;
}
.footer7 .single-footer-items ul.menu-list li a::after {
    content: "";
    position: absolute;
    top: 10px;
    left: 0;
    height: 20px;
    width: 0px;
    background-color: #5957e5;
    opacity: 0;
    transition: all 0.4s;
}
.footer7 .single-footer-items ul.menu-list li a:hover {
    padding-left: 12px;
    transition: all 0.4s;
    color: #5957e5;
}
.footer7 .single-footer-items ul.menu-list li a:hover::after {
    width: 3px;
    opacity: 1;
}
.footer7 .single-footer-items .social-icon {
    margin-top: 24px;
}
.footer7 .single-footer-items .social-icon li {
    display: inline-block;
}
.footer7 .single-footer-items .social-icon li a {
    display: inline-block;
    margin: 0px 4px;
    height: 36px;
    width: 36px;
    line-height: 36px;
    border-radius: 36px;
    background-color: #f8f7ff;
    text-align: center;
    color: #001431;
    transition: all 0.4s;
}
.footer7 .single-footer-items .social-icon li a:hover {
    background-color: #5957e5;
    color: #fff;
}
.footer7 .single-footer-items .contact-box {
    display: flex;
    align-items: start;
    margin-bottom: 16px;
}
.footer7 .single-footer-items .contact-box .pera a {
    display: inline-block;
    color: #0b0314;
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-medium);
    margin-left: 16px;
    transition: all 0.4s;
}
.footer7 .single-footer-items .contact-box:hover .pera a {
    color: #5957e5;
    transition: all 0.4s;
}
.footer7 .coppyright p {
    color: #111;
    text-align: center;
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 18px; /* 100% */
    text-align: center;
    padding: 20px 0;
    border-top: 1px solid rgba(17, 17, 17, 0.0549019608);
}

.footer8 {
    padding-top: 80px;
}
.footer8 .footer-logo {
    width: 160px;
}
.footer8 .single-footer-items p {
    color: #111;
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 28px; /* 155.556% */
}
.footer8 .single-footer-items h3 {
    font-size: var(--f-fs-font-fs20);
    line-height: var(--f-fs-font-fs20);
    font-weight: var(--f-fw-bold);
    color: #0b0314;
    padding-bottom: 24px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .footer8 .single-footer-items h3 {
        margin-top: 16px;
        padding-top: 24px;
    }
}
@media (max-width: 767px) {
    .footer8 .single-footer-items h3 {
        margin-top: 16px;
        padding-top: 24px;
    }
}
.footer8 .single-footer-items ul.menu-list li a {
    color: #0b0314;
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-medium);
    display: inline-block;
    padding: 6px 0px;
    transition: all 0.4s;
    position: relative;
}
.footer8 .single-footer-items ul.menu-list li a::after {
    content: "";
    position: absolute;
    top: 10px;
    left: 0;
    height: 20px;
    width: 0px;
    background-color: #f6aa32;
    opacity: 0;
    transition: all 0.4s;
}
.footer8 .single-footer-items ul.menu-list li a:hover {
    padding-left: 12px;
    transition: all 0.4s;
    color: #f6aa32;
}
.footer8 .single-footer-items ul.menu-list li a:hover::after {
    width: 3px;
    opacity: 1;
}
.footer8 .single-footer-items .social-icon {
    margin-top: 24px;
}
.footer8 .single-footer-items .social-icon li {
    display: inline-block;
}
.footer8 .single-footer-items .social-icon li a {
    display: inline-block;
    margin: 0px 4px;
    height: 36px;
    width: 36px;
    line-height: 36px;
    border-radius: 36px;
    background-color: rgba(20, 19, 57, 0.0470588235);
    text-align: center;
    color: #001431;
    transition: all 0.4s;
}
.footer8 .single-footer-items .social-icon li a:hover {
    background-color: #141339;
    color: #fff;
}
.footer8 .single-footer-items .contact-box {
    display: flex;
    align-items: start;
    margin-bottom: 16px;
}
.footer8 .single-footer-items .contact-box .pera a {
    display: inline-block;
    color: #0b0314;
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-medium);
    margin-left: 16px;
    transition: all 0.4s;
}
.footer8 .single-footer-items .contact-box:hover .pera a {
    color: #f6aa32;
    transition: all 0.4s;
}
.footer8 .coppyright p {
    color: #111;
    text-align: center;
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 18px; /* 100% */
    text-align: center;
    padding: 20px 0;
    border-top: 1px solid rgba(17, 17, 17, 0.0549019608);
}

.footer9 {
    padding-top: 80px;
}
.footer9 .footer-logo {
    width: 160px;
}
.footer9 .single-footer-items p {
    color: #111;
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 28px; /* 155.556% */
}
.footer9 .single-footer-items h3 {
    font-size: var(--f-fs-font-fs20);
    line-height: var(--f-fs-font-fs20);
    font-weight: var(--f-fw-bold);
    color: #0b0314;
    padding-bottom: 24px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .footer9 .single-footer-items h3 {
        margin-top: 16px;
        padding-top: 24px;
    }
}
@media (max-width: 767px) {
    .footer9 .single-footer-items h3 {
        margin-top: 16px;
        padding-top: 24px;
    }
}
.footer9 .single-footer-items ul.menu-list li a {
    color: #0b0314;
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-medium);
    display: inline-block;
    padding: 6px 0px;
    transition: all 0.4s;
    position: relative;
}
.footer9 .single-footer-items ul.menu-list li a::after {
    content: "";
    position: absolute;
    top: 10px;
    left: 0;
    height: 20px;
    width: 0px;
    background-color: #2a9134;
    opacity: 0;
    transition: all 0.4s;
}
.footer9 .single-footer-items ul.menu-list li a:hover {
    padding-left: 12px;
    transition: all 0.4s;
    color: #2a9134;
}
.footer9 .single-footer-items ul.menu-list li a:hover::after {
    width: 3px;
    opacity: 1;
}
.footer9 .single-footer-items .social-icon {
    margin-top: 24px;
}
.footer9 .single-footer-items .social-icon li {
    display: inline-block;
}
.footer9 .single-footer-items .social-icon li a {
    display: inline-block;
    margin: 0px 4px;
    height: 36px;
    width: 36px;
    line-height: 36px;
    border-radius: 36px;
    background-color: rgba(20, 19, 57, 0.0470588235);
    text-align: center;
    color: #001431;
    transition: all 0.4s;
}
.footer9 .single-footer-items .social-icon li a:hover {
    background-color: #2a9134;
    color: #fff;
}
.footer9 .single-footer-items .contact-box {
    display: flex;
    align-items: start;
    margin-bottom: 16px;
}
.footer9 .single-footer-items .contact-box .pera a {
    display: inline-block;
    color: #0b0314;
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-medium);
    margin-left: 16px;
    transition: all 0.4s;
}
.footer9 .single-footer-items .contact-box:hover .pera a {
    color: #2a9134;
    transition: all 0.4s;
}
.footer9 .coppyright p {
    color: #111;
    text-align: center;
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 18px; /* 100% */
    text-align: center;
    padding: 20px 0;
    border-top: 1px solid rgba(17, 17, 17, 0.0549019608);
}

.footer10 {
    padding-top: 80px;
}
.footer10 .footer-logo {
    width: 160px;
}
.footer10 .single-footer-items p {
    color: #111;
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 28px; /* 155.556% */
}
.footer10 .single-footer-items h3 {
    font-size: var(--f-fs-font-fs20);
    line-height: var(--f-fs-font-fs20);
    font-weight: var(--f-fw-bold);
    color: #0b0314;
    padding-bottom: 24px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .footer10 .single-footer-items h3 {
        margin-top: 16px;
        padding-top: 24px;
    }
}
@media (max-width: 767px) {
    .footer10 .single-footer-items h3 {
        margin-top: 16px;
        padding-top: 24px;
    }
}
.footer10 .single-footer-items ul.menu-list li a {
    color: #0b0314;
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-medium);
    display: inline-block;
    padding: 6px 0px;
    transition: all 0.4s;
    position: relative;
}
.footer10 .single-footer-items ul.menu-list li a::after {
    content: "";
    position: absolute;
    top: 10px;
    left: 0;
    height: 20px;
    width: 0px;
    background-color: #fa6a4b;
    opacity: 0;
    transition: all 0.4s;
}
.footer10 .single-footer-items ul.menu-list li a:hover {
    padding-left: 12px;
    transition: all 0.4s;
    color: #fa6a4b;
}
.footer10 .single-footer-items ul.menu-list li a:hover::after {
    width: 3px;
    opacity: 1;
}
.footer10 .single-footer-items .social-icon {
    margin-top: 24px;
}
.footer10 .single-footer-items .social-icon li {
    display: inline-block;
}
.footer10 .single-footer-items .social-icon li a {
    display: inline-block;
    margin: 0px 4px;
    height: 36px;
    width: 36px;
    line-height: 36px;
    border-radius: 36px;
    background-color: rgba(20, 19, 57, 0.0470588235);
    text-align: center;
    color: #001431;
    transition: all 0.4s;
}
.footer10 .single-footer-items .social-icon li a:hover {
    background-color: #fa6a4b;
    color: #fff;
}
.footer10 .single-footer-items .contact-box {
    display: flex;
    align-items: start;
    margin-bottom: 16px;
}
.footer10 .single-footer-items .contact-box .pera a {
    display: inline-block;
    color: #0b0314;
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-medium);
    margin-left: 16px;
    transition: all 0.4s;
}
.footer10 .single-footer-items .contact-box:hover .pera a {
    color: #fa6a4b;
    transition: all 0.4s;
}
.footer10 .coppyright p {
    color: #111;
    text-align: center;
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 18px; /* 100% */
    text-align: center;
    padding: 20px 0;
    border-top: 1px solid rgba(17, 17, 17, 0.0549019608);
}

/*
::::::::::::::::::::::::::
 FOOTER AREA CSS
::::::::::::::::::::::::::
*/
/*
::::::::::::::::::::::::::
 HEADER AREA CSS
::::::::::::::::::::::::::
*/
.header-area.header-area1 {
    border-bottom: 1px solid #fff;
}
.header-area.header-area1 .header1-buttons {
    display: flex;
    align-items: center;
}
.header-area.header-area1 .header1-buttons .contact-btn {
    display: flex;
    align-items: center;
    margin-right: 24px;
}
.header-area.header-area1 .header1-buttons .contact-btn .icon {
    height: 38px;
    width: 38px;
    text-align: center;
    line-height: 38px;
    border-radius: 50%;
    background-color: var(--ztc-bg-bg-2);
    margin-right: 10px;
}
.header-area.header-area1 .header1-buttons .contact-btn .headding p {
    font-size: 12px;
    line-height: 12px;
    color: var(--ztc-text-pera-text-1);
}
.header-area.header-area1 .header1-buttons .contact-btn .headding a {
    font-size: var(--f-fs-font-fs16);
    line-height: var(--f-fs-font-fs16);
    font-weight: var(--f-fw-blod);
    color: var(--ztc-text-text-1);
    display: inline-block;
    margin-top: 10px;
}

.header-area.header-area1 .header-elements .main-menu-ex ul li a.active {
    color: var(--vtc-bg-main-bg-1);
}

.header-area.header-area3 .header-elements .main-menu-ex ul li a.active {
    color: var(--vtc-bg-main-bg-1);
}

.header-area.header-area4 .header-elements .main-menu-ex ul li a.active {
    color: var(--vtc-bg-main-bg-6);
}

.header-area.header-area5 .header-elements .main-menu-ex ul li a.active {
    color: var(--vtc-bg-main-bg-2);
}

.header-area.header-area2 .header-elements .main-menu-ex ul li a.active {
    color: var(--vtc-bg-main-bg-2);
}

.header-area.header-area6 .header-elements .main-menu-ex ul li a.active {
    color: #f1c832;
}

.header-area.header-area7 .header-elements .main-menu-ex ul li a.active {
    color: #5957e5;
}

.header-area.header-area8 .header-elements .main-menu-ex ul li a.active {
    color: #f6aa32;
}

.header-area.header-area9 .header-elements .main-menu-ex ul li a.active {
    color: #2a9134;
}

.header-area.header-area10 .header-elements .main-menu-ex ul li a.active {
    color: #fa6444;
}

/*
::::::::::::::::::::::::::
 HEADER AREA CSS
::::::::::::::::::::::::::
*/
/*
 ::::::::::::::::::::::::::
  NAV MENU AREA CSS
 ::::::::::::::::::::::::::
 */
.header-area {
    padding: 12px 0;
    position: absolute;
    background: transparent;
    width: 100%;
    z-index: 99;
}

.site-logo {
    max-width: 130px;
    height: auto;
    display: flex;
    align-items: center;
}

.site-logo a {
    display: inline-block;
    position: relative;
    z-index: 9;
}

.header-area .header-elements {
    display: flex;
    align-items: center;
    align-items: center;
    justify-content: space-between;
}

.header-area1.sticky .header-elements .main-menu-ex {
    padding: 7px 24px;
    position: relative;
}

/* 
++++++++++++++++++++++++++++++++++
=========nav menu all ==========
+++++++++++++++++++++++++++++++++
*/
.header-area.header-area1 .main-menu-ex ul li .mega-menu-all {
    visibility: hidden;
    opacity: 0;
    box-shadow: rgba(0, 0, 0, 0.2) 0px 20px 30px;
    position: absolute;
    background: white;
    top: 201.3%;
    transform: scale(1, 0);
    z-index: 0;
    transition: all 0.4s;
    border-radius: 5px;
    padding: 36px 0;
    left: -400px;
    width: 1000px;
    min-height: 300px;
    overflow-x: hidden;
    overflow-y: hidden;
    transform-origin: top;
    display: block;
}
.header-area.header-area1 .main-menu-ex ul li .mega-menu-all ul {
    box-shadow: none !important;
    padding: 0px 10px !important;
}
.header-area.header-area1 .main-menu-ex ul li .mega-menu-all ul li a {
    display: inline-block;
    padding: 8px 40px;
}
.header-area.header-area1 .main-menu-ex ul li .mega-menu-all .mega-menu-single {
    position: relative;
}
.header-area.header-area1 .main-menu-ex ul li .mega-menu-all .mega-menu-single h3 {
    font-size: 20px;
    line-height: 20px;
    font-weight: 600;
    color: #081120;
    padding-left: 44px;
    position: relative;
}
.header-area.header-area1 .main-menu-ex ul li .mega-menu-all .mega-menu-single h3::after {
    content: "";
    position: absolute;
    bottom: -14px;
    left: 43px;
    height: 2px;
    width: 25%;
    background-color: rgba(145, 145, 145, 0.2117647059);
}
.header-area.header-area1 .main-menu-ex ul li .mega-menu-all .mega-menu-single::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    height: 400px;
    width: 2px;
    background-color: rgba(145, 145, 145, 0.2117647059);
}
.header-area.header-area1 .main-menu-ex ul li .mega-menu-all .mega-menu-single.dis1::after {
    display: none;
}
.header-area.header-area1 .main-menu-ex ul li .tp-submenu {
    visibility: hidden;
    opacity: 0;
    box-shadow: rgba(0, 0, 0, 0.2) 0px 20px 30px;
    position: absolute;
    background: white;
    top: 201.3%;
    transform: scale(1, 0);
    z-index: 0;
    transition: all 0.4s;
    border-radius: 5px;
    padding: 20px;
    left: -340px;
    width: 1300px;
    max-height: 600px;
    overflow-x: scroll;
    overflow-x: hidden;
    transform-origin: top;
    display: block;
}
.header-area.header-area1 .main-menu-ex ul li .tp-submenu .all-images-menu {
    display: flex;
    align-items: center;
}
.header-area.header-area1 .main-menu-ex ul li .tp-submenu .homemenu-thumb {
    transition: all 0.4s;
    position: relative;
    z-index: 1;
    margin: 0 20px 20px 0;
    text-align: center;
    overflow: hidden;
}
.header-area.header-area1 .main-menu-ex ul li .tp-submenu .homemenu-thumb .text {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    top: 10px;
    left: 0px;
}
.header-area.header-area1 .main-menu-ex ul li .tp-submenu .homemenu-thumb .text::after {
    position: absolute;
    content: "";
    height: 100px;
    width: 100px;
    left: -54px;
    top: -62px;
    transition: all 0.4s;
    background: #081120;
    transform: rotate(-45deg);
    z-index: -1;
}
.header-area.header-area1 .main-menu-ex ul li .tp-submenu .homemenu-thumb .text h2 {
    font-size: 20px;
    color: #fff;
    transform: rotate(-45deg);
    margin-left: 7px;
    margin-bottom: 13px;
    font-weight: 700;
}
.header-area.header-area1 .main-menu-ex ul li .tp-submenu .homemenu-thumb .bottom-heading {
    color: #08111a;
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: 18px; /* 100% */
    display: inline-block;
    margin-top: 8px;
}
.header-area.header-area1 .main-menu-ex ul li .tp-submenu .homemenu-thumb .bottom-heading:hover {
    color: #ff7a01;
    transition: all 0.4s;
}
.header-area.header-area1 .main-menu-ex ul li .tp-submenu .homemenu-thumb .bottom-text {
    color: #000;
    font-size: 24px;
    font-style: normal;
    font-weight: 700;
    line-height: 24px; /* 100% */
    letter-spacing: 0.18px;
    display: inline-block;
    padding-top: 20px;
}
.header-area.header-area1 .main-menu-ex ul li .tp-submenu .homemenu-thumb:hover .img1::after {
    transform: scale(1);
    transition: all 0.4s;
    visibility: visible;
    opacity: 0.4;
}
.header-area.header-area1 .main-menu-ex ul li .tp-submenu .homemenu-thumb:hover .homemenu-btn {
    top: 26%;
    visibility: visible;
    opacity: 1;
    transition: all 0.6s;
    margin-top: 4px;
}
.header-area.header-area1 .main-menu-ex ul li .tp-submenu .homemenu-thumb:hover .bottom-text {
    color: var(--vtc-bg-main-bg-1);
    transition: all 0.4s;
}
.header-area.header-area1 .main-menu-ex ul li .tp-submenu .homemenu-thumb.active .img1::after {
    transform: scale(1);
    transition: all 0.4s;
    visibility: visible;
    opacity: 0.3;
}
.header-area.header-area1 .main-menu-ex ul li .tp-submenu .homemenu-thumb.active .img1 .coming {
    position: absolute;
    top: 50%;
    left: 50%;
    margin-left: -84px;
    margin-top: -30px;
    height: 60px;
    width: 168px;
    z-index: 99;
    border: none;
}
.header-area.header-area1 .main-menu-ex ul li .tp-submenu .homemenu-thumb .img1 {
    position: relative;
    z-index: 1;
    overflow: hidden;
    width: 234px;
    height: 300px;
}
.header-area.header-area1 .main-menu-ex ul li .tp-submenu .homemenu-thumb .img1::after {
    position: absolute;
    content: "";
    height: 100%;
    width: 100%;
    left: 0;
    top: 0;
    transition: all 0.4s;
    background: #000026;
    opacity: 0;
    border-radius: 4px;
    transform: scale(0.8);
    z-index: 1;
    visibility: hidden;
}
.header-area.header-area1 .main-menu-ex ul li .tp-submenu .homemenu-thumb .img1 img {
    height: 100%;
    width: 100%;
    border-radius: 4px;
    transition: all 0.4s;
    border: 1px solid #e5e7eb;
}
.header-area.header-area1 .main-menu-ex ul li .tp-submenu .homemenu-thumb .homemenu-btn {
    position: absolute;
    top: 30%;
    z-index: 2;
    visibility: hidden;
    opacity: 0;
    text-align: center;
    transition: all 0.6s;
    margin: 0 auto;
    left: 18%;
    right: 20%;
}
.header-area.header-area1 .main-menu-ex ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn1 {
    display: inline-block;
    padding: 16px;
    transition: all 0.4s;
    border-radius: 7px;
    position: relative;
    width: 150px;
    color: #fff;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: 16px; /* 100% */
    text-transform: capitalize;
    z-index: 1;
    background: #ff7a01;
}
.header-area.header-area1 .main-menu-ex ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn1 i {
    transform: rotate(-45deg);
    margin-left: 4px;
    transition: all 0.4s;
}
.header-area.header-area1 .main-menu-ex ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn1:hover {
    color: #fff;
    transition: all 0.4s;
    transform: translateY(-5px);
    background: #ff7a01;
}
.header-area.header-area1 .main-menu-ex ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn1:hover i {
    transform: rotate(0);
    transition: all 0.4s;
}
.header-area.header-area1 .main-menu-ex ul li ul {
    position: absolute;
    left: 0;
    top: 50px;
    border-radius: 4px;
    width: 200px;
    box-shadow: rgba(0, 0, 0, 0.2) 0px 20px 30px;
    padding: 10px;
}
.header-area.header-area1 .main-menu-ex ul li ul li ul {
    left: 200px;
}
.header-area.header-area1 .main-menu-ex ul li ul li a {
    color: var(--qt-text-h-text1);
}
.header-area.header-area1 .main-menu-ex ul li ul li a::after {
    content: "";
    position: absolute;
    bottom: 9px;
    left: 0;
    width: 0%;
    height: 2px;
    color: #fff;
    border-radius: 4px;
    transition: all 0.4s;
    z-index: -1;
    opacity: 1;
}
.header-area.header-area1 .main-menu-ex ul li ul li a:hover::after {
    background-color: var(--qt-text-h-text1);
    border-radius: 4px;
    width: 50px;
    left: 17px;
    opacity: 1;
}
.header-area.header-area1 .main-menu-ex ul li a {
    color: var(--qt-text-h-text1);
    font-size: 16px;
    display: block;
    font-weight: 500;
    padding: 8px 15px;
    transition: all 0.3s;
}
.header-area.header-area1 .main-menu-ex ul li:hover .tp-submenu {
    visibility: visible;
    transition: all 0.5s ease-in-out;
    opacity: 1;
    z-index: 9;
    top: 150.3%;
    position: absolute;
    transition: all 0.4s;
    transform: scale(1);
}
.header-area.header-area1 .main-menu-ex ul li:hover .mega-menu-all {
    opacity: 1;
    visibility: visible;
    transform: scale(1) translateY(-30px);
}

.main-menu-ex li {
    display: inline-block;
}

.main-menu-ex li {
    position: relative;
    z-index: 9;
}

.main-menu-ex li li {
    display: block;
}

.main-menu-ex li > ul {
    opacity: 0;
    visibility: hidden;
    top: 60px;
    right: 0;
    transition: all 0.3s;
    transform: translateY(20px) rotateX(45deg);
}

.main-menu-ex li li > ul {
    left: 100%;
    right: auto;
}

.main-menu-ex li li:hover > ul {
    top: 0;
}

.main-menu-ex li:hover > ul {
    visibility: visible;
    opacity: 1;
    top: 50px;
    transition: all 0.3s;
    z-index: 99;
    transform: translateY(0);
}

li.has-dropdown1 {
    position: relative;
}

li.has-dropdown1 span {
    position: absolute;
    right: 10px;
}

.header-area1.sticky {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    transform: translate3d(0, 0, 0);
    z-index: 111;
    animation-name: fade-in-down;
    animation-duration: 1s;
    animation-fill-mode: forwards;
    background-color: var(--vtc-bg-bg-white);
    box-shadow: rgba(0, 0, 0, 0.068) 0px 20px 30px;
    transition: all 0.3s ease-in-out;
    z-index: 999;
}

.header-area2.sticky {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    transform: translate3d(0, 0, 0);
    z-index: 111;
    animation-name: fade-in-down;
    animation-duration: 1s;
    animation-fill-mode: forwards;
    background-color: #000029;
    box-shadow: rgba(0, 0, 0, 0.068) 0px 20px 30px;
    transition: all 0.3s ease-in-out;
    z-index: 999;
}

.header-area3.sticky {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    transform: translate3d(0, 0, 0);
    z-index: 111;
    animation-name: fade-in-down;
    animation-duration: 1s;
    animation-fill-mode: forwards;
    background-color: #44524d;
    box-shadow: rgba(0, 0, 0, 0.068) 0px 20px 30px;
    transition: all 0.3s ease-in-out;
    z-index: 999;
}
.header-area3.sticky .header-bg {
    padding: 0;
}

.header-area4.sticky {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    transform: translate3d(0, 0, 0);
    z-index: 111;
    animation-name: fade-in-down;
    animation-duration: 1s;
    animation-fill-mode: forwards;
    background-color: #19326a;
    box-shadow: rgba(0, 0, 0, 0.068) 0px 20px 30px;
    transition: all 0.3s ease-in-out;
    z-index: 999;
}

.header-area5.sticky {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    transform: translate3d(0, 0, 0);
    z-index: 111;
    animation-name: fade-in-down;
    animation-duration: 1s;
    animation-fill-mode: forwards;
    background-color: #d1d8e2;
    box-shadow: rgba(0, 0, 0, 0.068) 0px 20px 30px;
    transition: all 0.3s ease-in-out;
    z-index: 999;
    padding-top: 0 !important;
    padding-bottom: 0;
}

.header-area6.sticky {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    transform: translate3d(0, 0, 0);
    z-index: 111;
    animation-name: fade-in-down;
    animation-duration: 1s;
    animation-fill-mode: forwards;
    background-color: #fff;
    box-shadow: rgba(0, 0, 0, 0.068) 0px 20px 30px;
    transition: all 0.3s ease-in-out;
    z-index: 999;
    padding-top: 0 !important;
    padding-bottom: 0;
}
.header-area6.sticky .header-elements {
    padding: 14px 0px !important;
}

.header-area7.sticky {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    transform: translate3d(0, 0, 0);
    z-index: 111;
    animation-name: fade-in-down;
    animation-duration: 1s;
    animation-fill-mode: forwards;
    background-color: #fff;
    box-shadow: rgba(0, 0, 0, 0.068) 0px 20px 30px;
    transition: all 0.3s ease-in-out;
    z-index: 999;
    padding-top: 0 !important;
    padding-bottom: 0;
}
.header-area7.sticky .header-elements {
    padding: 14px 0px !important;
}

.header-area8.sticky {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    transform: translate3d(0, 0, 0);
    z-index: 111;
    animation-name: fade-in-down;
    animation-duration: 1s;
    animation-fill-mode: forwards;
    background-color: #fff;
    box-shadow: rgba(0, 0, 0, 0.068) 0px 20px 30px;
    transition: all 0.3s ease-in-out;
    z-index: 999;
    padding-top: 0 !important;
    padding-bottom: 0;
}
.header-area8.sticky .header-elements {
    padding: 14px 0px !important;
}

.header-area9.sticky {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    transform: translate3d(0, 0, 0);
    z-index: 111;
    animation-name: fade-in-down;
    animation-duration: 1s;
    animation-fill-mode: forwards;
    background-color: #f3f5f2;
    box-shadow: rgba(0, 0, 0, 0.068) 0px 20px 30px;
    transition: all 0.3s ease-in-out;
    z-index: 999;
    padding-top: 0 !important;
    padding-bottom: 0;
}
.header-area9.sticky .header-elements {
    padding: 14px 0px !important;
}

.header-area10.sticky {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    transform: translate3d(0, 0, 0);
    z-index: 111;
    animation-name: fade-in-down;
    animation-duration: 1s;
    animation-fill-mode: forwards;
    background-color: #fff;
    box-shadow: rgba(0, 0, 0, 0.068) 0px 20px 30px;
    transition: all 0.3s ease-in-out;
    z-index: 999;
    padding-top: 0 !important;
    padding-bottom: 0;
}
.header-area10.sticky .header-elements {
    padding: 14px 0px !important;
}
@keyframes fade-in-down {
    0% {
        transform: translate3d(0, -50px, 0);
    }
    100% {
        opacity: 1;
        transform: none;
    }
}
.fade-in-down {
    animation-name: fade-in-down;
    animation-duration: 1s;
    animation-fill-mode: forwards;
}

/* 
++++++++++++++++++++++++++++++++++
==== =====nav menu all ==========
+++++++++++++++++++++++++++++++++
*/
/* 
++++++++++++++++++++++++++++++++++
==== =====nav menu all 2 =========
+++++++++++++++++++++++++++++++++
*/
.header-area.header-area1 .header-elements .main-menu-ex {
    padding: 2px 24px;
    position: relative;
}
.header-area.header-area1 .header-elements .main-menu-ex ul li ul {
    position: absolute;
    left: 0;
    top: 50px;
    border-radius: 4px;
    background: #fff;
    -webkit-backdrop-filter: blur(27px);
    backdrop-filter: blur(27px);
    width: 206px;
    box-shadow: rgba(0, 0, 0, 0.068) 0px 20px 30px;
    padding: 10px;
}
.header-area.header-area1 .header-elements .main-menu-ex ul li ul:hover > a {
    transition: all 0.4s;
    padding-left: 25px;
    color: red;
}
.header-area.header-area1 .header-elements .main-menu-ex ul li ul:hover > a::after {
    background: blue;
    transition: all 0.4s;
    visibility: visible;
    opacity: 1;
}
.header-area.header-area1 .header-elements .main-menu-ex ul li ul li ul {
    left: 190px;
    top: 10px;
}
.header-area.header-area1 .header-elements .main-menu-ex ul li ul li a {
    color: var(--qt-text-h-text3);
}
.header-area.header-area1 .header-elements .main-menu-ex ul li ul li a::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: -10px;
    width: 0px;
    height: 100%;
    color: var(--qt-text-h-text3);
    border-radius: 4px;
    transition: all 0.4s;
    z-index: -1;
    opacity: 1;
}
.header-area.header-area1 .header-elements .main-menu-ex ul li ul li a:hover {
    color: var(--vtc-bg-main-bg-1);
    transition: all 0.4s;
}
.header-area.header-area1 .header-elements .main-menu-ex ul li ul li a:hover::after {
    background-color: var(--vtc-bg-main-bg-1);
    border-radius: 4px;
    width: 5px;
    left: -10px;
    opacity: 1;
}
.header-area.header-area1 .header-elements .main-menu-ex ul li a {
    color: var(--vtc-text-heading-text-1);
    font-size: 16px;
    display: block;
    font-weight: 500;
    padding: 8px 15px;
    transition: all 0.3s;
}
.header-area.header-area1 .header-elements .main-menu-ex ul li a:hover {
    color: var(--vtc-bg-main-bg-1);
}

.header-area.header-area3 .header-elements .main-menu-ex {
    padding: 2px 24px;
    position: relative;
}
.header-area.header-area3 .header-elements .main-menu-ex ul li .mega-menu-all {
    visibility: hidden;
    opacity: 0;
    box-shadow: rgba(0, 0, 0, 0.2) 0px 20px 30px;
    position: absolute;
    background: white;
    top: 201.3%;
    transform: scale(1, 0);
    z-index: 0;
    transition: all 0.4s;
    border-radius: 5px;
    padding: 36px 0;
    left: -400px;
    width: 1000px;
    min-height: 300px;
    overflow-x: hidden;
    overflow-y: hidden;
    transform-origin: top;
    display: block;
}
.header-area.header-area3 .header-elements .main-menu-ex ul li .mega-menu-all ul {
    box-shadow: none !important;
    padding: 0px 10px !important;
}
.header-area.header-area3 .header-elements .main-menu-ex ul li .mega-menu-all ul li a {
    display: inline-block;
    padding: 8px 40px;
}
.header-area.header-area3 .header-elements .main-menu-ex ul li .mega-menu-all .mega-menu-single {
    position: relative;
}
.header-area.header-area3 .header-elements .main-menu-ex ul li .mega-menu-all .mega-menu-single h3 {
    font-size: 20px;
    line-height: 20px;
    font-weight: 600;
    color: #081120;
    padding-left: 44px;
    position: relative;
}
.header-area.header-area3 .header-elements .main-menu-ex ul li .mega-menu-all .mega-menu-single h3::after {
    content: "";
    position: absolute;
    bottom: -14px;
    left: 43px;
    height: 2px;
    width: 25%;
    background-color: rgba(145, 145, 145, 0.2117647059);
}
.header-area.header-area3 .header-elements .main-menu-ex ul li .mega-menu-all .mega-menu-single::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    height: 400px;
    width: 2px;
    background-color: rgba(145, 145, 145, 0.2117647059);
}
.header-area.header-area3 .header-elements .main-menu-ex ul li .mega-menu-all .mega-menu-single.dis1::after {
    display: none;
}
.header-area.header-area3 .header-elements .main-menu-ex ul li .tp-submenu {
    visibility: hidden;
    opacity: 0;
    box-shadow: rgba(0, 0, 0, 0.2) 0px 20px 30px;
    position: absolute;
    background: white;
    top: 201.3%;
    transform: scale(1, 0);
    z-index: 0;
    transition: all 0.4s;
    border-radius: 5px;
    padding: 20px;
    left: -340px;
    width: 1300px;
    max-height: 600px;
    overflow-x: scroll;
    overflow-x: hidden;
    transform-origin: top;
    display: block;
}
.header-area.header-area3 .header-elements .main-menu-ex ul li .tp-submenu .all-images-menu {
    display: flex;
    align-items: center;
}
.header-area.header-area3 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb {
    transition: all 0.4s;
    position: relative;
    z-index: 1;
    margin: 0 20px 20px 0;
    text-align: center;
    overflow: hidden;
}
.header-area.header-area3 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .text {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    top: 10px;
    left: 0px;
}
.header-area.header-area3 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .text::after {
    position: absolute;
    content: "";
    height: 100px;
    width: 100px;
    left: -54px;
    top: -62px;
    transition: all 0.4s;
    background: #081120;
    transform: rotate(-45deg);
    z-index: -1;
}
.header-area.header-area3 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .text h2 {
    font-size: 20px;
    color: #fff;
    transform: rotate(-45deg);
    margin-left: 7px;
    margin-bottom: 13px;
    font-weight: 700;
}
.header-area.header-area3 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .bottom-heading {
    color: #08111a;
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: 18px; /* 100% */
    display: inline-block;
    margin-top: 8px;
}
.header-area.header-area3 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .bottom-heading:hover {
    color: #fd965b;
    transition: all 0.4s;
}
.header-area.header-area3 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .bottom-text {
    color: #000;
    font-size: 24px;
    font-style: normal;
    font-weight: 700;
    line-height: 24px; /* 100% */
    letter-spacing: 0.18px;
    display: inline-block;
    padding-top: 20px;
}
.header-area.header-area3 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb:hover .img1::after {
    transform: scale(1);
    transition: all 0.4s;
    visibility: visible;
    opacity: 0.4;
}
.header-area.header-area3 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb:hover .homemenu-btn {
    top: 26%;
    visibility: visible;
    opacity: 1;
    transition: all 0.6s;
    margin-top: 4px;
}
.header-area.header-area3 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb:hover .bottom-text {
    color: var(--vtc-bg-main-bg-1);
    transition: all 0.4s;
}
.header-area.header-area3 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb.active .img1::after {
    transform: scale(1);
    transition: all 0.4s;
    visibility: visible;
    opacity: 0.3;
}
.header-area.header-area3 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb.active .img1 .coming {
    position: absolute;
    top: 50%;
    left: 50%;
    margin-left: -84px;
    margin-top: -30px;
    height: 60px;
    width: 168px;
    z-index: 99;
    border: none;
}
.header-area.header-area3 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .img1 {
    position: relative;
    z-index: 1;
    overflow: hidden;
    width: 234px;
    height: 300px;
}
.header-area.header-area3 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .img1::after {
    position: absolute;
    content: "";
    height: 100%;
    width: 100%;
    left: 0;
    top: 0;
    transition: all 0.4s;
    background: #000026;
    opacity: 0;
    border-radius: 4px;
    transform: scale(0.8);
    z-index: 1;
    visibility: hidden;
}
.header-area.header-area3 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .img1 img {
    height: 100%;
    width: 100%;
    border-radius: 4px;
    transition: all 0.4s;
    border: 1px solid #e5e7eb;
}
.header-area.header-area3 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .homemenu-btn {
    position: absolute;
    top: 30%;
    z-index: 2;
    visibility: hidden;
    opacity: 0;
    text-align: center;
    transition: all 0.6s;
    margin: 0 auto;
    left: 18%;
    right: 20%;
}
.header-area.header-area3 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn1 {
    display: inline-block;
    padding: 16px;
    transition: all 0.4s;
    border-radius: 7px;
    position: relative;
    width: 150px;
    color: #fff;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: 16px; /* 100% */
    text-transform: capitalize;
    z-index: 1;
    background: #fd965b;
}
.header-area.header-area3 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn1 i {
    transform: rotate(-45deg);
    margin-left: 4px;
    transition: all 0.4s;
}
.header-area.header-area3 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn1:hover {
    color: #fff;
    transition: all 0.4s;
    transform: translateY(-5px);
    background: #fd965b;
}
.header-area.header-area3 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn1:hover i {
    transform: rotate(0);
    transition: all 0.4s;
}
.header-area.header-area3 .header-elements .main-menu-ex ul li ul {
    position: absolute;
    left: 0;
    top: 50px;
    border-radius: 4px;
    background: #fff;
    -webkit-backdrop-filter: blur(27px);
    backdrop-filter: blur(27px);
    width: 206px;
    box-shadow: rgba(0, 0, 0, 0.068) 0px 20px 30px;
    padding: 10px;
}
.header-area.header-area3 .header-elements .main-menu-ex ul li ul:hover > a {
    transition: all 0.4s;
    padding-left: 25px;
    color: red;
}
.header-area.header-area3 .header-elements .main-menu-ex ul li ul:hover > a::after {
    background: blue;
    transition: all 0.4s;
    visibility: visible;
    opacity: 1;
}
.header-area.header-area3 .header-elements .main-menu-ex ul li ul li ul {
    left: 190px;
    top: 10px;
}
.header-area.header-area3 .header-elements .main-menu-ex ul li ul li a {
    color: var(--qt-text-h-text3);
}
.header-area.header-area3 .header-elements .main-menu-ex ul li ul li a::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: -10px;
    width: 0px;
    height: 100%;
    color: var(--qt-text-h-text3);
    border-radius: 4px;
    transition: all 0.4s;
    z-index: -1;
    opacity: 1;
}
.header-area.header-area3 .header-elements .main-menu-ex ul li ul li a:hover {
    color: var(--vtc-bg-main-bg-1);
    transition: all 0.4s;
}
.header-area.header-area3 .header-elements .main-menu-ex ul li ul li a:hover::after {
    background-color: var(--vtc-bg-main-bg-1);
    border-radius: 4px;
    width: 5px;
    left: -10px;
    opacity: 1;
}
.header-area.header-area3 .header-elements .main-menu-ex ul li a {
    color: #fff;
    font-size: 16px;
    display: block;
    font-weight: 400;
    padding: 8px 15px;
    transition: all 0.3s;
    font-weight: 500;
}
.header-area.header-area3 .header-elements .main-menu-ex ul li a:hover {
    color: var(--vtc-bg-main-bg-1);
    transition: all 0.4s;
}
.header-area.header-area3 .header-elements .main-menu-ex ul li:hover .tp-submenu {
    visibility: visible;
    transition: all 0.5s ease-in-out;
    opacity: 1;
    z-index: 9;
    top: 150.3%;
    position: absolute;
    transition: all 0.4s;
    transform: scale(1);
}
.header-area.header-area3 .header-elements .main-menu-ex ul li:hover .mega-menu-all {
    opacity: 1;
    visibility: visible;
    transform: scale(1) translateY(-30px);
}

.header-area.header-area2 .header-elements .main-menu-ex {
    padding: 2px 24px;
    position: relative;
}
.header-area.header-area2 .header-elements .main-menu-ex ul li .mega-menu-all {
    visibility: hidden;
    opacity: 0;
    box-shadow: rgba(0, 0, 0, 0.2) 0px 20px 30px;
    position: absolute;
    background: white;
    top: 201.3%;
    transform: scale(1, 0);
    z-index: 0;
    transition: all 0.4s;
    border-radius: 5px;
    padding: 36px 0;
    left: -400px;
    width: 1000px;
    min-height: 300px;
    overflow-x: hidden;
    overflow-y: hidden;
    transform-origin: top;
    display: block;
}
.header-area.header-area2 .header-elements .main-menu-ex ul li .mega-menu-all ul {
    box-shadow: none !important;
    padding: 0px 10px !important;
}
.header-area.header-area2 .header-elements .main-menu-ex ul li .mega-menu-all ul li a {
    display: inline-block;
    padding: 8px 40px;
}
.header-area.header-area2 .header-elements .main-menu-ex ul li .mega-menu-all .mega-menu-single {
    position: relative;
}
.header-area.header-area2 .header-elements .main-menu-ex ul li .mega-menu-all .mega-menu-single h3 {
    font-size: 20px;
    line-height: 20px;
    font-weight: 600;
    color: #081120;
    padding-left: 44px;
    position: relative;
}
.header-area.header-area2 .header-elements .main-menu-ex ul li .mega-menu-all .mega-menu-single h3::after {
    content: "";
    position: absolute;
    bottom: -14px;
    left: 43px;
    height: 2px;
    width: 25%;
    background-color: rgba(145, 145, 145, 0.2117647059);
}
.header-area.header-area2 .header-elements .main-menu-ex ul li .mega-menu-all .mega-menu-single::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    height: 400px;
    width: 2px;
    background-color: rgba(145, 145, 145, 0.2117647059);
}
.header-area.header-area2 .header-elements .main-menu-ex ul li .mega-menu-all .mega-menu-single.dis1::after {
    display: none;
}
.header-area.header-area2 .header-elements .main-menu-ex ul li .tp-submenu {
    visibility: hidden;
    opacity: 0;
    box-shadow: rgba(0, 0, 0, 0.2) 0px 20px 30px;
    position: absolute;
    background: white;
    top: 201.3%;
    transform: scale(1, 0);
    z-index: 0;
    transition: all 0.4s;
    border-radius: 5px;
    padding: 20px;
    left: -340px;
    width: 1300px;
    max-height: 600px;
    overflow-x: scroll;
    overflow-x: hidden;
    transform-origin: top;
    display: block;
}
.header-area.header-area2 .header-elements .main-menu-ex ul li .tp-submenu .all-images-menu {
    display: flex;
    align-items: center;
}
.header-area.header-area2 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb {
    transition: all 0.4s;
    position: relative;
    z-index: 1;
    margin: 0 20px 20px 0;
    text-align: center;
    overflow: hidden;
}
.header-area.header-area2 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .text {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    top: 10px;
    left: 0px;
}
.header-area.header-area2 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .text::after {
    position: absolute;
    content: "";
    height: 100px;
    width: 100px;
    left: -54px;
    top: -62px;
    transition: all 0.4s;
    background: #081120;
    transform: rotate(-45deg);
    z-index: -1;
}
.header-area.header-area2 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .text h2 {
    font-size: 20px;
    color: #fff;
    transform: rotate(-45deg);
    margin-left: 7px;
    margin-bottom: 13px;
    font-weight: 700;
}
.header-area.header-area2 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .bottom-heading {
    color: #08111a;
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: 18px; /* 100% */
    display: inline-block;
    margin-top: 8px;
}
.header-area.header-area2 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .bottom-heading:hover {
    color: #fd965b;
    transition: all 0.4s;
}
.header-area.header-area2 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .bottom-text {
    color: #000;
    font-size: 24px;
    font-style: normal;
    font-weight: 700;
    line-height: 24px; /* 100% */
    letter-spacing: 0.18px;
    display: inline-block;
    padding-top: 20px;
}
.header-area.header-area2 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb:hover .img1::after {
    transform: scale(1);
    transition: all 0.4s;
    visibility: visible;
    opacity: 0.4;
}
.header-area.header-area2 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb:hover .homemenu-btn {
    top: 26%;
    visibility: visible;
    opacity: 1;
    transition: all 0.6s;
    margin-top: 4px;
}
.header-area.header-area2 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb:hover .bottom-text {
    color: var(--vtc-bg-main-bg-1);
    transition: all 0.4s;
}
.header-area.header-area2 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb.active .img1::after {
    transform: scale(1);
    transition: all 0.4s;
    visibility: visible;
    opacity: 0.3;
}
.header-area.header-area2 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb.active .img1 .coming {
    position: absolute;
    top: 50%;
    left: 50%;
    margin-left: -84px;
    margin-top: -30px;
    height: 60px;
    width: 168px;
    z-index: 99;
    border: none;
}
.header-area.header-area2 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .img1 {
    position: relative;
    z-index: 1;
    overflow: hidden;
    width: 234px;
    height: 300px;
}
.header-area.header-area2 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .img1::after {
    position: absolute;
    content: "";
    height: 100%;
    width: 100%;
    left: 0;
    top: 0;
    transition: all 0.4s;
    background: #000026;
    opacity: 0;
    border-radius: 4px;
    transform: scale(0.8);
    z-index: 1;
    visibility: hidden;
}
.header-area.header-area2 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .img1 img {
    height: 100%;
    width: 100%;
    border-radius: 4px;
    transition: all 0.4s;
    border: 1px solid #e5e7eb;
}
.header-area.header-area2 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .homemenu-btn {
    position: absolute;
    top: 30%;
    z-index: 2;
    visibility: hidden;
    opacity: 0;
    text-align: center;
    transition: all 0.6s;
    margin: 0 auto;
    left: 18%;
    right: 20%;
}
.header-area.header-area2 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn1 {
    display: inline-block;
    padding: 16px;
    transition: all 0.4s;
    border-radius: 7px;
    position: relative;
    width: 150px;
    color: #fff;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: 16px; /* 100% */
    text-transform: capitalize;
    z-index: 1;
    background: #fc253f;
}
.header-area.header-area2 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn1 i {
    transform: rotate(-45deg);
    margin-left: 4px;
    transition: all 0.4s;
}
.header-area.header-area2 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn1:hover {
    color: #fff;
    transition: all 0.4s;
    transform: translateY(-5px);
    background: #fc253f;
}
.header-area.header-area2 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn1:hover i {
    transform: rotate(0);
    transition: all 0.4s;
}
.header-area.header-area2 .header-elements .main-menu-ex ul li ul {
    position: absolute;
    left: 0;
    top: 50px;
    border-radius: 4px;
    background: #fff;
    -webkit-backdrop-filter: blur(27px);
    backdrop-filter: blur(27px);
    width: 206px;
    box-shadow: rgba(0, 0, 0, 0.068) 0px 20px 30px;
    padding: 10px;
}
.header-area.header-area2 .header-elements .main-menu-ex ul li ul:hover > a {
    transition: all 0.4s;
    padding-left: 25px;
    color: red;
}
.header-area.header-area2 .header-elements .main-menu-ex ul li ul:hover > a::after {
    background: blue;
    transition: all 0.4s;
    visibility: visible;
    opacity: 1;
}
.header-area.header-area2 .header-elements .main-menu-ex ul li ul li ul {
    left: 190px;
    top: 10px;
}
.header-area.header-area2 .header-elements .main-menu-ex ul li ul li a {
    color: var(--qt-text-h-text3);
}
.header-area.header-area2 .header-elements .main-menu-ex ul li ul li a::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: -10px;
    width: 0px;
    height: 100%;
    color: var(--qt-text-h-text3);
    border-radius: 4px;
    transition: all 0.4s;
    z-index: -1;
    opacity: 1;
}
.header-area.header-area2 .header-elements .main-menu-ex ul li ul li a:hover {
    color: var(--vtc-bg-main-bg-2);
    transition: all 0.4s;
}
.header-area.header-area2 .header-elements .main-menu-ex ul li ul li a:hover::after {
    background-color: var(--vtc-bg-main-bg-2);
    border-radius: 4px;
    width: 5px;
    left: -10px;
    opacity: 1;
}
.header-area.header-area2 .header-elements .main-menu-ex ul li a {
    color: #fff;
    font-size: 16px;
    display: block;
    font-weight: 400;
    padding: 8px 15px;
    transition: all 0.3s;
    font-weight: 500;
}
.header-area.header-area2 .header-elements .main-menu-ex ul li a:hover {
    color: var(--vtc-bg-main-bg-2);
    transition: all 0.4s;
}
.header-area.header-area2 .header-elements .main-menu-ex ul li:hover .tp-submenu {
    visibility: visible;
    transition: all 0.5s ease-in-out;
    opacity: 1;
    z-index: 9;
    top: 150.3%;
    position: absolute;
    transition: all 0.4s;
    transform: scale(1);
}
.header-area.header-area2 .header-elements .main-menu-ex ul li:hover .mega-menu-all {
    opacity: 1;
    visibility: visible;
    transform: scale(1) translateY(-30px);
}

.header-area.header-area4 {
    background-color: var(--vtc-bg-main-bg-5);
}
.header-area.header-area4 .header-elements .main-menu-ex {
    padding: 2px 24px;
    position: relative;
}
.header-area.header-area4 .header-elements .main-menu-ex ul li .mega-menu-all {
    visibility: hidden;
    opacity: 0;
    box-shadow: rgba(0, 0, 0, 0.2) 0px 20px 30px;
    position: absolute;
    background: white;
    top: 201.3%;
    transform: scale(1, 0);
    z-index: 0;
    transition: all 0.4s;
    border-radius: 5px;
    padding: 36px 0;
    left: -400px;
    width: 1000px;
    min-height: 300px;
    overflow-x: hidden;
    overflow-y: hidden;
    transform-origin: top;
    display: block;
}
.header-area.header-area4 .header-elements .main-menu-ex ul li .mega-menu-all ul {
    box-shadow: none !important;
    padding: 0px 10px !important;
}
.header-area.header-area4 .header-elements .main-menu-ex ul li .mega-menu-all ul li a {
    display: inline-block;
    padding: 8px 40px;
}
.header-area.header-area4 .header-elements .main-menu-ex ul li .mega-menu-all .mega-menu-single {
    position: relative;
}
.header-area.header-area4 .header-elements .main-menu-ex ul li .mega-menu-all .mega-menu-single h3 {
    font-size: 20px;
    line-height: 20px;
    font-weight: 600;
    color: #081120;
    padding-left: 44px;
    position: relative;
}
.header-area.header-area4 .header-elements .main-menu-ex ul li .mega-menu-all .mega-menu-single h3::after {
    content: "";
    position: absolute;
    bottom: -14px;
    left: 43px;
    height: 2px;
    width: 25%;
    background-color: rgba(145, 145, 145, 0.2117647059);
}
.header-area.header-area4 .header-elements .main-menu-ex ul li .mega-menu-all .mega-menu-single::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    height: 400px;
    width: 2px;
    background-color: rgba(145, 145, 145, 0.2117647059);
}
.header-area.header-area4 .header-elements .main-menu-ex ul li .mega-menu-all .mega-menu-single.dis1::after {
    display: none;
}
.header-area.header-area4 .header-elements .main-menu-ex ul li .tp-submenu {
    visibility: hidden;
    opacity: 0;
    box-shadow: rgba(0, 0, 0, 0.2) 0px 20px 30px;
    position: absolute;
    background: white;
    top: 201.3%;
    transform: scale(1, 0);
    z-index: 0;
    transition: all 0.4s;
    border-radius: 5px;
    padding: 20px;
    left: -350px;
    width: 1300px;
    max-height: 600px;
    overflow-x: scroll;
    overflow-x: hidden;
    transform-origin: top;
    display: block;
}
.header-area.header-area4 .header-elements .main-menu-ex ul li .tp-submenu .all-images-menu {
    display: flex;
    align-items: center;
}
.header-area.header-area4 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb {
    transition: all 0.4s;
    position: relative;
    z-index: 1;
    margin: 0 20px 20px 0;
    text-align: center;
    overflow: hidden;
}
.header-area.header-area4 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .text {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    top: 10px;
    left: 0px;
}
.header-area.header-area4 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .text::after {
    position: absolute;
    content: "";
    height: 100px;
    width: 100px;
    left: -54px;
    top: -62px;
    transition: all 0.4s;
    background: #081120;
    transform: rotate(-45deg);
    z-index: -1;
}
.header-area.header-area4 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .text h2 {
    font-size: 20px;
    color: #fff;
    transform: rotate(-45deg);
    margin-left: 7px;
    margin-bottom: 13px;
    font-weight: 700;
}
.header-area.header-area4 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .bottom-heading {
    color: #08111a;
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: 18px; /* 100% */
    display: inline-block;
    margin-top: 8px;
}
.header-area.header-area4 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .bottom-heading:hover {
    color: #52b5e9;
    transition: all 0.4s;
}
.header-area.header-area4 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .bottom-text {
    color: #000;
    font-size: 24px;
    font-style: normal;
    font-weight: 700;
    line-height: 24px; /* 100% */
    letter-spacing: 0.18px;
    display: inline-block;
    padding-top: 20px;
}
.header-area.header-area4 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb:hover .img1::after {
    transform: scale(1);
    transition: all 0.4s;
    visibility: visible;
    opacity: 0.4;
}
.header-area.header-area4 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb:hover .homemenu-btn {
    top: 26%;
    visibility: visible;
    opacity: 1;
    transition: all 0.6s;
    margin-top: 4px;
}
.header-area.header-area4 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb:hover .bottom-text {
    color: var(--vtc-bg-main-bg-1);
    transition: all 0.4s;
}
.header-area.header-area4 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb.active .img1::after {
    transform: scale(1);
    transition: all 0.4s;
    visibility: visible;
    opacity: 0.3;
}
.header-area.header-area4 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb.active .img1 .coming {
    position: absolute;
    top: 50%;
    left: 50%;
    margin-left: -84px;
    margin-top: -30px;
    height: 60px;
    width: 168px;
    z-index: 99;
    border: none;
}
.header-area.header-area4 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .img1 {
    position: relative;
    z-index: 1;
    overflow: hidden;
    width: 234px;
    height: 300px;
}
.header-area.header-area4 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .img1::after {
    position: absolute;
    content: "";
    height: 100%;
    width: 100%;
    left: 0;
    top: 0;
    transition: all 0.4s;
    background: #000026;
    opacity: 0;
    border-radius: 4px;
    transform: scale(0.8);
    z-index: 1;
    visibility: hidden;
}
.header-area.header-area4 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .img1 img {
    height: 100%;
    width: 100%;
    border-radius: 4px;
    transition: all 0.4s;
    border: 1px solid #e5e7eb;
}
.header-area.header-area4 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .homemenu-btn {
    position: absolute;
    top: 30%;
    z-index: 2;
    visibility: hidden;
    opacity: 0;
    text-align: center;
    transition: all 0.6s;
    margin: 0 auto;
    left: 18%;
    right: 20%;
}
.header-area.header-area4 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn1 {
    display: inline-block;
    padding: 16px;
    transition: all 0.4s;
    border-radius: 7px;
    position: relative;
    width: 150px;
    color: #fff;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: 16px; /* 100% */
    text-transform: capitalize;
    z-index: 1;
    background: #52b5e9;
}
.header-area.header-area4 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn1 i {
    transform: rotate(-45deg);
    margin-left: 4px;
    transition: all 0.4s;
}
.header-area.header-area4 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn1:hover {
    color: #fff;
    transition: all 0.4s;
    transform: translateY(-5px);
    background: #52b5e9;
}
.header-area.header-area4 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn1:hover i {
    transform: rotate(0);
    transition: all 0.4s;
}
.header-area.header-area4 .header-elements .main-menu-ex ul li ul {
    position: absolute;
    left: 0;
    top: 50px;
    border-radius: 4px;
    background: #fff;
    -webkit-backdrop-filter: blur(27px);
    backdrop-filter: blur(27px);
    width: 206px;
    box-shadow: rgba(0, 0, 0, 0.068) 0px 20px 30px;
    padding: 10px;
}
.header-area.header-area4 .header-elements .main-menu-ex ul li ul:hover > a {
    transition: all 0.4s;
    padding-left: 25px;
    color: red;
}
.header-area.header-area4 .header-elements .main-menu-ex ul li ul:hover > a::after {
    background: blue;
    transition: all 0.4s;
    visibility: visible;
    opacity: 1;
}
.header-area.header-area4 .header-elements .main-menu-ex ul li ul li ul {
    left: 190px;
    top: 10px;
}
.header-area.header-area4 .header-elements .main-menu-ex ul li ul li a {
    color: var(--qt-text-h-text3);
}
.header-area.header-area4 .header-elements .main-menu-ex ul li ul li a::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: -10px;
    width: 0px;
    height: 100%;
    color: var(--qt-text-h-text3);
    border-radius: 4px;
    transition: all 0.4s;
    z-index: -1;
    opacity: 1;
}
.header-area.header-area4 .header-elements .main-menu-ex ul li ul li a:hover {
    color: var(--vtc-bg-main-bg-6);
    transition: all 0.4s;
}
.header-area.header-area4 .header-elements .main-menu-ex ul li ul li a:hover::after {
    background-color: var(--vtc-bg-main-bg-6);
    border-radius: 4px;
    width: 5px;
    left: -10px;
    opacity: 1;
}
.header-area.header-area4 .header-elements .main-menu-ex ul li a {
    color: #fff;
    font-size: 16px;
    display: block;
    font-weight: 400;
    padding: 8px 15px;
    transition: all 0.3s;
    font-weight: 500;
}
.header-area.header-area4 .header-elements .main-menu-ex ul li a:hover {
    color: var(--vtc-bg-main-bg-6);
    transition: all 0.4s;
}
.header-area.header-area4 .header-elements .main-menu-ex ul li:hover .tp-submenu {
    visibility: visible;
    transition: all 0.5s ease-in-out;
    opacity: 1;
    z-index: 9;
    top: 150.3%;
    position: absolute;
    transition: all 0.4s;
    transform: scale(1);
}
.header-area.header-area4 .header-elements .main-menu-ex ul li:hover .mega-menu-all {
    opacity: 1;
    visibility: visible;
    transform: scale(1) translateY(-30px);
}

.header-area.header-area5 {
    padding-top: 60px;
}
.header-area.header-area5 .header-elements {
    background-color: #d1d8e2;
    position: relative;
    z-index: 3;
    padding: 20px 32px;
    border-radius: 4px;
}
.header-area.header-area5 .header-elements .main-menu-ex {
    padding: 2px 24px;
    position: relative;
}
.header-area.header-area5 .header-elements .main-menu-ex ul li .mega-menu-all {
    visibility: hidden;
    opacity: 0;
    box-shadow: rgba(0, 0, 0, 0.2) 0px 20px 30px;
    position: absolute;
    background: white;
    top: 201.3%;
    transform: scale(1, 0);
    z-index: 0;
    transition: all 0.4s;
    border-radius: 5px;
    padding: 36px 0;
    left: -400px;
    width: 1000px;
    min-height: 300px;
    overflow-x: hidden;
    overflow-y: hidden;
    transform-origin: top;
    display: block;
}
.header-area.header-area5 .header-elements .main-menu-ex ul li .mega-menu-all ul {
    box-shadow: none !important;
    padding: 0px 10px !important;
}
.header-area.header-area5 .header-elements .main-menu-ex ul li .mega-menu-all ul li a {
    display: inline-block;
    padding: 8px 40px;
}
.header-area.header-area5 .header-elements .main-menu-ex ul li .mega-menu-all .mega-menu-single {
    position: relative;
}
.header-area.header-area5 .header-elements .main-menu-ex ul li .mega-menu-all .mega-menu-single h3 {
    font-size: 20px;
    line-height: 20px;
    font-weight: 600;
    color: #081120;
    padding-left: 44px;
    position: relative;
}
.header-area.header-area5 .header-elements .main-menu-ex ul li .mega-menu-all .mega-menu-single h3::after {
    content: "";
    position: absolute;
    bottom: -14px;
    left: 43px;
    height: 2px;
    width: 25%;
    background-color: rgba(145, 145, 145, 0.2117647059);
}
.header-area.header-area5 .header-elements .main-menu-ex ul li .mega-menu-all .mega-menu-single::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    height: 400px;
    width: 2px;
    background-color: rgba(145, 145, 145, 0.2117647059);
}
.header-area.header-area5 .header-elements .main-menu-ex ul li .mega-menu-all .mega-menu-single.dis1::after {
    display: none;
}
.header-area.header-area5 .header-elements .main-menu-ex ul li .tp-submenu {
    visibility: hidden;
    opacity: 0;
    box-shadow: rgba(0, 0, 0, 0.2) 0px 20px 30px;
    position: absolute;
    background: white;
    top: 201.3%;
    transform: scale(1, 0);
    z-index: 0;
    transition: all 0.4s;
    border-radius: 5px;
    padding: 20px;
    left: -347px;
    width: 1300px;
    max-height: 600px;
    overflow-x: scroll;
    overflow-x: hidden;
    transform-origin: top;
    display: block;
}
.header-area.header-area5 .header-elements .main-menu-ex ul li .tp-submenu .all-images-menu {
    display: flex;
    align-items: center;
}
.header-area.header-area5 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb {
    transition: all 0.4s;
    position: relative;
    z-index: 1;
    margin: 0 20px 20px 0;
    text-align: center;
    overflow: hidden;
}
.header-area.header-area5 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .text {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    top: 10px;
    left: 0px;
}
.header-area.header-area5 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .text::after {
    position: absolute;
    content: "";
    height: 100px;
    width: 100px;
    left: -54px;
    top: -62px;
    transition: all 0.4s;
    background: #081120;
    transform: rotate(-45deg);
    z-index: -1;
}
.header-area.header-area5 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .text h2 {
    font-size: 20px;
    color: #fff;
    transform: rotate(-45deg);
    margin-left: 7px;
    margin-bottom: 13px;
    font-weight: 700;
}
.header-area.header-area5 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .bottom-heading {
    color: #08111a;
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: 18px; /* 100% */
    display: inline-block;
    margin-top: 8px;
}
.header-area.header-area5 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .bottom-heading:hover {
    color: #fc253f;
    transition: all 0.4s;
}
.header-area.header-area5 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .bottom-text {
    color: #000;
    font-size: 24px;
    font-style: normal;
    font-weight: 700;
    line-height: 24px; /* 100% */
    letter-spacing: 0.18px;
    display: inline-block;
    padding-top: 20px;
}
.header-area.header-area5 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb:hover .img1::after {
    transform: scale(1);
    transition: all 0.4s;
    visibility: visible;
    opacity: 0.4;
}
.header-area.header-area5 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb:hover .homemenu-btn {
    top: 26%;
    visibility: visible;
    opacity: 1;
    transition: all 0.6s;
    margin-top: 4px;
}
.header-area.header-area5 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb:hover .bottom-text {
    color: var(--vtc-bg-main-bg-1);
    transition: all 0.4s;
}
.header-area.header-area5 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb.active .img1::after {
    transform: scale(1);
    transition: all 0.4s;
    visibility: visible;
    opacity: 0.3;
}
.header-area.header-area5 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb.active .img1 .coming {
    position: absolute;
    top: 50%;
    left: 50%;
    margin-left: -84px;
    margin-top: -30px;
    height: 60px;
    width: 168px;
    z-index: 99;
    border: none;
}
.header-area.header-area5 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .img1 {
    position: relative;
    z-index: 1;
    overflow: hidden;
    width: 234px;
    height: 300px;
}
.header-area.header-area5 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .img1::after {
    position: absolute;
    content: "";
    height: 100%;
    width: 100%;
    left: 0;
    top: 0;
    transition: all 0.4s;
    background: #000026;
    opacity: 0;
    border-radius: 4px;
    transform: scale(0.8);
    z-index: 1;
    visibility: hidden;
}
.header-area.header-area5 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .img1 img {
    height: 100%;
    width: 100%;
    border-radius: 4px;
    transition: all 0.4s;
    border: 1px solid #e5e7eb;
}
.header-area.header-area5 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .homemenu-btn {
    position: absolute;
    top: 30%;
    z-index: 2;
    visibility: hidden;
    opacity: 0;
    text-align: center;
    transition: all 0.6s;
    margin: 0 auto;
    left: 18%;
    right: 20%;
}
.header-area.header-area5 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn1 {
    display: inline-block;
    padding: 16px;
    transition: all 0.4s;
    border-radius: 7px;
    position: relative;
    width: 150px;
    color: #fff;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: 16px; /* 100% */
    text-transform: capitalize;
    z-index: 1;
    background: #fc253f;
}
.header-area.header-area5 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn1 i {
    transform: rotate(-45deg);
    margin-left: 4px;
    transition: all 0.4s;
}
.header-area.header-area5 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn1:hover {
    color: #fff;
    transition: all 0.4s;
    transform: translateY(-5px);
    background: #fc253f;
}
.header-area.header-area5 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn1:hover i {
    transform: rotate(0);
    transition: all 0.4s;
}
.header-area.header-area5 .header-elements .main-menu-ex ul li ul {
    position: absolute;
    left: 0;
    top: 50px;
    border-radius: 4px;
    background: #fff;
    -webkit-backdrop-filter: blur(27px);
    backdrop-filter: blur(27px);
    width: 206px;
    box-shadow: rgba(0, 0, 0, 0.068) 0px 20px 30px;
    padding: 10px;
}
.header-area.header-area5 .header-elements .main-menu-ex ul li ul:hover > a {
    transition: all 0.4s;
    padding-left: 25px;
    color: red;
}
.header-area.header-area5 .header-elements .main-menu-ex ul li ul:hover > a::after {
    background: blue;
    transition: all 0.4s;
    visibility: visible;
    opacity: 1;
}
.header-area.header-area5 .header-elements .main-menu-ex ul li ul li ul {
    left: 190px;
    top: 10px;
}
.header-area.header-area5 .header-elements .main-menu-ex ul li ul li a {
    color: var(--qt-text-h-text3);
}
.header-area.header-area5 .header-elements .main-menu-ex ul li ul li a::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: -10px;
    width: 0px;
    height: 100%;
    color: var(--qt-text-h-text3);
    border-radius: 4px;
    transition: all 0.4s;
    z-index: -1;
    opacity: 1;
}
.header-area.header-area5 .header-elements .main-menu-ex ul li ul li a:hover {
    color: var(--vtc-bg-main-bg-2);
    transition: all 0.4s;
}
.header-area.header-area5 .header-elements .main-menu-ex ul li ul li a:hover::after {
    background-color: var(--vtc-bg-main-bg-2);
    border-radius: 4px;
    width: 5px;
    left: -10px;
    opacity: 1;
}
.header-area.header-area5 .header-elements .main-menu-ex ul li a {
    color: var(--vtc-text-heading-text-1);
    font-size: 16px;
    display: block;
    font-weight: 500;
    padding: 8px 15px;
    transition: all 0.3s;
}
.header-area.header-area5 .header-elements .main-menu-ex ul li a:hover {
    color: var(--vtc-bg-main-bg-2);
    transition: all 0.4s;
}
.header-area.header-area5 .header-elements .main-menu-ex ul li:hover .tp-submenu {
    visibility: visible;
    transition: all 0.5s ease-in-out;
    opacity: 1;
    z-index: 9;
    top: 150.3%;
    position: absolute;
    transition: all 0.4s;
    transform: scale(1);
}
.header-area.header-area5 .header-elements .main-menu-ex ul li:hover .mega-menu-all {
    opacity: 1;
    visibility: visible;
    transform: scale(1) translateY(-30px);
}

.header-area.header-area6 {
    padding-top: 10px;
}
.header-area.header-area6 .header-elements {
    background-color: #ffffff;
    position: relative;
    z-index: 3;
    padding: 20px 32px;
    border-radius: 4px;
}
.header-area.header-area6 .header-elements .main-menu-ex {
    padding: 2px 24px;
    position: relative;
}
.header-area.header-area6 .header-elements .main-menu-ex ul li .mega-menu-all {
    visibility: hidden;
    opacity: 0;
    box-shadow: rgba(0, 0, 0, 0.2) 0px 20px 30px;
    position: absolute;
    background: white;
    top: 201.3%;
    transform: scale(1, 0);
    z-index: 0;
    transition: all 0.4s;
    border-radius: 5px;
    padding: 36px 0;
    left: -400px;
    width: 1000px;
    min-height: 300px;
    overflow-x: hidden;
    overflow-y: hidden;
    transform-origin: top;
    display: block;
}
.header-area.header-area6 .header-elements .main-menu-ex ul li .mega-menu-all ul {
    box-shadow: none !important;
    padding: 0px 10px !important;
}
.header-area.header-area6 .header-elements .main-menu-ex ul li .mega-menu-all ul li a {
    display: inline-block;
    padding: 8px 40px;
}
.header-area.header-area6 .header-elements .main-menu-ex ul li .mega-menu-all .mega-menu-single {
    position: relative;
}
.header-area.header-area6 .header-elements .main-menu-ex ul li .mega-menu-all .mega-menu-single h3 {
    font-size: 20px;
    line-height: 20px;
    font-weight: 600;
    color: #081120;
    padding-left: 44px;
    position: relative;
}
.header-area.header-area6 .header-elements .main-menu-ex ul li .mega-menu-all .mega-menu-single h3::after {
    content: "";
    position: absolute;
    bottom: -14px;
    left: 43px;
    height: 2px;
    width: 25%;
    background-color: rgba(145, 145, 145, 0.2117647059);
}
.header-area.header-area6 .header-elements .main-menu-ex ul li .mega-menu-all .mega-menu-single::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    height: 400px;
    width: 2px;
    background-color: rgba(145, 145, 145, 0.2117647059);
}
.header-area.header-area6 .header-elements .main-menu-ex ul li .mega-menu-all .mega-menu-single.dis1::after {
    display: none;
}
.header-area.header-area6 .header-elements .main-menu-ex ul li .tp-submenu {
    visibility: hidden;
    opacity: 0;
    box-shadow: rgba(0, 0, 0, 0.2) 0px 20px 30px;
    position: absolute;
    background: white;
    top: 201.3%;
    transform: scale(1, 0);
    z-index: 0;
    transition: all 0.4s;
    border-radius: 5px;
    padding: 20px;
    left: -347px;
    width: 1300px;
    max-height: 600px;
    overflow-x: scroll;
    overflow-x: hidden;
    transform-origin: top;
    display: block;
}
.header-area.header-area6 .header-elements .main-menu-ex ul li .tp-submenu .all-images-menu {
    display: flex;
    align-items: center;
}
.header-area.header-area6 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb {
    transition: all 0.4s;
    position: relative;
    z-index: 1;
    margin: 0 20px 20px 0;
    text-align: center;
    overflow: hidden;
}
.header-area.header-area6 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .text {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    top: 10px;
    left: 0px;
}
.header-area.header-area6 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .text::after {
    position: absolute;
    content: "";
    height: 100px;
    width: 100px;
    left: -54px;
    top: -62px;
    transition: all 0.4s;
    background: #081120;
    transform: rotate(-45deg);
    z-index: -1;
}
.header-area.header-area6 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .text h2 {
    font-size: 20px;
    color: #fff;
    transform: rotate(-45deg);
    margin-left: 7px;
    margin-bottom: 13px;
    font-weight: 700;
}
.header-area.header-area6 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .bottom-heading {
    color: #08111a;
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: 18px; /* 100% */
    display: inline-block;
    margin-top: 8px;
}
.header-area.header-area6 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .bottom-heading:hover {
    color: #fc253f;
    transition: all 0.4s;
}
.header-area.header-area6 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .bottom-heading {
    color: #000;
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: 18px;
    display: inline-block;
    margin-top: 8px;
}
.header-area.header-area6 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .bottom-heading:hover {
    color: #f1c832;
}
.header-area.header-area6 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb:hover .img1::after {
    transform: scale(1);
    transition: all 0.4s;
    visibility: visible;
    opacity: 0.4;
}
.header-area.header-area6 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb:hover .homemenu-btn {
    top: 26%;
    visibility: visible;
    opacity: 1;
    transition: all 0.6s;
    margin-top: 4px;
}
.header-area.header-area6 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb:hover .bottom-text {
    color: var(--vtc-bg-main-bg-1);
    transition: all 0.4s;
}
.header-area.header-area6 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb.active .img1::after {
    transform: scale(1);
    transition: all 0.4s;
    visibility: visible;
    opacity: 0.3;
}
.header-area.header-area6 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb.active .img1 .coming {
    position: absolute;
    top: 50%;
    left: 50%;
    margin-left: -84px;
    margin-top: -30px;
    height: 60px;
    width: 168px;
    z-index: 99;
    border: none;
}
.header-area.header-area6 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .img1 {
    position: relative;
    z-index: 1;
    overflow: hidden;
    width: 234px;
    height: 300px;
}
.header-area.header-area6 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .img1::after {
    position: absolute;
    content: "";
    height: 100%;
    width: 100%;
    left: 0;
    top: 0;
    transition: all 0.4s;
    background: #000026;
    opacity: 0;
    border-radius: 4px;
    transform: scale(0.8);
    z-index: 1;
    visibility: hidden;
}
.header-area.header-area6 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .img1 img {
    height: 100%;
    width: 100%;
    border-radius: 4px;
    transition: all 0.4s;
    border: 1px solid #e5e7eb;
}
.header-area.header-area6 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .homemenu-btn {
    position: absolute;
    top: 30%;
    z-index: 2;
    visibility: hidden;
    opacity: 0;
    text-align: center;
    transition: all 0.6s;
    margin: 0 auto;
    left: 18%;
    right: 20%;
}
.header-area.header-area6 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn1 {
    display: inline-block;
    padding: 16px;
    transition: all 0.4s;
    border-radius: 7px;
    position: relative;
    width: 150px;
    color: #081120;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: 16px; /* 100% */
    text-transform: capitalize;
    z-index: 1;
    background: #f1c832;
}
.header-area.header-area6 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn1 i {
    transform: rotate(-45deg);
    margin-left: 4px;
    transition: all 0.4s;
}
.header-area.header-area6 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn1:hover {
    color: #081120;
    transition: all 0.4s;
    transform: translateY(-5px);
    background: #f1c832;
}
.header-area.header-area6 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn1:hover i {
    transform: rotate(0);
    transition: all 0.4s;
}
.header-area.header-area6 .header-elements .main-menu-ex ul li ul {
    position: absolute;
    left: 0;
    top: 50px;
    border-radius: 4px;
    background: #fff;
    -webkit-backdrop-filter: blur(27px);
    backdrop-filter: blur(27px);
    width: 206px;
    box-shadow: rgba(0, 0, 0, 0.068) 0px 20px 30px;
    padding: 10px;
}
.header-area.header-area6 .header-elements .main-menu-ex ul li ul:hover > a {
    transition: all 0.4s;
    padding-left: 25px;
    color: red;
}
.header-area.header-area6 .header-elements .main-menu-ex ul li ul:hover > a::after {
    background: blue;
    transition: all 0.4s;
    visibility: visible;
    opacity: 1;
}
.header-area.header-area6 .header-elements .main-menu-ex ul li ul li ul {
    left: 190px;
    top: 10px;
}
.header-area.header-area6 .header-elements .main-menu-ex ul li ul li a {
    color: var(--qt-text-h-text3);
}
.header-area.header-area6 .header-elements .main-menu-ex ul li ul li a::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: -10px;
    width: 0px;
    height: 100%;
    color: var(--qt-text-h-text3);
    border-radius: 4px;
    transition: all 0.4s;
    z-index: -1;
    opacity: 1;
}
.header-area.header-area6 .header-elements .main-menu-ex ul li ul li a:hover {
    color: #31572c;
    transition: all 0.4s;
}
.header-area.header-area6 .header-elements .main-menu-ex ul li ul li a:hover::after {
    background-color: #31572c;
    border-radius: 4px;
    width: 5px;
    left: -10px;
    opacity: 1;
}
.header-area.header-area6 .header-elements .main-menu-ex ul li a {
    color: var(--vtc-text-heading-text-1);
    font-size: 16px;
    display: block;
    font-weight: 500;
    padding: 8px 15px;
    transition: all 0.3s;
}
.header-area.header-area6 .header-elements .main-menu-ex ul li a:hover {
    color: #31572c;
    transition: all 0.4s;
}
.header-area.header-area6 .header-elements .main-menu-ex ul li:hover .tp-submenu {
    visibility: visible;
    transition: all 0.5s ease-in-out;
    opacity: 1;
    z-index: 9;
    top: 150.3%;
    position: absolute;
    transition: all 0.4s;
    transform: scale(1);
}
.header-area.header-area6 .header-elements .main-menu-ex ul li:hover .mega-menu-all {
    opacity: 1;
    visibility: visible;
    transform: scale(1) translateY(-30px);
}

.header-area.header-area7 {
    padding-top: 10px;
}
.header-area.header-area7 .header-elements {
    background-color: #ffffff;
    position: relative;
    z-index: 3;
    padding: 20px 32px;
    border-radius: 8px;
}
.header-area.header-area7 .header-elements .main-menu-ex {
    padding: 2px 24px;
    position: relative;
}
.header-area.header-area7 .header-elements .main-menu-ex ul li .mega-menu-all {
    visibility: hidden;
    opacity: 0;
    box-shadow: rgba(0, 0, 0, 0.2) 0px 20px 30px;
    position: absolute;
    background: white;
    top: 201.3%;
    transform: scale(1, 0);
    z-index: 0;
    transition: all 0.4s;
    border-radius: 5px;
    padding: 36px 0;
    left: -400px;
    width: 1000px;
    min-height: 300px;
    overflow-x: hidden;
    overflow-y: hidden;
    transform-origin: top;
    display: block;
}
.header-area.header-area7 .header-elements .main-menu-ex ul li .mega-menu-all ul {
    box-shadow: none !important;
    padding: 0px 10px !important;
}
.header-area.header-area7 .header-elements .main-menu-ex ul li .mega-menu-all ul li a {
    display: inline-block;
    padding: 8px 40px;
}
.header-area.header-area7 .header-elements .main-menu-ex ul li .mega-menu-all .mega-menu-single {
    position: relative;
}
.header-area.header-area7 .header-elements .main-menu-ex ul li .mega-menu-all .mega-menu-single h3 {
    font-size: 20px;
    line-height: 20px;
    font-weight: 600;
    color: #081120;
    padding-left: 44px;
    position: relative;
}
.header-area.header-area7 .header-elements .main-menu-ex ul li .mega-menu-all .mega-menu-single h3::after {
    content: "";
    position: absolute;
    bottom: -14px;
    left: 43px;
    height: 2px;
    width: 25%;
    background-color: rgba(145, 145, 145, 0.2117647059);
}
.header-area.header-area7 .header-elements .main-menu-ex ul li .mega-menu-all .mega-menu-single::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    height: 400px;
    width: 2px;
    background-color: rgba(145, 145, 145, 0.2117647059);
}
.header-area.header-area7 .header-elements .main-menu-ex ul li .mega-menu-all .mega-menu-single.dis1::after {
    display: none;
}
.header-area.header-area7 .header-elements .main-menu-ex ul li .tp-submenu {
    visibility: hidden;
    opacity: 0;
    box-shadow: rgba(0, 0, 0, 0.2) 0px 20px 30px;
    position: absolute;
    background: white;
    top: 201.3%;
    transform: scale(1, 0);
    z-index: 0;
    transition: all 0.4s;
    border-radius: 5px;
    padding: 20px;
    left: -347px;
    width: 1300px;
    max-height: 600px;
    overflow-x: scroll;
    overflow-x: hidden;
    transform-origin: top;
    display: block;
}
.header-area.header-area7 .header-elements .main-menu-ex ul li .tp-submenu .all-images-menu {
    display: flex;
    align-items: center;
}
.header-area.header-area7 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb {
    transition: all 0.4s;
    position: relative;
    z-index: 1;
    margin: 0 20px 20px 0;
    text-align: center;
    overflow: hidden;
}
.header-area.header-area7 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .text {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    top: 10px;
    left: 0px;
}
.header-area.header-area7 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .text::after {
    position: absolute;
    content: "";
    height: 100px;
    width: 100px;
    left: -54px;
    top: -62px;
    transition: all 0.4s;
    background: #081120;
    transform: rotate(-45deg);
    z-index: -1;
}
.header-area.header-area7 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .text h2 {
    font-size: 20px;
    color: #fff;
    transform: rotate(-45deg);
    margin-left: 7px;
    margin-bottom: 13px;
    font-weight: 700;
}
.header-area.header-area7 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .bottom-heading {
    color: #08111a;
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: 18px; /* 100% */
    display: inline-block;
    margin-top: 8px;
}
.header-area.header-area7 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .bottom-heading:hover {
    color: #5e53e1;
    transition: all 0.4s;
}
.header-area.header-area7 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .bottom-text {
    color: #000;
    font-size: 24px;
    font-style: normal;
    font-weight: 700;
    line-height: 24px; /* 100% */
    letter-spacing: 0.18px;
    display: inline-block;
    padding-top: 20px;
}
.header-area.header-area7 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb:hover .img1::after {
    transform: scale(1);
    transition: all 0.4s;
    visibility: visible;
    opacity: 0.4;
}
.header-area.header-area7 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb:hover .homemenu-btn {
    top: 26%;
    visibility: visible;
    opacity: 1;
    transition: all 0.6s;
    margin-top: 4px;
}
.header-area.header-area7 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb:hover .bottom-text {
    color: var(--vtc-bg-main-bg-1);
    transition: all 0.4s;
}
.header-area.header-area7 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb.active .img1::after {
    transform: scale(1);
    transition: all 0.4s;
    visibility: visible;
    opacity: 0.3;
}
.header-area.header-area7 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb.active .img1 .coming {
    position: absolute;
    top: 50%;
    left: 50%;
    margin-left: -84px;
    margin-top: -30px;
    height: 60px;
    width: 168px;
    z-index: 99;
    border: none;
}
.header-area.header-area7 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .img1 {
    position: relative;
    z-index: 1;
    overflow: hidden;
    width: 234px;
    height: 300px;
}
.header-area.header-area7 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .img1::after {
    position: absolute;
    content: "";
    height: 100%;
    width: 100%;
    left: 0;
    top: 0;
    transition: all 0.4s;
    background: #000026;
    opacity: 0;
    border-radius: 4px;
    transform: scale(0.8);
    z-index: 1;
    visibility: hidden;
}
.header-area.header-area7 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .img1 img {
    height: 100%;
    width: 100%;
    border-radius: 4px;
    transition: all 0.4s;
    border: 1px solid #e5e7eb;
}
.header-area.header-area7 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .homemenu-btn {
    position: absolute;
    top: 30%;
    z-index: 2;
    visibility: hidden;
    opacity: 0;
    text-align: center;
    transition: all 0.6s;
    margin: 0 auto;
    left: 18%;
    right: 20%;
}
.header-area.header-area7 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn1 {
    display: inline-block;
    padding: 16px;
    transition: all 0.4s;
    border-radius: 7px;
    position: relative;
    width: 150px;
    color: #fff;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: 16px; /* 100% */
    text-transform: capitalize;
    z-index: 1;
    background: #5957e5;
}
.header-area.header-area7 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn1 i {
    transform: rotate(-45deg);
    margin-left: 4px;
    transition: all 0.4s;
}
.header-area.header-area7 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn1:hover {
    color: #fff;
    transition: all 0.4s;
    transform: translateY(-5px);
    background: #5957e5;
}
.header-area.header-area7 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn1:hover i {
    transform: rotate(0);
    transition: all 0.4s;
}
.header-area.header-area7 .header-elements .main-menu-ex ul li ul {
    position: absolute;
    left: 0;
    top: 50px;
    border-radius: 4px;
    background: #fff;
    -webkit-backdrop-filter: blur(27px);
    backdrop-filter: blur(27px);
    width: 206px;
    box-shadow: rgba(0, 0, 0, 0.068) 0px 20px 30px;
    padding: 10px;
}
.header-area.header-area7 .header-elements .main-menu-ex ul li ul:hover > a {
    transition: all 0.4s;
    padding-left: 25px;
    color: red;
}
.header-area.header-area7 .header-elements .main-menu-ex ul li ul:hover > a::after {
    background: blue;
    transition: all 0.4s;
    visibility: visible;
    opacity: 1;
}
.header-area.header-area7 .header-elements .main-menu-ex ul li ul li ul {
    left: 190px;
    top: 10px;
}
.header-area.header-area7 .header-elements .main-menu-ex ul li ul li a {
    color: var(--qt-text-h-text3);
}
.header-area.header-area7 .header-elements .main-menu-ex ul li ul li a::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: -10px;
    width: 0px;
    height: 100%;
    color: var(--qt-text-h-text3);
    border-radius: 4px;
    transition: all 0.4s;
    z-index: -1;
    opacity: 1;
}
.header-area.header-area7 .header-elements .main-menu-ex ul li ul li a:hover {
    color: #5957e5;
    transition: all 0.4s;
}
.header-area.header-area7 .header-elements .main-menu-ex ul li ul li a:hover::after {
    background-color: #5957e5;
    border-radius: 4px;
    width: 5px;
    left: -10px;
    opacity: 1;
}
.header-area.header-area7 .header-elements .main-menu-ex ul li a {
    color: var(--vtc-text-heading-text-1);
    font-size: 16px;
    display: block;
    font-weight: 500;
    padding: 8px 15px;
    transition: all 0.3s;
}
.header-area.header-area7 .header-elements .main-menu-ex ul li a:hover {
    color: #5957e5;
    transition: all 0.4s;
}
.header-area.header-area7 .header-elements .main-menu-ex ul li:hover .tp-submenu {
    visibility: visible;
    transition: all 0.5s ease-in-out;
    opacity: 1;
    z-index: 9;
    top: 150.3%;
    position: absolute;
    transition: all 0.4s;
    transform: scale(1);
}
.header-area.header-area7 .header-elements .main-menu-ex ul li:hover .mega-menu-all {
    opacity: 1;
    visibility: visible;
    transform: scale(1) translateY(-30px);
}

.header-area.header-area8 {
    padding-top: 10px;
}
.header-area.header-area8 .header-elements {
    background-color: #ffffff;
    position: relative;
    z-index: 3;
    padding: 20px 32px;
    border-radius: 8px;
}
.header-area.header-area8 .header-elements .main-menu-ex {
    padding: 2px 24px;
    position: relative;
}
.header-area.header-area8 .header-elements .main-menu-ex ul li .mega-menu-all {
    visibility: hidden;
    opacity: 0;
    box-shadow: rgba(0, 0, 0, 0.2) 0px 20px 30px;
    position: absolute;
    background: white;
    top: 201.3%;
    transform: scale(1, 0);
    z-index: 0;
    transition: all 0.4s;
    border-radius: 5px;
    padding: 36px 0;
    left: -400px;
    width: 1000px;
    min-height: 300px;
    overflow-x: hidden;
    overflow-y: hidden;
    transform-origin: top;
    display: block;
}
.header-area.header-area8 .header-elements .main-menu-ex ul li .mega-menu-all ul {
    box-shadow: none !important;
    padding: 0px 10px !important;
}
.header-area.header-area8 .header-elements .main-menu-ex ul li .mega-menu-all ul li a {
    display: inline-block;
    padding: 8px 40px;
}
.header-area.header-area8 .header-elements .main-menu-ex ul li .mega-menu-all .mega-menu-single {
    position: relative;
}
.header-area.header-area8 .header-elements .main-menu-ex ul li .mega-menu-all .mega-menu-single h3 {
    font-size: 20px;
    line-height: 20px;
    font-weight: 600;
    color: #081120;
    padding-left: 44px;
    position: relative;
}
.header-area.header-area8 .header-elements .main-menu-ex ul li .mega-menu-all .mega-menu-single h3::after {
    content: "";
    position: absolute;
    bottom: -14px;
    left: 43px;
    height: 2px;
    width: 25%;
    background-color: rgba(145, 145, 145, 0.2117647059);
}
.header-area.header-area8 .header-elements .main-menu-ex ul li .mega-menu-all .mega-menu-single::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    height: 400px;
    width: 2px;
    background-color: rgba(145, 145, 145, 0.2117647059);
}
.header-area.header-area8 .header-elements .main-menu-ex ul li .mega-menu-all .mega-menu-single.dis1::after {
    display: none;
}
.header-area.header-area8 .header-elements .main-menu-ex ul li .tp-submenu {
    visibility: hidden;
    opacity: 0;
    box-shadow: rgba(0, 0, 0, 0.2) 0px 20px 30px;
    position: absolute;
    background: white;
    top: 201.3%;
    transform: scale(1, 0);
    z-index: 0;
    transition: all 0.4s;
    border-radius: 5px;
    padding: 20px;
    left: -347px;
    width: 1300px;
    max-height: 600px;
    overflow-x: scroll;
    overflow-x: hidden;
    transform-origin: top;
    display: block;
}
.header-area.header-area8 .header-elements .main-menu-ex ul li .tp-submenu .all-images-menu {
    display: flex;
    align-items: center;
}
.header-area.header-area8 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb {
    transition: all 0.4s;
    position: relative;
    z-index: 1;
    margin: 0 20px 20px 0;
    text-align: center;
    overflow: hidden;
}
.header-area.header-area8 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .text {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    top: 10px;
    left: 0px;
}
.header-area.header-area8 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .text::after {
    position: absolute;
    content: "";
    height: 100px;
    width: 100px;
    left: -54px;
    top: -62px;
    transition: all 0.4s;
    background: #081120;
    transform: rotate(-45deg);
    z-index: -1;
}
.header-area.header-area8 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .text h2 {
    font-size: 20px;
    color: #fff;
    transform: rotate(-45deg);
    margin-left: 7px;
    margin-bottom: 13px;
    font-weight: 700;
}
.header-area.header-area8 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .bottom-heading {
    color: #08111a;
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: 18px; /* 100% */
    display: inline-block;
    margin-top: 8px;
}
.header-area.header-area8 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .bottom-heading:hover {
    color: #fc253f;
    transition: all 0.4s;
}
.header-area.header-area8 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .bottom-text {
    color: #000;
    font-size: 24px;
    font-style: normal;
    font-weight: 700;
    line-height: 24px; /* 100% */
    letter-spacing: 0.18px;
    display: inline-block;
    padding-top: 20px;
}
.header-area.header-area8 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb:hover .img1::after {
    transform: scale(1);
    transition: all 0.4s;
    visibility: visible;
    opacity: 0.4;
}
.header-area.header-area8 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb:hover .homemenu-btn {
    top: 26%;
    visibility: visible;
    opacity: 1;
    transition: all 0.6s;
    margin-top: 4px;
}
.header-area.header-area8 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb:hover .bottom-text {
    color: var(--vtc-bg-main-bg-1);
    transition: all 0.4s;
}
.header-area.header-area8 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb.active .img1::after {
    transform: scale(1);
    transition: all 0.4s;
    visibility: visible;
    opacity: 0.3;
}
.header-area.header-area8 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb.active .img1 .coming {
    position: absolute;
    top: 50%;
    left: 50%;
    margin-left: -84px;
    margin-top: -30px;
    height: 60px;
    width: 168px;
    z-index: 99;
    border: none;
}
.header-area.header-area8 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .img1 {
    position: relative;
    z-index: 1;
    overflow: hidden;
    width: 234px;
    height: 300px;
}
.header-area.header-area8 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .img1::after {
    position: absolute;
    content: "";
    height: 100%;
    width: 100%;
    left: 0;
    top: 0;
    transition: all 0.4s;
    background: #000026;
    opacity: 0;
    border-radius: 4px;
    transform: scale(0.8);
    z-index: 1;
    visibility: hidden;
}
.header-area.header-area8 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .img1 img {
    height: 100%;
    width: 100%;
    border-radius: 4px;
    transition: all 0.4s;
    border: 1px solid #e5e7eb;
}
.header-area.header-area8 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .homemenu-btn {
    position: absolute;
    top: 30%;
    z-index: 2;
    visibility: hidden;
    opacity: 0;
    text-align: center;
    transition: all 0.6s;
    margin: 0 auto;
    left: 18%;
    right: 20%;
}
.header-area.header-area8 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn1 {
    display: inline-block;
    padding: 16px;
    transition: all 0.4s;
    border-radius: 7px;
    position: relative;
    width: 150px;
    color: #081120;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: 16px; /* 100% */
    text-transform: capitalize;
    z-index: 1;
    background: #f6aa32;
}
.header-area.header-area8 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn1 i {
    transform: rotate(-45deg);
    margin-left: 4px;
    transition: all 0.4s;
}
.header-area.header-area8 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn1:hover {
    color: #081120;
    transition: all 0.4s;
    transform: translateY(-5px);
    background: #f6aa32;
}
.header-area.header-area8 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn1:hover i {
    transform: rotate(0);
    transition: all 0.4s;
}
.header-area.header-area8 .header-elements .main-menu-ex ul li ul {
    position: absolute;
    left: 0;
    top: 50px;
    border-radius: 4px;
    background: #fff;
    -webkit-backdrop-filter: blur(27px);
    backdrop-filter: blur(27px);
    width: 206px;
    box-shadow: rgba(0, 0, 0, 0.068) 0px 20px 30px;
    padding: 10px;
}
.header-area.header-area8 .header-elements .main-menu-ex ul li ul:hover > a {
    transition: all 0.4s;
    padding-left: 25px;
    color: red;
}
.header-area.header-area8 .header-elements .main-menu-ex ul li ul:hover > a::after {
    background: blue;
    transition: all 0.4s;
    visibility: visible;
    opacity: 1;
}
.header-area.header-area8 .header-elements .main-menu-ex ul li ul li ul {
    left: 190px;
    top: 10px;
}
.header-area.header-area8 .header-elements .main-menu-ex ul li ul li a {
    color: var(--qt-text-h-text3);
}
.header-area.header-area8 .header-elements .main-menu-ex ul li ul li a::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: -10px;
    width: 0px;
    height: 100%;
    color: var(--qt-text-h-text3);
    border-radius: 4px;
    transition: all 0.4s;
    z-index: -1;
    opacity: 1;
}
.header-area.header-area8 .header-elements .main-menu-ex ul li ul li a:hover {
    color: #f6aa32;
    transition: all 0.4s;
}
.header-area.header-area8 .header-elements .main-menu-ex ul li ul li a:hover::after {
    background-color: #f6aa32;
    border-radius: 4px;
    width: 5px;
    left: -10px;
    opacity: 1;
}
.header-area.header-area8 .header-elements .main-menu-ex ul li a {
    color: var(--vtc-text-heading-text-1);
    font-size: 16px;
    display: block;
    font-weight: 500;
    padding: 8px 15px;
    transition: all 0.3s;
}
.header-area.header-area8 .header-elements .main-menu-ex ul li a:hover {
    color: #f6aa32;
    transition: all 0.4s;
}
.header-area.header-area8 .header-elements .main-menu-ex ul li:hover .tp-submenu {
    visibility: visible;
    transition: all 0.5s ease-in-out;
    opacity: 1;
    z-index: 9;
    top: 150.3%;
    position: absolute;
    transition: all 0.4s;
    transform: scale(1);
}
.header-area.header-area8 .header-elements .main-menu-ex ul li:hover .mega-menu-all {
    opacity: 1;
    visibility: visible;
    transform: scale(1) translateY(-30px);
}

.header-area.header-area9 {
    padding-top: 10px;
}
.header-area.header-area9 .header-elements {
    background-color: #f3f5f2;
    position: relative;
    z-index: 3;
    padding: 20px 32px;
    border-radius: 8px;
}
.header-area.header-area9 .header-elements .main-menu-ex {
    padding: 2px 24px;
    position: relative;
}
.header-area.header-area9 .header-elements .main-menu-ex ul li .mega-menu-all {
    visibility: hidden;
    opacity: 0;
    box-shadow: rgba(0, 0, 0, 0.2) 0px 20px 30px;
    position: absolute;
    background: white;
    top: 201.3%;
    transform: scale(1, 0);
    z-index: 0;
    transition: all 0.4s;
    border-radius: 5px;
    padding: 36px 0;
    left: -400px;
    width: 1000px;
    min-height: 300px;
    overflow-x: hidden;
    overflow-y: hidden;
    transform-origin: top;
    display: block;
}
.header-area.header-area9 .header-elements .main-menu-ex ul li .mega-menu-all ul {
    box-shadow: none !important;
    padding: 0px 10px !important;
}
.header-area.header-area9 .header-elements .main-menu-ex ul li .mega-menu-all ul li a {
    display: inline-block;
    padding: 8px 40px;
}
.header-area.header-area9 .header-elements .main-menu-ex ul li .mega-menu-all .mega-menu-single {
    position: relative;
}
.header-area.header-area9 .header-elements .main-menu-ex ul li .mega-menu-all .mega-menu-single h3 {
    font-size: 20px;
    line-height: 20px;
    font-weight: 600;
    color: #081120;
    padding-left: 44px;
    position: relative;
}
.header-area.header-area9 .header-elements .main-menu-ex ul li .mega-menu-all .mega-menu-single h3::after {
    content: "";
    position: absolute;
    bottom: -14px;
    left: 43px;
    height: 2px;
    width: 25%;
    background-color: rgba(145, 145, 145, 0.2117647059);
}
.header-area.header-area9 .header-elements .main-menu-ex ul li .mega-menu-all .mega-menu-single::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    height: 400px;
    width: 2px;
    background-color: rgba(145, 145, 145, 0.2117647059);
}
.header-area.header-area9 .header-elements .main-menu-ex ul li .mega-menu-all .mega-menu-single.dis1::after {
    display: none;
}
.header-area.header-area9 .header-elements .main-menu-ex ul li .tp-submenu {
    visibility: hidden;
    opacity: 0;
    box-shadow: rgba(0, 0, 0, 0.2) 0px 20px 30px;
    position: absolute;
    background: white;
    top: 201.3%;
    transform: scale(1, 0);
    z-index: 0;
    transition: all 0.4s;
    border-radius: 5px;
    padding: 20px;
    left: -347px;
    width: 1300px;
    max-height: 600px;
    overflow-x: scroll;
    overflow-x: hidden;
    transform-origin: top;
    display: block;
}
.header-area.header-area9 .header-elements .main-menu-ex ul li .tp-submenu .all-images-menu {
    display: flex;
    align-items: center;
}
.header-area.header-area9 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb {
    transition: all 0.4s;
    position: relative;
    z-index: 1;
    margin: 0 20px 20px 0;
    text-align: center;
    overflow: hidden;
}
.header-area.header-area9 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .text {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    top: 10px;
    left: 0px;
}
.header-area.header-area9 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .text::after {
    position: absolute;
    content: "";
    height: 100px;
    width: 100px;
    left: -54px;
    top: -62px;
    transition: all 0.4s;
    background: #081120;
    transform: rotate(-45deg);
    z-index: -1;
}
.header-area.header-area9 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .text h2 {
    font-size: 20px;
    color: #fff;
    transform: rotate(-45deg);
    margin-left: 7px;
    margin-bottom: 13px;
    font-weight: 700;
}
.header-area.header-area9 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .bottom-heading {
    color: #08111a;
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: 18px; /* 100% */
    display: inline-block;
    margin-top: 8px;
}
.header-area.header-area9 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .bottom-heading:hover {
    color: #fc253f;
    transition: all 0.4s;
}
.header-area.header-area9 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .bottom-text {
    color: #000;
    font-size: 24px;
    font-style: normal;
    font-weight: 700;
    line-height: 24px; /* 100% */
    letter-spacing: 0.18px;
    display: inline-block;
    padding-top: 20px;
}
.header-area.header-area9 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb:hover .img1::after {
    transform: scale(1);
    transition: all 0.4s;
    visibility: visible;
    opacity: 0.4;
}
.header-area.header-area9 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb:hover .homemenu-btn {
    top: 26%;
    visibility: visible;
    opacity: 1;
    transition: all 0.6s;
    margin-top: 4px;
}
.header-area.header-area9 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb:hover .bottom-text {
    color: var(--vtc-bg-main-bg-1);
    transition: all 0.4s;
}
.header-area.header-area9 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb.active .img1::after {
    transform: scale(1);
    transition: all 0.4s;
    visibility: visible;
    opacity: 0.3;
}
.header-area.header-area9 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb.active .img1 .coming {
    position: absolute;
    top: 50%;
    left: 50%;
    margin-left: -84px;
    margin-top: -30px;
    height: 60px;
    width: 168px;
    z-index: 99;
    border: none;
}
.header-area.header-area9 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .img1 {
    position: relative;
    z-index: 1;
    overflow: hidden;
    width: 234px;
    height: 300px;
}
.header-area.header-area9 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .img1::after {
    position: absolute;
    content: "";
    height: 100%;
    width: 100%;
    left: 0;
    top: 0;
    transition: all 0.4s;
    background: #000026;
    opacity: 0;
    border-radius: 4px;
    transform: scale(0.8);
    z-index: 1;
    visibility: hidden;
}
.header-area.header-area9 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .img1 img {
    height: 100%;
    width: 100%;
    border-radius: 4px;
    transition: all 0.4s;
    border: 1px solid #e5e7eb;
}
.header-area.header-area9 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .homemenu-btn {
    position: absolute;
    top: 30%;
    z-index: 2;
    visibility: hidden;
    opacity: 0;
    text-align: center;
    transition: all 0.6s;
    margin: 0 auto;
    left: 18%;
    right: 20%;
}
.header-area.header-area9 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn1 {
    display: inline-block;
    padding: 16px;
    transition: all 0.4s;
    border-radius: 7px;
    position: relative;
    width: 150px;
    color: #fff;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: 16px; /* 100% */
    text-transform: capitalize;
    z-index: 1;
    background: #3b943b;
}
.header-area.header-area9 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn1 i {
    transform: rotate(-45deg);
    margin-left: 4px;
    transition: all 0.4s;
}
.header-area.header-area9 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn1:hover {
    color: #fff;
    transition: all 0.4s;
    transform: translateY(-5px);
    background: #3b943b;
}
.header-area.header-area9 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn1:hover i {
    transform: rotate(0);
    transition: all 0.4s;
}
.header-area.header-area9 .header-elements .main-menu-ex ul li ul {
    position: absolute;
    left: 0;
    top: 50px;
    border-radius: 4px;
    background: #fff;
    -webkit-backdrop-filter: blur(27px);
    backdrop-filter: blur(27px);
    width: 206px;
    box-shadow: rgba(0, 0, 0, 0.068) 0px 20px 30px;
    padding: 10px;
}
.header-area.header-area9 .header-elements .main-menu-ex ul li ul:hover > a {
    transition: all 0.4s;
    padding-left: 25px;
    color: red;
}
.header-area.header-area9 .header-elements .main-menu-ex ul li ul:hover > a::after {
    background: blue;
    transition: all 0.4s;
    visibility: visible;
    opacity: 1;
}
.header-area.header-area9 .header-elements .main-menu-ex ul li ul li ul {
    left: 190px;
    top: 10px;
}
.header-area.header-area9 .header-elements .main-menu-ex ul li ul li a {
    color: var(--qt-text-h-text3);
}
.header-area.header-area9 .header-elements .main-menu-ex ul li ul li a::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: -10px;
    width: 0px;
    height: 100%;
    color: var(--qt-text-h-text3);
    border-radius: 4px;
    transition: all 0.4s;
    z-index: -1;
    opacity: 1;
}
.header-area.header-area9 .header-elements .main-menu-ex ul li ul li a:hover {
    color: #2a9134;
    transition: all 0.4s;
}
.header-area.header-area9 .header-elements .main-menu-ex ul li ul li a:hover::after {
    background-color: #2a9134;
    border-radius: 4px;
    width: 5px;
    left: -10px;
    opacity: 1;
}
.header-area.header-area9 .header-elements .main-menu-ex ul li a {
    color: var(--vtc-text-heading-text-1);
    font-size: 16px;
    display: block;
    font-weight: 500;
    padding: 8px 15px;
    transition: all 0.3s;
}
.header-area.header-area9 .header-elements .main-menu-ex ul li a:hover {
    color: #2a9134;
    transition: all 0.4s;
}
.header-area.header-area9 .header-elements .main-menu-ex ul li:hover .tp-submenu {
    visibility: visible;
    transition: all 0.5s ease-in-out;
    opacity: 1;
    z-index: 9;
    top: 150.3%;
    position: absolute;
    transition: all 0.4s;
    transform: scale(1);
}
.header-area.header-area9 .header-elements .main-menu-ex ul li:hover .mega-menu-all {
    opacity: 1;
    visibility: visible;
    transform: scale(1) translateY(-30px);
}

.header-area.header-area10 {
    padding-top: 10px;
}
.header-area.header-area10 .header-elements {
    background-color: #fff;
    position: relative;
    z-index: 3;
    padding: 20px 32px;
    border-radius: 8px;
}
.header-area.header-area10 .header-elements .main-menu-ex {
    padding: 2px 24px;
    position: relative;
}
.header-area.header-area10 .header-elements .main-menu-ex ul li .mega-menu-all {
    visibility: hidden;
    opacity: 0;
    box-shadow: rgba(0, 0, 0, 0.2) 0px 20px 30px;
    position: absolute;
    background: white;
    top: 201.3%;
    transform: scale(1, 0);
    z-index: 0;
    transition: all 0.4s;
    border-radius: 5px;
    padding: 36px 0;
    left: -400px;
    width: 1000px;
    min-height: 300px;
    overflow-x: hidden;
    overflow-y: hidden;
    transform-origin: top;
    display: block;
}
.header-area.header-area10 .header-elements .main-menu-ex ul li .mega-menu-all ul {
    box-shadow: none !important;
    padding: 0px 10px !important;
}
.header-area.header-area10 .header-elements .main-menu-ex ul li .mega-menu-all ul li a {
    display: inline-block;
    padding: 8px 40px;
}
.header-area.header-area10 .header-elements .main-menu-ex ul li .mega-menu-all .mega-menu-single {
    position: relative;
}
.header-area.header-area10 .header-elements .main-menu-ex ul li .mega-menu-all .mega-menu-single h3 {
    font-size: 20px;
    line-height: 20px;
    font-weight: 600;
    color: #081120;
    padding-left: 44px;
    position: relative;
}
.header-area.header-area10 .header-elements .main-menu-ex ul li .mega-menu-all .mega-menu-single h3::after {
    content: "";
    position: absolute;
    bottom: -14px;
    left: 43px;
    height: 2px;
    width: 25%;
    background-color: rgba(145, 145, 145, 0.2117647059);
}
.header-area.header-area10 .header-elements .main-menu-ex ul li .mega-menu-all .mega-menu-single::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    height: 400px;
    width: 2px;
    background-color: rgba(145, 145, 145, 0.2117647059);
}
.header-area.header-area10 .header-elements .main-menu-ex ul li .mega-menu-all .mega-menu-single.dis1::after {
    display: none;
}
.header-area.header-area10 .header-elements .main-menu-ex ul li .tp-submenu {
    visibility: hidden;
    opacity: 0;
    box-shadow: rgba(0, 0, 0, 0.2) 0px 20px 30px;
    position: absolute;
    background: white;
    top: 201.3%;
    transform: scale(1, 0);
    z-index: 0;
    transition: all 0.4s;
    border-radius: 5px;
    padding: 20px;
    left: -347px;
    width: 1300px;
    max-height: 600px;
    overflow-x: scroll;
    overflow-x: hidden;
    transform-origin: top;
    display: block;
}
.header-area.header-area10 .header-elements .main-menu-ex ul li .tp-submenu .all-images-menu {
    display: flex;
    align-items: center;
}
.header-area.header-area10 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb {
    transition: all 0.4s;
    position: relative;
    z-index: 1;
    margin: 0 20px 20px 0;
    text-align: center;
    overflow: hidden;
}
.header-area.header-area10 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .text {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    top: 10px;
    left: 0px;
}
.header-area.header-area10 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .text::after {
    position: absolute;
    content: "";
    height: 100px;
    width: 100px;
    left: -54px;
    top: -62px;
    transition: all 0.4s;
    background: #081120;
    transform: rotate(-45deg);
    z-index: -1;
}
.header-area.header-area10 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .text h2 {
    font-size: 20px;
    color: #fff;
    transform: rotate(-45deg);
    margin-left: 7px;
    margin-bottom: 13px;
    font-weight: 700;
}
.header-area.header-area10 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .bottom-heading {
    color: #08111a;
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: 18px; /* 100% */
    display: inline-block;
    margin-top: 8px;
}
.header-area.header-area10 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .bottom-heading:hover {
    color: #fc253f;
    transition: all 0.4s;
}
.header-area.header-area10 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .bottom-text {
    color: #000;
    font-size: 24px;
    font-style: normal;
    font-weight: 700;
    line-height: 24px; /* 100% */
    letter-spacing: 0.18px;
    display: inline-block;
    padding-top: 20px;
}
.header-area.header-area10 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb:hover .img1::after {
    transform: scale(1);
    transition: all 0.4s;
    visibility: visible;
    opacity: 0.4;
}
.header-area.header-area10 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb:hover .homemenu-btn {
    top: 26%;
    visibility: visible;
    opacity: 1;
    transition: all 0.6s;
    margin-top: 4px;
}
.header-area.header-area10 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb:hover .bottom-text {
    color: var(--vtc-bg-main-bg-1);
    transition: all 0.4s;
}
.header-area.header-area10 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb.active .img1::after {
    transform: scale(1);
    transition: all 0.4s;
    visibility: visible;
    opacity: 0.3;
}
.header-area.header-area10 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb.active .img1 .coming {
    position: absolute;
    top: 50%;
    left: 50%;
    margin-left: -84px;
    margin-top: -30px;
    height: 60px;
    width: 168px;
    z-index: 99;
    border: none;
}
.header-area.header-area10 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .img1 {
    position: relative;
    z-index: 1;
    overflow: hidden;
    width: 234px;
    height: 300px;
}
.header-area.header-area10 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .img1::after {
    position: absolute;
    content: "";
    height: 100%;
    width: 100%;
    left: 0;
    top: 0;
    transition: all 0.4s;
    background: #000026;
    opacity: 0;
    border-radius: 4px;
    transform: scale(0.8);
    z-index: 1;
    visibility: hidden;
}
.header-area.header-area10 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .img1 img {
    height: 100%;
    width: 100%;
    border-radius: 4px;
    transition: all 0.4s;
    border: 1px solid #e5e7eb;
}
.header-area.header-area10 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .homemenu-btn {
    position: absolute;
    top: 30%;
    z-index: 2;
    visibility: hidden;
    opacity: 0;
    text-align: center;
    transition: all 0.6s;
    margin: 0 auto;
    left: 18%;
    right: 20%;
}
.header-area.header-area10 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn1 {
    display: inline-block;
    padding: 16px;
    transition: all 0.4s;
    border-radius: 7px;
    position: relative;
    width: 150px;
    color: #fff;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: 16px; /* 100% */
    text-transform: capitalize;
    z-index: 1;
    background: #fa6444;
}
.header-area.header-area10 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn1 i {
    transform: rotate(-45deg);
    margin-left: 4px;
    transition: all 0.4s;
}
.header-area.header-area10 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn1:hover {
    color: #fff;
    transition: all 0.4s;
    transform: translateY(-5px);
    background: #fa6444;
}
.header-area.header-area10 .header-elements .main-menu-ex ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn1:hover i {
    transform: rotate(0);
    transition: all 0.4s;
}
.header-area.header-area10 .header-elements .main-menu-ex ul li ul {
    position: absolute;
    left: 0;
    top: 50px;
    border-radius: 4px;
    background: #fff;
    -webkit-backdrop-filter: blur(27px);
    backdrop-filter: blur(27px);
    width: 206px;
    box-shadow: rgba(0, 0, 0, 0.068) 0px 20px 30px;
    padding: 10px;
}
.header-area.header-area10 .header-elements .main-menu-ex ul li ul:hover > a {
    transition: all 0.4s;
    padding-left: 25px;
    color: red;
}
.header-area.header-area10 .header-elements .main-menu-ex ul li ul:hover > a::after {
    background: blue;
    transition: all 0.4s;
    visibility: visible;
    opacity: 1;
}
.header-area.header-area10 .header-elements .main-menu-ex ul li ul li ul {
    left: 190px;
    top: 10px;
}
.header-area.header-area10 .header-elements .main-menu-ex ul li ul li a {
    color: var(--qt-text-h-text3);
}
.header-area.header-area10 .header-elements .main-menu-ex ul li ul li a::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: -10px;
    width: 0px;
    height: 100%;
    color: var(--qt-text-h-text3);
    border-radius: 4px;
    transition: all 0.4s;
    z-index: -1;
    opacity: 1;
}
.header-area.header-area10 .header-elements .main-menu-ex ul li ul li a:hover {
    color: #fa6444;
    transition: all 0.4s;
}
.header-area.header-area10 .header-elements .main-menu-ex ul li ul li a:hover::after {
    background-color: #fa6444;
    border-radius: 4px;
    width: 5px;
    left: -10px;
    opacity: 1;
}
.header-area.header-area10 .header-elements .main-menu-ex ul li a {
    color: var(--vtc-text-heading-text-1);
    font-size: 16px;
    display: block;
    font-weight: 500;
    padding: 8px 15px;
    transition: all 0.3s;
}
.header-area.header-area10 .header-elements .main-menu-ex ul li a:hover {
    color: #fa6444;
    transition: all 0.4s;
}
.header-area.header-area10 .header-elements .main-menu-ex ul li:hover .tp-submenu {
    visibility: visible;
    transition: all 0.5s ease-in-out;
    opacity: 1;
    z-index: 9;
    top: 150.3%;
    position: absolute;
    transition: all 0.4s;
    transform: scale(1);
}
.header-area.header-area10 .header-elements .main-menu-ex ul li:hover .mega-menu-all {
    opacity: 1;
    visibility: visible;
    transform: scale(1) translateY(-30px);
}

.header-area.header-area1 .main-menu-ex.main-menu-ex1 li.has-dropdown:hover a.main1 {
    color: var(--vtc-bg-main-bg-1);
}

.header-area.header-area1 .main-menu-ex.main-menu-ex1 li.dropdown-menu-parrent:hover a.main1 {
    color: var(--vtc-bg-main-bg-1);
}

.header-area.header-area1 .main-menu-ex.main-menu-ex1 li.has-dropdown.has-dropdown1:hover a.main {
    color: var(--vtc-bg-main-bg-1);
}
.header-area.header-area1 .main-menu-ex.main-menu-ex1 li.has-dropdown.has-dropdown1:hover a.main::after {
    background-color: var(--vtc-bg-main-bg-1);
    border-radius: 4px;
    width: 5px;
    left: -10px;
    opacity: 1;
}

.header-area.header-area3 .main-menu-ex.main-menu-ex1 li.has-dropdown:hover a.main1 {
    color: var(--vtc-bg-main-bg-1);
}

.header-area.header-area3 .main-menu-ex.main-menu-ex1 li.dropdown-menu-parrent:hover a.main1 {
    color: var(--vtc-bg-main-bg-1);
}

.header-area.header-area3 .main-menu-ex.main-menu-ex1 li.has-dropdown.has-dropdown1:hover a.main {
    color: var(--vtc-bg-main-bg-1);
}
.header-area.header-area3 .main-menu-ex.main-menu-ex1 li.has-dropdown.has-dropdown1:hover a.main::after {
    background-color: var(--vtc-bg-main-bg-1);
    border-radius: 4px;
    width: 5px;
    left: -10px;
    opacity: 1;
}

.header-area.header-area4 .main-menu-ex.main-menu-ex1 li.has-dropdown:hover a.main1 {
    color: var(--vtc-bg-main-bg-6);
}

.header-area.header-area4 .main-menu-ex.main-menu-ex1 li.dropdown-menu-parrent:hover a.main1 {
    color: var(--vtc-bg-main-bg-6);
}

.header-area.header-area4 .main-menu-ex.main-menu-ex1 li.has-dropdown.has-dropdown1:hover a.main {
    color: var(--vtc-bg-main-bg-6);
}
.header-area.header-area4 .main-menu-ex.main-menu-ex1 li.has-dropdown.has-dropdown1:hover a.main::after {
    background-color: var(--vtc-bg-main-bg-6);
    border-radius: 4px;
    width: 5px;
    left: -10px;
    opacity: 1;
}

.header-area.header-area5 .main-menu-ex.main-menu-ex1 li.has-dropdown:hover a.main1 {
    color: var(--vtc-bg-main-bg-2);
}

.header-area.header-area5 .main-menu-ex.main-menu-ex1 li.dropdown-menu-parrent:hover a.main1 {
    color: var(--vtc-bg-main-bg-2);
}

.header-area.header-area5 .main-menu-ex.main-menu-ex1 li.has-dropdown.has-dropdown1:hover a.main {
    color: var(--vtc-bg-main-bg-2);
}
.header-area.header-area5 .main-menu-ex.main-menu-ex1 li.has-dropdown.has-dropdown1:hover a.main::after {
    background-color: var(--vtc-bg-main-bg-2);
    border-radius: 4px;
    width: 5px;
    left: -10px;
    opacity: 1;
}

.header-area.header-area6 .main-menu-ex.main-menu-ex1 li.has-dropdown:hover a.main1 {
    color: #31572c;
}

.header-area.header-area6 .main-menu-ex.main-menu-ex1 li.dropdown-menu-parrent:hover a.main1 {
    color: #31572c;
}

.header-area.header-area6 .main-menu-ex.main-menu-ex1 li.has-dropdown.has-dropdown1:hover a.main {
    color: #31572c;
}
.header-area.header-area6 .main-menu-ex.main-menu-ex1 li.has-dropdown.has-dropdown1:hover a.main::after {
    background-color: #31572c;
    border-radius: 4px;
    width: 5px;
    left: -10px;
    opacity: 1;
}

.header-area.header-area7 .main-menu-ex.main-menu-ex1 li.has-dropdown:hover a.main1 {
    color: #5957e5;
}

.header-area.header-area7 .main-menu-ex.main-menu-ex1 li.dropdown-menu-parrent:hover a.main1 {
    color: #5957e5;
}

.header-area.header-area7 .main-menu-ex.main-menu-ex1 li.has-dropdown.has-dropdown1:hover a.main {
    color: #5957e5;
}
.header-area.header-area7 .main-menu-ex.main-menu-ex1 li.has-dropdown.has-dropdown1:hover a.main::after {
    background-color: #5957e5;
    border-radius: 4px;
    width: 5px;
    left: -10px;
    opacity: 1;
}

.header-area.header-area8 .main-menu-ex.main-menu-ex1 li.has-dropdown:hover a.main1 {
    color: #f6aa32;
}

.header-area.header-area8 .main-menu-ex.main-menu-ex1 li.dropdown-menu-parrent:hover a.main1 {
    color: #f6aa32;
}

.header-area.header-area8 .main-menu-ex.main-menu-ex1 li.has-dropdown.has-dropdown1:hover a.main {
    color: #f6aa32;
}
.header-area.header-area8 .main-menu-ex.main-menu-ex1 li.has-dropdown.has-dropdown1:hover a.main::after {
    background-color: #f6aa32;
    border-radius: 4px;
    width: 5px;
    left: -10px;
    opacity: 1;
}

.header-area.header-area9 .main-menu-ex.main-menu-ex1 li.has-dropdown:hover a.main1 {
    color: #2a9134;
}

.header-area.header-area9 .main-menu-ex.main-menu-ex1 li.dropdown-menu-parrent:hover a.main1 {
    color: #2a9134;
}

.header-area.header-area9 .main-menu-ex.main-menu-ex1 li.has-dropdown.has-dropdown1:hover a.main {
    color: #2a9134;
}
.header-area.header-area9 .main-menu-ex.main-menu-ex1 li.has-dropdown.has-dropdown1:hover a.main::after {
    background-color: #2a9134;
    border-radius: 4px;
    width: 5px;
    left: -10px;
    opacity: 1;
}

.header-area.header-area10 .main-menu-ex.main-menu-ex1 li.has-dropdown:hover a.main1 {
    color: #fa6444;
}

.header-area.header-area10 .main-menu-ex.main-menu-ex1 li.dropdown-menu-parrent:hover a.main1 {
    color: #fa6444;
}

.header-area.header-area10 .main-menu-ex.main-menu-ex1 li.has-dropdown.has-dropdown1:hover a.main {
    color: #fa6444;
}
.header-area.header-area10 .main-menu-ex.main-menu-ex1 li.has-dropdown.has-dropdown1:hover a.main::after {
    background-color: #fa6444;
    border-radius: 4px;
    width: 5px;
    left: -10px;
    opacity: 1;
}

.header-area.header-area2 .main-menu-ex.main-menu-ex1 li.has-dropdown:hover a.main1 {
    color: var(--vtc-bg-main-bg-2);
}

.header-area.header-area2 .main-menu-ex.main-menu-ex1 li.dropdown-menu-parrent:hover a.main1 {
    color: var(--vtc-bg-main-bg-2);
}

.header-area.header-area2 .main-menu-ex.main-menu-ex1 li.has-dropdown.has-dropdown1:hover a.main {
    color: var(--vtc-bg-main-bg-2);
}
.header-area.header-area2 .main-menu-ex.main-menu-ex1 li.has-dropdown.has-dropdown1:hover a.main::after {
    background-color: var(--vtc-bg-main-bg-2);
    border-radius: 4px;
    width: 5px;
    left: -10px;
    opacity: 1;
}

.header5-top {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 9;
    width: 100%;
    margin: 16px 0;
}
.header5-top .icon-text a {
    display: inline-block;
    color: var(--vtc-text-text-white-text-1);
    font-size: var(--f-fs-font-fs16);
    line-height: var(--f-fs-font-fs16);
    font-weight: var(--f-fw-medium);
}
.header5-top .icon-text a img {
    margin-top: -3px;
    margin-right: 5px;
}

/* 
++++++++++++++++++++++++++++++++++
==== =====nav menu all 1 ====== ======
+++++++++++++++++++++++++++++++++
*/
.mobile-sidebar .logo-m {
    margin-top: -40px;
    margin-bottom: 30px;
}
.mobile-sidebar .mobile-button a {
    width: 100%;
    text-align: center;
    margin-top: 30px;
    margin-bottom: 30px;
}
.mobile-sidebar .single-footer-items h3 {
    color: var(--vtc-text-heading-text-1);
    line-height: var(--f-fs-font-fs24);
    font-size: var(--f-fs-font-fs24);
    font-weight: var(--f-fw-bold);
    margin-bottom: 30px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .mobile-sidebar .single-footer-items h3 {
        margin-bottom: 16px;
        margin-top: 30px;
    }
}
@media (max-width: 767px) {
    .mobile-sidebar .single-footer-items h3 {
        margin-bottom: 16px;
        margin-top: 30px;
    }
}
.mobile-sidebar .single-footer-items .menu-list li a {
    display: inline-block;
    color: var(--ztc-text-text-2);
    font-size: var(--f-fs-font-fs16);
    line-height: var(--f-fs-font-fs16);
    font-weight: var(--f-fw-medium);
    padding: 10px 0px;
    transition: all 0.4s;
}
.mobile-sidebar .single-footer-items .menu-list li a:hover {
    color: var(--ztc-bg-main-bg-4);
    transform: translateX(5px);
    transition: all 0.4s;
}
.mobile-sidebar .single-footer-items .contact-box {
    display: flex;
    align-items: center;
    padding: 10px 0px;
}
.mobile-sidebar .single-footer-items .contact-box .icon {
    margin-right: 16px;
}
.mobile-sidebar .single-footer-items .contact-box .icon img {
    filter: brightness(0);
}
.mobile-sidebar .single-footer-items .contact-box .pera a {
    display: inline-block;
    color: var(--ztc-text-text-2);
    font-size: var(--f-fs-font-fs18);
    line-height: var(--f-fs-font-fs28);
    font-weight: var(--f-fw-medium);
    transition: all 0.4s;
}
.mobile-sidebar .single-footer-items .contact-box .pera a:hover {
    color: var(--ztc-bg-main-bg-4);
    transition: all 0.4s;
}
.mobile-sidebar .contact-infos h3 {
    color: var(--vtc-text-heading-text-1);
    line-height: var(--f-fs-font-fs24);
    font-size: var(--f-fs-font-fs24);
    font-weight: var(--f-fw-bold);
    margin-bottom: 30px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .mobile-sidebar .contact-infos h3 {
        margin-bottom: 16px;
        margin-top: 30px;
    }
}
@media (max-width: 767px) {
    .mobile-sidebar .contact-infos h3 {
        margin-bottom: 16px;
        margin-top: 30px;
    }
}
.mobile-sidebar .contact-infos .social-icon {
    padding-top: 24px;
}
.mobile-sidebar .contact-infos .social-icon li {
    display: inline-block;
}
.mobile-sidebar .contact-infos .social-icon li a {
    display: inline-block;
    color: var(--ztc-text-text-2);
    font-size: var(--f-fs-font-fs16);
    background-color: rgba(0, 0, 0, 0.0549019608);
    height: 32px;
    width: 32px;
    text-align: center;
    line-height: 0;
    border-radius: 50%;
    margin: 0px 2px;
    transition: all 0.4s;
}
.mobile-sidebar .contact-infos .social-icon li a:hover {
    color: var(--ztc-text-text-1);
    background-color: var(--ztc-bg-main-bg-4);
    transition: all 0.4s;
}
.mobile-sidebar .mobile-button a.menu-btn2 {
    background-color: #fff;
    padding: 18px;
    width: 100%;
    border-radius: 4px;
    display: inline-block;
    color: var(--ztc-text-text-1);
    font-size: var(--f-fs-font-fs16);
    font-weight: var(--f-fw-blod);
    line-height: var(--f-fs-font-fs16);
}

/*
::::::::::::::::::::::::::
 NAV MENU AREA CSS
::::::::::::::::::::::::::
*/ /*# sourceMappingURL=main.css.map */
