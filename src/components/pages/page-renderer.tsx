'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { BlockRenderer } from './blocks/block-renderer'
import { SEOHead } from './seo/seo-head'
import { ShareButtons } from './social/share-buttons'
import { BreadcrumbNav } from './navigation/breadcrumb-nav'
import { TableOfContents } from './navigation/table-of-contents'
import { RelatedContent } from './content/related-content'
import { CommentSection } from './comments/comment-section'
import { format } from 'date-fns'
import { 
  Calendar, 
  Clock, 
  User, 
  Tag, 
  Folder, 
  Eye, 
  Share2, 
  Bookmark,
  Edit,
  ArrowLeft,
  ChevronRight
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface PageData {
  id: string
  title: string
  slug: string
  content?: string
  excerpt?: string
  template: string
  status: 'DRAFT' | 'PUBLISHED' | 'ARCHIVED'
  publishedAt?: Date
  createdAt: Date
  updatedAt: Date
  seoTitle?: string
  seoDescription?: string
  featuredImage?: string
  author: {
    id: string
    name: string
    image?: string
    bio?: string
  }
  category?: {
    id: string
    name: string
    slug: string
    color?: string
  }
  tags?: Array<{
    id: string
    name: string
    slug: string
    color?: string
  }>
  blocks?: Array<{
    id: string
    type: string
    content: any
    order: number
  }>
  comments?: Array<{
    id: string
    content: string
    author: {
      name: string
      image?: string
    }
    createdAt: Date
    approved: boolean
  }>
  _count?: {
    views: number
    comments: number
    likes: number
  }
}

interface PageRendererProps {
  page: PageData
  template?: string
  showComments?: boolean
  showRelated?: boolean
  showTOC?: boolean
  showBreadcrumbs?: boolean
  showShare?: boolean
  showAuthor?: boolean
  showMeta?: boolean
  isPreview?: boolean
  onEdit?: () => void
  relatedPages?: PageData[]
  className?: string
}

export function PageRenderer({
  page,
  template,
  showComments = true,
  showRelated = true,
  showTOC = true,
  showBreadcrumbs = true,
  showShare = true,
  showAuthor = true,
  showMeta = true,
  isPreview = false,
  onEdit,
  relatedPages = [],
  className,
}: PageRendererProps) {
  const [isBookmarked, setIsBookmarked] = useState(false)
  const [viewCount, setViewCount] = useState(page._count?.views || 0)

  useEffect(() => {
    // Track page view
    if (!isPreview) {
      // Implement view tracking
      setViewCount(prev => prev + 1)
    }
  }, [page.id, isPreview])

  const pageTemplate = template || page.template || 'default'
  const publishedDate = page.publishedAt || page.createdAt
  const readingTime = calculateReadingTime(page.content || '')

  // Generate table of contents from content
  const tableOfContents = generateTOC(page.content || '')

  const handleBookmark = () => {
    setIsBookmarked(!isBookmarked)
    // Implement bookmark functionality
  }

  return (
    <>
      <SEOHead
        title={page.seoTitle || page.title}
        description={page.seoDescription || page.excerpt}
        image={page.featuredImage}
        url={`/${page.slug}`}
        type="article"
        publishedTime={page.publishedAt}
        modifiedTime={page.updatedAt}
        author={page.author.name}
        tags={page.tags?.map(tag => tag.name)}
      />

      <div className={cn("min-h-screen bg-background", className)}>
        {/* Preview Banner */}
        {isPreview && (
          <div className="bg-yellow-100 border-b border-yellow-200 px-4 py-2">
            <div className="max-w-4xl mx-auto flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Eye className="h-4 w-4 text-yellow-600" />
                <span className="text-sm font-medium text-yellow-800">Preview Mode</span>
              </div>
              {onEdit && (
                <Button size="sm" onClick={onEdit}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Page
                </Button>
              )}
            </div>
          </div>
        )}

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
            {/* Main Content */}
            <main className="lg:col-span-8">
              {/* Breadcrumbs */}
              {showBreadcrumbs && (
                <BreadcrumbNav
                  items={[
                    { label: 'Home', href: '/' },
                    ...(page.category ? [{ label: page.category.name, href: `/category/${page.category.slug}` }] : []),
                    { label: page.title, href: `/${page.slug}`, current: true }
                  ]}
                  className="mb-6"
                />
              )}

              {/* Page Header */}
              <PageHeader
                page={page}
                template={pageTemplate}
                showMeta={showMeta}
                showAuthor={showAuthor}
                showShare={showShare}
                readingTime={readingTime}
                viewCount={viewCount}
                isBookmarked={isBookmarked}
                onBookmark={handleBookmark}
              />

              {/* Featured Image */}
              {page.featuredImage && (
                <div className="mb-8">
                  <img
                    src={page.featuredImage}
                    alt={page.title}
                    className="w-full h-64 md:h-96 object-cover rounded-lg"
                  />
                </div>
              )}

              {/* Content */}
              <PageContent
                page={page}
                template={pageTemplate}
              />

              {/* Tags */}
              {page.tags && page.tags.length > 0 && (
                <div className="mt-8 pt-8 border-t">
                  <div className="flex items-center space-x-2 mb-4">
                    <Tag className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium text-muted-foreground">Tags</span>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {page.tags.map((tag) => (
                      <Badge
                        key={tag.id}
                        variant="secondary"
                        className="hover:bg-primary hover:text-primary-foreground cursor-pointer"
                        style={tag.color ? { backgroundColor: tag.color + '20', color: tag.color } : {}}
                      >
                        {tag.name}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* Author Bio */}
              {showAuthor && page.author.bio && (
                <AuthorBio author={page.author} className="mt-8" />
              )}

              {/* Share Buttons */}
              {showShare && (
                <ShareButtons
                  url={`/${page.slug}`}
                  title={page.title}
                  description={page.excerpt}
                  className="mt-8"
                />
              )}

              {/* Related Content */}
              {showRelated && relatedPages.length > 0 && (
                <RelatedContent
                  pages={relatedPages}
                  currentPage={page}
                  className="mt-12"
                />
              )}

              {/* Comments */}
              {showComments && (
                <CommentSection
                  pageId={page.id}
                  comments={page.comments || []}
                  className="mt-12"
                />
              )}
            </main>

            {/* Sidebar */}
            <aside className="lg:col-span-4">
              <div className="sticky top-8 space-y-6">
                {/* Table of Contents */}
                {showTOC && tableOfContents.length > 0 && (
                  <TableOfContents
                    items={tableOfContents}
                    className="mb-6"
                  />
                )}

                {/* Page Stats */}
                <PageStats
                  viewCount={viewCount}
                  commentCount={page._count?.comments || 0}
                  likeCount={page._count?.likes || 0}
                  publishedDate={publishedDate}
                  readingTime={readingTime}
                />

                {/* Category Info */}
                {page.category && (
                  <CategoryInfo category={page.category} />
                )}
              </div>
            </aside>
          </div>
        </div>
      </div>
    </>
  )
}

interface PageHeaderProps {
  page: PageData
  template: string
  showMeta: boolean
  showAuthor: boolean
  showShare: boolean
  readingTime: number
  viewCount: number
  isBookmarked: boolean
  onBookmark: () => void
}

function PageHeader({
  page,
  template,
  showMeta,
  showAuthor,
  showShare,
  readingTime,
  viewCount,
  isBookmarked,
  onBookmark,
}: PageHeaderProps) {
  const publishedDate = page.publishedAt || page.createdAt

  return (
    <header className="mb-8">
      {/* Status Badge */}
      {page.status !== 'PUBLISHED' && (
        <Badge variant="secondary" className="mb-4">
          {page.status}
        </Badge>
      )}

      {/* Title */}
      <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold tracking-tight mb-4">
        {page.title}
      </h1>

      {/* Excerpt */}
      {page.excerpt && (
        <p className="text-xl text-muted-foreground mb-6 leading-relaxed">
          {page.excerpt}
        </p>
      )}

      {/* Meta Information */}
      {showMeta && (
        <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground mb-6">
          {/* Author */}
          {showAuthor && (
            <div className="flex items-center space-x-2">
              <Avatar className="h-6 w-6">
                <AvatarImage src={page.author.image} alt={page.author.name} />
                <AvatarFallback>
                  {page.author.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <span>{page.author.name}</span>
            </div>
          )}

          {/* Published Date */}
          <div className="flex items-center space-x-1">
            <Calendar className="h-4 w-4" />
            <span>{format(publishedDate, 'MMM dd, yyyy')}</span>
          </div>

          {/* Reading Time */}
          <div className="flex items-center space-x-1">
            <Clock className="h-4 w-4" />
            <span>{readingTime} min read</span>
          </div>

          {/* View Count */}
          <div className="flex items-center space-x-1">
            <Eye className="h-4 w-4" />
            <span>{viewCount.toLocaleString()} views</span>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex items-center space-x-2">
        <Button
          variant="outline"
          size="sm"
          onClick={onBookmark}
          className={isBookmarked ? 'bg-primary text-primary-foreground' : ''}
        >
          <Bookmark className="h-4 w-4 mr-2" />
          {isBookmarked ? 'Bookmarked' : 'Bookmark'}
        </Button>

        {showShare && (
          <Button variant="outline" size="sm">
            <Share2 className="h-4 w-4 mr-2" />
            Share
          </Button>
        )}
      </div>
    </header>
  )
}

interface PageContentProps {
  page: PageData
  template: string
}

function PageContent({ page, template }: PageContentProps) {
  // Render content based on template
  switch (template) {
    case 'blocks':
      return (
        <div className="space-y-8">
          {page.blocks?.map((block) => (
            <BlockRenderer
              key={block.id}
              block={block}
            />
          ))}
        </div>
      )
    
    case 'landing':
      return <LandingPageContent page={page} />
    
    case 'about':
      return <AboutPageContent page={page} />
    
    case 'contact':
      return <ContactPageContent page={page} />
    
    default:
      return (
        <div className="prose prose-lg max-w-none">
          {page.content && (
            <div dangerouslySetInnerHTML={{ __html: page.content }} />
          )}
        </div>
      )
  }
}

function LandingPageContent({ page }: { page: PageData }) {
  return (
    <div className="space-y-12">
      {/* Hero Section */}
      <section className="text-center py-12">
        <h2 className="text-4xl font-bold mb-4">Welcome to {page.title}</h2>
        {page.excerpt && (
          <p className="text-xl text-muted-foreground mb-8">{page.excerpt}</p>
        )}
        <Button size="lg">Get Started</Button>
      </section>

      {/* Content */}
      {page.content && (
        <section className="prose prose-lg max-w-none">
          <div dangerouslySetInnerHTML={{ __html: page.content }} />
        </section>
      )}

      {/* Blocks */}
      {page.blocks && (
        <section className="space-y-8">
          {page.blocks.map((block) => (
            <BlockRenderer key={block.id} block={block} />
          ))}
        </section>
      )}
    </div>
  )
}

function AboutPageContent({ page }: { page: PageData }) {
  return (
    <div className="space-y-8">
      {/* Main Content */}
      {page.content && (
        <div className="prose prose-lg max-w-none">
          <div dangerouslySetInnerHTML={{ __html: page.content }} />
        </div>
      )}

      {/* Author Info */}
      <Card>
        <CardHeader>
          <CardTitle>About the Author</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-start space-x-4">
            <Avatar className="h-16 w-16">
              <AvatarImage src={page.author.image} alt={page.author.name} />
              <AvatarFallback>
                {page.author.name.split(' ').map(n => n[0]).join('').toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div>
              <h3 className="font-semibold text-lg">{page.author.name}</h3>
              {page.author.bio && (
                <p className="text-muted-foreground mt-2">{page.author.bio}</p>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Blocks */}
      {page.blocks && (
        <div className="space-y-8">
          {page.blocks.map((block) => (
            <BlockRenderer key={block.id} block={block} />
          ))}
        </div>
      )}
    </div>
  )
}

function ContactPageContent({ page }: { page: PageData }) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
      {/* Content */}
      <div>
        {page.content && (
          <div className="prose prose-lg">
            <div dangerouslySetInnerHTML={{ __html: page.content }} />
          </div>
        )}
      </div>

      {/* Contact Form */}
      <Card>
        <CardHeader>
          <CardTitle>Get in Touch</CardTitle>
          <CardDescription>Send us a message and we'll get back to you.</CardDescription>
        </CardHeader>
        <CardContent>
          {/* Contact form would go here */}
          <p className="text-muted-foreground">Contact form component would be rendered here.</p>
        </CardContent>
      </Card>
    </div>
  )
}

function AuthorBio({ author, className }: { author: PageData['author'], className?: string }) {
  return (
    <Card className={className}>
      <CardContent className="pt-6">
        <div className="flex items-start space-x-4">
          <Avatar className="h-16 w-16">
            <AvatarImage src={author.image} alt={author.name} />
            <AvatarFallback>
              {author.name.split(' ').map(n => n[0]).join('').toUpperCase()}
            </AvatarFallback>
          </Avatar>
          <div className="flex-1">
            <h3 className="font-semibold text-lg mb-2">About {author.name}</h3>
            <p className="text-muted-foreground">{author.bio}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

function PageStats({ 
  viewCount, 
  commentCount, 
  likeCount, 
  publishedDate, 
  readingTime 
}: {
  viewCount: number
  commentCount: number
  likeCount: number
  publishedDate: Date
  readingTime: number
}) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Page Statistics</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <span className="text-sm text-muted-foreground">Views</span>
          <span className="font-medium">{viewCount.toLocaleString()}</span>
        </div>
        <div className="flex items-center justify-between">
          <span className="text-sm text-muted-foreground">Comments</span>
          <span className="font-medium">{commentCount}</span>
        </div>
        <div className="flex items-center justify-between">
          <span className="text-sm text-muted-foreground">Likes</span>
          <span className="font-medium">{likeCount}</span>
        </div>
        <div className="flex items-center justify-between">
          <span className="text-sm text-muted-foreground">Reading Time</span>
          <span className="font-medium">{readingTime} min</span>
        </div>
        <div className="flex items-center justify-between">
          <span className="text-sm text-muted-foreground">Published</span>
          <span className="font-medium">{format(publishedDate, 'MMM dd, yyyy')}</span>
        </div>
      </CardContent>
    </Card>
  )
}

function CategoryInfo({ category }: { category: NonNullable<PageData['category']> }) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg flex items-center space-x-2">
          <Folder className="h-4 w-4" />
          <span>Category</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex items-center space-x-2">
          {category.color && (
            <div
              className="w-4 h-4 rounded-full"
              style={{ backgroundColor: category.color }}
            />
          )}
          <span className="font-medium">{category.name}</span>
        </div>
      </CardContent>
    </Card>
  )
}

// Utility functions
function calculateReadingTime(content: string): number {
  const wordsPerMinute = 200
  const words = content.replace(/<[^>]*>/g, '').split(/\s+/).length
  return Math.ceil(words / wordsPerMinute)
}

function generateTOC(content: string) {
  // Extract headings from HTML content
  const headingRegex = /<h([1-6])[^>]*>(.*?)<\/h[1-6]>/gi
  const headings = []
  let match

  while ((match = headingRegex.exec(content)) !== null) {
    const level = parseInt(match[1])
    const text = match[2].replace(/<[^>]*>/g, '')
    const id = text.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-|-$/g, '')
    
    headings.push({
      level,
      text,
      id,
    })
  }

  return headings
}