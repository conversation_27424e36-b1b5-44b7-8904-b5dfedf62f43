import fs from 'fs';
import path from 'path';
import { notFound } from 'next/navigation';
import Layout from '@/components/layout/Layout';
import ProjectBanner from '@/components/sections/project-details/ProjectBanner';
import ProjectAbout from '@/components/sections/project-details/ProjectAbout';
import ProjectFeatures from '@/components/sections/project-details/ProjectFeatures';
import ProjectTimeline from '@/components/sections/project-details/ProjectTimeline';
import ProjectGallery from '@/components/sections/project-details/ProjectGallery';
import ProjectTechnologies from '@/components/sections/project-details/ProjectTechnologies';
import type { Metadata } from 'next';

export async function generateStaticParams() {
  const dir = path.join(process.cwd(), 'src/data/projects');
  const files = fs.readdirSync(dir);
  return files.map((file) => ({ id: file.replace('.json', '') }));
}

export async function generateMetadata({ params }: { params: { id: string } }): Promise<Metadata> {
  try {
    const filePath = path.join(process.cwd(), 'src/data/projects', `${params.id}.json`);
    const projectData = JSON.parse(fs.readFileSync(filePath, 'utf-8'));

    return {
      title: `${projectData.pageBanner.pageTitle} | Motshwanelo IT Consulting`,
      description: projectData.pageBanner.description,
      openGraph: {
        title: projectData.pageBanner.pageTitle,
        description: projectData.pageBanner.description,
        type: 'website',
        url: `https://motshwaneloitconsulting.co.za/project/${params.id}`,
        images: projectData.about?.image?.src ? [projectData.about.image.src] : [],
      },
    };
  } catch (error) {
    return {
      title: 'Project Not Found | Motshwanelo IT Consulting',
      description: 'The requested project could not be found.',
    };
  }
}

export default function ProjectDetailsPage({ params }: { params: { id: string } }) {
  try {
    const filePath = path.join(process.cwd(), 'src/data/projects', `${params.id}.json`);
    const projectData = JSON.parse(fs.readFileSync(filePath, 'utf-8'));

    return (
      <Layout>
        <ProjectBanner banner={projectData.pageBanner} client={projectData.client} />
        <ProjectAbout about={projectData.about} />
        <ProjectFeatures features={projectData.features} />
        {projectData.timeline && <ProjectTimeline timeline={projectData.timeline} />}
        {projectData.technologies && <ProjectTechnologies technologies={projectData.technologies} />}
        {projectData.gallery && <ProjectGallery gallery={projectData.gallery} />}
      </Layout>
    );
  } catch (error) {
    notFound();
  }
}
