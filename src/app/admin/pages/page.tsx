'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { Plus, Search, Filter, MoreHorizontal, Eye, Edit, Trash2, Copy, FileText } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { useToast } from '@/hooks/use-toast'
import { format } from 'date-fns'
import { AdminDataTable, BulkActions, StatsCards } from '@/components/admin'

interface Page {
  id: string
  title: string
  slug: string
  template: string
  status: 'DRAFT' | 'PUBLISHED' | 'ARCHIVED'
  publishedAt?: Date
  createdAt: Date
  updatedAt: Date
  author: {
    id: string
    name: string
    image?: string
  }
  category?: {
    id: string
    name: string
    color?: string
  }
  _count: {
    views: number
    comments: number
  }
}

const MOCK_PAGES: Page[] = [
  {
    id: '1',
    title: 'Welcome to Our Platform',
    slug: 'welcome',
    template: 'landing',
    status: 'PUBLISHED',
    publishedAt: new Date('2024-01-15'),
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-15'),
    author: {
      id: '1',
      name: 'John Doe',
      image: '/avatars/john.jpg'
    },
    category: {
      id: '1',
      name: 'Marketing',
      color: '#3b82f6'
    },
    _count: {
      views: 1250,
      comments: 8
    }
  },
  {
    id: '2',
    title: 'About Our Company',
    slug: 'about',
    template: 'about',
    status: 'PUBLISHED',
    publishedAt: new Date('2024-01-12'),
    createdAt: new Date('2024-01-08'),
    updatedAt: new Date('2024-01-12'),
    author: {
      id: '2',
      name: 'Jane Smith',
      image: '/avatars/jane.jpg'
    },
    category: {
      id: '2',
      name: 'Company',
      color: '#10b981'
    },
    _count: {
      views: 890,
      comments: 3
    }
  },
  {
    id: '3',
    title: 'Contact Information',
    slug: 'contact',
    template: 'contact',
    status: 'DRAFT',
    createdAt: new Date('2024-01-20'),
    updatedAt: new Date('2024-01-20'),
    author: {
      id: '1',
      name: 'John Doe',
      image: '/avatars/john.jpg'
    },
    _count: {
      views: 0,
      comments: 0
    }
  }
]

export default function PagesAdminPage() {
  const router = useRouter()
  const { toast } = useToast()
  const [pages, setPages] = useState<Page[]>(MOCK_PAGES)
  const [filteredPages, setFilteredPages] = useState<Page[]>(MOCK_PAGES)
  const [selectedPages, setSelectedPages] = useState<string[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [templateFilter, setTemplateFilter] = useState<string>('all')
  const [loading, setLoading] = useState(false)

  // Filter pages based on search and filters
  useEffect(() => {
    let filtered = pages

    // Search filter
    if (searchQuery) {
      filtered = filtered.filter(page =>
        page.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        page.slug.toLowerCase().includes(searchQuery.toLowerCase()) ||
        page.author.name.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }

    // Status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(page => page.status === statusFilter)
    }

    // Template filter
    if (templateFilter !== 'all') {
      filtered = filtered.filter(page => page.template === templateFilter)
    }

    setFilteredPages(filtered)
  }, [pages, searchQuery, statusFilter, templateFilter])

  const handleCreatePage = () => {
    router.push('/admin/pages/new')
  }

  const handleEditPage = (pageId: string) => {
    router.push(`/admin/pages/${pageId}/edit`)
  }

  const handleViewPage = (slug: string) => {
    window.open(`/${slug}`, '_blank')
  }

  const handleDuplicatePage = async (pageId: string) => {
    try {
      setLoading(true)
      // API call to duplicate page
      toast({
        title: 'Page duplicated',
        description: 'The page has been successfully duplicated.',
      })
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to duplicate page.',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  const handleDeletePage = async (pageId: string) => {
    try {
      setLoading(true)
      setPages(pages.filter(page => page.id !== pageId))
      toast({
        title: 'Page deleted',
        description: 'The page has been successfully deleted.',
      })
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to delete page.',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  const handleBulkAction = async (action: string, pageIds: string[]) => {
    try {
      setLoading(true)

      switch (action) {
        case 'publish':
          setPages(pages.map(page =>
            pageIds.includes(page.id)
              ? { ...page, status: 'PUBLISHED' as const, publishedAt: new Date() }
              : page
          ))
          break
        case 'draft':
          setPages(pages.map(page =>
            pageIds.includes(page.id)
              ? { ...page, status: 'DRAFT' as const }
              : page
          ))
          break
        case 'archive':
          setPages(pages.map(page =>
            pageIds.includes(page.id)
              ? { ...page, status: 'ARCHIVED' as const }
              : page
          ))
          break
        case 'delete':
          setPages(pages.filter(page => !pageIds.includes(page.id)))
          break
      }

      setSelectedPages([])
      toast({
        title: 'Bulk action completed',
        description: `Successfully ${action}ed ${pageIds.length} page(s).`,
      })
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to perform bulk action.',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  const getStatusBadge = (status: Page['status']) => {
    const variants = {
      PUBLISHED: 'default',
      DRAFT: 'secondary',
      ARCHIVED: 'outline'
    } as const

    const colors = {
      PUBLISHED: 'text-green-700 bg-green-50 border-green-200',
      DRAFT: 'text-yellow-700 bg-yellow-50 border-yellow-200',
      ARCHIVED: 'text-gray-700 bg-gray-50 border-gray-200'
    }

    return (
      <Badge variant={variants[status]} className={colors[status]}>
        {status.toLowerCase()}
      </Badge>
    )
  }

  const getTemplateBadge = (template: string) => {
    const colors = {
      default: 'bg-blue-50 text-blue-700',
      landing: 'bg-purple-50 text-purple-700',
      about: 'bg-green-50 text-green-700',
      contact: 'bg-orange-50 text-orange-700',
      blocks: 'bg-pink-50 text-pink-700'
    }

    return (
      <Badge variant="outline" className={colors[template as keyof typeof colors] || colors.default}>
        {template}
      </Badge>
    )
  }

  const columns = [
    {
      id: 'select',
      header: ({ table }: any) => (
        <input
          type="checkbox"
          checked={table.getIsAllPageRowsSelected()}
          onChange={(e) => {
            if (e.target.checked) {
              setSelectedPages(filteredPages.map(page => page.id))
            } else {
              setSelectedPages([])
            }
          }}
          className="rounded border-gray-300"
        />
      ),
      cell: ({ row }: any) => (
        <input
          type="checkbox"
          checked={selectedPages.includes(row.original.id)}
          onChange={(e) => {
            if (e.target.checked) {
              setSelectedPages([...selectedPages, row.original.id])
            } else {
              setSelectedPages(selectedPages.filter(id => id !== row.original.id))
            }
          }}
          className="rounded border-gray-300"
        />
      ),
    },
    {
      accessorKey: 'title',
      header: 'Title',
      cell: ({ row }: any) => {
        const page = row.original
        return (
          <div className="flex items-center space-x-3">
            <FileText className="h-4 w-4 text-muted-foreground" />
            <div className="space-y-1">
              <div className="font-medium">{page.title}</div>
              <div className="text-sm text-muted-foreground">/{page.slug}</div>
            </div>
          </div>
        )
      },
    },
    {
      accessorKey: 'author',
      header: 'Author',
      cell: ({ row }: any) => {
        const author = row.original.author
        return (
          <div className="flex items-center space-x-2">
            <Avatar className="h-6 w-6">
              <AvatarImage src={author.image} alt={author.name} />
              <AvatarFallback className="text-xs">
                {author.name.split(' ').map((n: string) => n[0]).join('')}
              </AvatarFallback>
            </Avatar>
            <span className="text-sm">{author.name}</span>
          </div>
        )
      },
    },
    {
      accessorKey: 'template',
      header: 'Template',
      cell: ({ row }: any) => getTemplateBadge(row.original.template),
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ row }: any) => getStatusBadge(row.original.status),
    },
    {
      accessorKey: 'category',
      header: 'Category',
      cell: ({ row }: any) => {
        const category = row.original.category
        return category ? (
          <Badge
            variant="outline"
            style={{
              borderColor: category.color,
              color: category.color
            }}
          >
            {category.name}
          </Badge>
        ) : (
          <span className="text-muted-foreground">—</span>
        )
      },
    },
    {
      accessorKey: 'stats',
      header: 'Stats',
      cell: ({ row }: any) => {
        const stats = row.original._count
        return (
          <div className="text-sm text-muted-foreground">
            <div>{stats.views} views</div>
            <div>{stats.comments} comments</div>
          </div>
        )
      },
    },
    {
      accessorKey: 'updatedAt',
      header: 'Last Modified',
      cell: ({ row }: any) => (
        <div className="text-sm text-muted-foreground">
          {format(row.original.updatedAt, 'MMM dd, yyyy')}
        </div>
      ),
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }: any) => {
        const page = row.original
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => handleViewPage(page.slug)}>
                <Eye className="mr-2 h-4 w-4" />
                View Page
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleEditPage(page.id)}>
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleDuplicatePage(page.id)}>
                <Copy className="mr-2 h-4 w-4" />
                Duplicate
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => handleDeletePage(page.id)}
                className="text-destructive"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )
      },
    },
  ]

  const stats = [
    {
      title: 'Total Pages',
      value: pages.length.toString(),
      description: 'All pages in the system',
      trend: { value: 12, isPositive: true }
    },
    {
      title: 'Published',
      value: pages.filter(p => p.status === 'PUBLISHED').length.toString(),
      description: 'Live pages',
      trend: { value: 8, isPositive: true }
    },
    {
      title: 'Drafts',
      value: pages.filter(p => p.status === 'DRAFT').length.toString(),
      description: 'Unpublished pages',
      trend: { value: 3, isPositive: false }
    },
    {
      title: 'Total Views',
      value: pages.reduce((sum, p) => sum + p._count.views, 0).toLocaleString(),
      description: 'All-time page views',
      trend: { value: 15, isPositive: true }
    }
  ]

  const bulkActions = [
    { value: 'publish', label: 'Publish' },
    { value: 'draft', label: 'Move to Draft' },
    { value: 'archive', label: 'Archive' },
    { value: 'delete', label: 'Delete', destructive: true }
  ]

  return (
    <div className="space-y-6">
      {/* Stats */}
      <StatsCards stats={stats} className="py-2 px-4"/>

      {/* Filters and Search */}
      <Card className="border-none">
        <CardHeader>
          <CardTitle>Pages</CardTitle>
          <CardDescription>
            A list of all pages in your website
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Search pages..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="PUBLISHED">Published</SelectItem>
                <SelectItem value="DRAFT">Draft</SelectItem>
                <SelectItem value="ARCHIVED">Archived</SelectItem>
              </SelectContent>
            </Select>

            <Select value={templateFilter} onValueChange={setTemplateFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Template" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Templates</SelectItem>
                <SelectItem value="default">Default</SelectItem>
                <SelectItem value="landing">Landing</SelectItem>
                <SelectItem value="about">About</SelectItem>
                <SelectItem value="contact">Contact</SelectItem>
                <SelectItem value="blocks">Blocks</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Data Table */}
          <AdminDataTable
            columns={columns}
            data={filteredPages}
            isLoading={loading}
          />
        </CardContent>
      </Card>
    </div>
  )
}
