/* Header Styles for Motshwanelo Brand */

/* Enhanced Header Area Styles */
.header-area {
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(3, 39, 110, 0.1);
}

.header-area.sticky {
  background: rgba(255, 255, 255, 0.98);
  box-shadow: 0 2px 20px rgba(3, 39, 110, 0.1);
}

/* Logo Consistency */
.site-logo img {
  max-height: 50px;
  width: auto;
  transition: all 0.3s ease;
}

.sticky .site-logo img {
  max-height: 45px;
}

/* Main Navigation Styles */
.main-nav {
  display: flex;
  gap: 10px;
}

.main-nav li {
  position: relative;
}

.main-nav li a {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 12px 18px;
  font-weight: 500;
  transition: all 0.3s ease;
  border-radius: 6px;
  color: #03276e;
  font-size: 16px;
}

.main-nav li a:hover {
  background-color: rgba(3, 39, 110, 0.08);
  color: #e89d1a;
  transform: translateY(-1px);
}

.main-nav li a.active {
  background-color: rgba(3, 39, 110, 0.1);
  color: #e89d1a;
}

.main-nav li a .nav-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  color: #03276e;
  margin-right: 4px;
}

/* Experience Menu Styles */
.experience-menu {
  width: 800px;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border-top: 3px solid #03276e;
}

.experience-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin-bottom: 30px;
}

.experience-card {
  padding: 25px;
  border-radius: 10px;
  transition: all 0.3s ease;
}

.experience-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
}

.experience-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
  font-size: 24px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.experience-card h4 {
  font-size: 18px;
  margin-bottom: 10px;
  color: #333;
  font-weight: 600;
}

.experience-card p {
  font-size: 14px;
  color: #6F6F87;
  margin-bottom: 20px;
}

.exp-link {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #03276e;
  font-weight: 500;
  font-size: 14px;
  transition: all 0.3s ease;
}

.exp-link:hover {
  gap: 12px;
  color: #e89d1a;
}

.quick-links {
  background-color: #f9fafc;
  padding: 20px;
  border-radius: 10px;
}

.quick-links h4 {
  font-size: 16px;
  margin-bottom: 15px;
  color: #333;
  font-weight: 600;
}

.quick-links-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 15px;
}

.quick-link-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 15px;
  background-color: white;
  border-radius: 8px;
  transition: all 0.3s ease;
  text-align: center;
}

.quick-link-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  color: #03276e;
}

.quick-link-item i {
  font-size: 24px;
  margin-bottom: 8px;
  color: #e89d1a;
}

.quick-link-item span {
  font-size: 14px;
  font-weight: 500;
}

/* Services Menu Styles */
.services-mega-menu {
  width: 900px;
  padding: 0;
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border-top: 3px solid #03276e;
  overflow: hidden;
}

.services-menu-tabs {
  height: 100%;
  background-color: #f9fafc;
  padding: 30px;
}

.services-menu-tabs-header {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.tab-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 15px;
  background-color: transparent;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  color: #6F6F87;
}

.tab-btn:hover {
  background-color: rgba(3, 39, 110, 0.05);
}

.tab-btn.active {
  background-color: #03276e;
  color: white;
}

.tab-btn i {
  font-size: 16px;
}

.services-menu-tabs-content {
  position: relative;
}

.tab-content {
  display: none;
  opacity: 0;
  transition: all 0.3s ease;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
}

.tab-content.active {
  display: block;
  opacity: 1;
  position: relative;
}

.service-category-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.service-category-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background-color: white;
  border-radius: 8px;
  transition: all 0.3s ease;
  position: relative;
}

.service-category-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.service-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background-color: rgba(3, 39, 110, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #03276e;
  font-size: 18px;
  flex-shrink: 0;
}

.service-info {
  flex-grow: 1;
}

.service-info h4 {
  font-size: 15px;
  margin-bottom: 5px;
  color: #333;
  font-weight: 600;
}

.service-info p {
  font-size: 12px;
  color: #6F6F87;
  margin: 0;
}

.arrow-icon {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #e89d1a;
  opacity: 0;
  transition: all 0.3s ease;
}

.service-category-item:hover .arrow-icon {
  opacity: 1;
  right: 10px;
}

.service-category-item.view-all {
  background-color: rgba(232, 157, 26, 0.05);
}

.service-category-item.view-all .service-icon {
  background-color: rgba(232, 157, 26, 0.2);
  color: #e89d1a;
}

.services-menu-showcase {
  background-color: white;
  padding: 30px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.showcase-header {
  margin-bottom: 30px;
}

.showcase-header h3 {
  font-size: 20px;
  margin-bottom: 10px;
  color: #03276e;
  font-weight: 600;
}

.showcase-header p {
  font-size: 14px;
  color: #6F6F87;
}

.showcase-features {
  margin-bottom: 30px;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  margin-bottom: 20px;
}

.feature-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(3, 39, 110, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #03276e;
  font-size: 18px;
  flex-shrink: 0;
}

.feature-info h4 {
  font-size: 16px;
  margin-bottom: 5px;
  color: #333;
  font-weight: 600;
}

.feature-info p {
  font-size: 14px;
  color: #6F6F87;
  margin: 0;
}

.showcase-cta {
  margin-top: auto;
  display: flex;
  gap: 15px;
}

.showcase-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 20px;
  background-color: #03276e;
  color: white;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
  flex: 1;
}

.showcase-btn:hover {
  background-color: #021d4e;
  transform: translateY(-3px);
}

.showcase-btn.outline {
  background-color: transparent;
  border: 1px solid #03276e;
  color: #03276e;
}

.showcase-btn.outline:hover {
  background-color: rgba(3, 39, 110, 0.05);
}

/* Products Menu Styles */
.products-submenu {
  width: 400px;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border-top: 3px solid #e89d1a;
  background-color: white;
}

.product-card {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  background-color: rgba(3, 39, 110, 0.05);
  border-radius: 10px;
  margin-bottom: 30px;
  transition: all 0.3s ease;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
}

.product-icon {
  width: 60px;
  height: 60px;
  border-radius: 10px;
  background-color: #03276e;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  flex-shrink: 0;
}

.product-info {
  flex-grow: 1;
}

.product-info h4 {
  font-size: 18px;
  margin-bottom: 5px;
  color: #333;
  font-weight: 600;
}

.product-info p {
  font-size: 14px;
  color: #6F6F87;
  margin-bottom: 15px;
}

.product-link {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #03276e;
  font-weight: 500;
  font-size: 14px;
  transition: all 0.3s ease;
}

.product-link:hover {
  gap: 12px;
  color: #e89d1a;
}

.coming-soon-products h4 {
  font-size: 16px;
  margin-bottom: 15px;
  color: #333;
  font-weight: 600;
}

.coming-soon-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
}

.coming-soon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px;
  background-color: #f9fafc;
  border-radius: 8px;
  transition: all 0.3s ease;
  text-align: center;
}

.coming-soon-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.cs-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background-color: rgba(232, 157, 26, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #e89d1a;
  font-size: 18px;
  margin-bottom: 10px;
}

.coming-soon-item span {
  font-size: 14px;
  font-weight: 500;
  color: #6F6F87;
}

/* Technology Menu Styles */
.tech-submenu {
  width: 800px;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border-top: 3px solid #030376;
  background-color: white;
}

.tech-menu-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-bottom: 30px;
}

.tech-menu-item {
  display: flex;
  flex-direction: column;
  padding: 20px;
  background-color: #f9fafc;
  border-radius: 10px;
  transition: all 0.3s ease;
}

.tech-menu-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
}

.tech-menu-icon {
  width: 60px;
  height: 60px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  margin-bottom: 15px;
}

.tech-menu-item h4 {
  font-size: 18px;
  margin-bottom: 10px;
  color: #333;
  font-weight: 600;
}

.tech-menu-item p {
  font-size: 14px;
  color: #6F6F87;
  margin: 0;
}

.tech-partners {
  background-color: #f9fafc;
  padding: 20px;
  border-radius: 10px;
}

.tech-partners h4 {
  font-size: 16px;
  margin-bottom: 15px;
  color: #333;
  font-weight: 600;
  text-align: center;
}

.partners-logos {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}

.partner-logo {
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.7;
  transition: all 0.3s ease;
}

.partner-logo:hover {
  opacity: 1;
}

.partner-logo img {
  max-height: 100%;
  max-width: 100%;
}

/* Contact Menu Styles */
.contact-submenu {
  width: 400px;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border-top: 3px solid #e89d1a;
  background-color: white;
}

.contact-info h4 {
  font-size: 18px;
  margin-bottom: 20px;
  color: #333;
  font-weight: 600;
  text-align: center;
}

.contact-methods {
  margin-bottom: 30px;
}

.contact-method {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
}

.contact-method i {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(3, 39, 110, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #03276e;
  font-size: 18px;
  flex-shrink: 0;
}

.contact-method h5 {
  font-size: 14px;
  margin-bottom: 5px;
  color: #333;
  font-weight: 600;
}

.contact-method p {
  font-size: 14px;
  color: #6F6F87;
  margin: 0;
}

.contact-links {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
  margin-bottom: 30px;
}

.contact-link-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px;
  background-color: #f9fafc;
  border-radius: 8px;
  transition: all 0.3s ease;
  text-align: center;
}

.contact-link-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  color: #03276e;
}

.contact-link-item i {
  font-size: 24px;
  margin-bottom: 8px;
  color: #e89d1a;
}

.contact-link-item span {
  font-size: 14px;
  font-weight: 500;
}

.social-links {
  display: flex;
  justify-content: center;
  gap: 15px;
}

.social-link {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #f9fafc;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6F6F87;
  font-size: 18px;
  transition: all 0.3s ease;
}

.social-link:hover {
  background-color: #03276e;
  color: white;
  transform: translateY(-3px);
}
.header-area {
  box-shadow: 0 2px 15px rgba(3, 39, 110, 0.1);
  transition: all 0.3s ease;
}

.header-area.sticky {
  box-shadow: 0 5px 20px rgba(3, 39, 110, 0.15);
}

.site-logo img {
  max-height: 48px;
  transition: all 0.3s ease;
}

.sticky .site-logo img {
  max-height: 40px;
}

.theme-btn1 {
  border-radius: 4px;
  padding: 10px 20px;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.theme-btn1:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Enhanced Mobile Header Styles */
.mobile-header {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 15px rgba(3, 39, 110, 0.1);
  border-bottom: 1px solid rgba(3, 39, 110, 0.1);
  position: sticky;
  top: 0;
  z-index: 999;
}

.mobile-header-elements {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0;
}

.mobile-logo img {
  max-height: 42px;
  width: auto;
  transition: all 0.3s ease;
}

.mobile-nav-icon {
  color: #03276e;
  font-size: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 8px;
  border-radius: 6px;
  background: rgba(3, 39, 110, 0.05);
}

.mobile-nav-icon:hover {
  color: #e89d1a;
  background: rgba(232, 157, 26, 0.1);
  transform: scale(1.05);
}

/* Mobile Menu Improvements */
.mobile-sidebar {
  background: linear-gradient(135deg, #03276e 0%, #1a4480 100%);
  box-shadow: 0 0 30px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
}

.mobile-sidebar .logo-m {
  padding: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 20px;
}

.mobile-sidebar .logo-m img {
  max-height: 45px;
  filter: brightness(0) invert(1);
}

.mobile-nav ul li a {
  color: rgba(255, 255, 255, 0.9);
  padding: 15px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
  font-weight: 500;
}

.mobile-nav ul li a:hover {
  background: rgba(232, 157, 26, 0.1);
  color: #e89d1a;
  padding-left: 30px;
}

.mobile-menu-overlay {
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(5px);
}

/* Enhanced Dropdown Menu Styles */
.dropdown-menu-parrent {
  position: relative;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(3, 39, 110, 0.08);
  padding: 12px 0;
  min-width: 280px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-15px) scale(0.95);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1000;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.98);
}

.dropdown-menu-parrent:hover .dropdown-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0) scale(1);
}

.dropdown-menu::before {
  content: '';
  position: absolute;
  top: -8px;
  left: 20px;
  width: 16px;
  height: 16px;
  background: white;
  border: 1px solid rgba(3, 39, 110, 0.08);
  border-bottom: none;
  border-right: none;
  transform: rotate(45deg);
  z-index: -1;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 14px 24px;
  color: #333;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 8px;
  margin: 2px 12px;
  font-weight: 500;
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.dropdown-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(3, 39, 110, 0.05), transparent);
  transition: left 0.5s ease;
}

.dropdown-item:hover::before {
  left: 100%;
}

.dropdown-item:hover {
  background: linear-gradient(135deg, rgba(3, 39, 110, 0.08) 0%, rgba(232, 157, 26, 0.08) 100%);
  color: #e89d1a;
  transform: translateX(8px);
  box-shadow: 0 4px 12px rgba(232, 157, 26, 0.2);
}

.dropdown-item .nav-icon {
  color: #03276e;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  transition: all 0.3s ease;
}

.dropdown-item:hover .nav-icon {
  color: #e89d1a;
  transform: scale(1.1);
}

/* Dropdown Separator */
.dropdown-separator {
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(3, 39, 110, 0.1), transparent);
  margin: 8px 20px;
}

/* Dropdown Header */
.dropdown-header {
  padding: 12px 24px 8px;
  font-size: 12px;
  font-weight: 700;
  color: #03276e;
  text-transform: uppercase;
  letter-spacing: 1px;
  border-bottom: 1px solid rgba(3, 39, 110, 0.1);
  margin-bottom: 8px;
}

/* Header Button Improvements */
.header2-buttons .button .theme-btn1,
.header2-buttons .button .theme-btn12,
.header2-buttons .button .theme-btn14,
.header2-buttons .button .theme-btn16 {
  background: linear-gradient(135deg, #03276e 0%, #e89d1a 100%);
  color: white;
  padding: 12px 24px;
  border-radius: 6px;
  font-weight: 600;
  transition: all 0.3s ease;
  border: none;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.header2-buttons .button .theme-btn1:hover,
.header2-buttons .button .theme-btn12:hover,
.header2-buttons .button .theme-btn14:hover,
.header2-buttons .button .theme-btn16:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(3, 39, 110, 0.3);
  background: linear-gradient(135deg, #e89d1a 0%, #03276e 100%);
}

/* Responsive Design Improvements */
@media (max-width: 1199px) {
  .main-nav li a {
    padding: 10px 14px;
    font-size: 15px;
  }

  .site-logo img {
    max-height: 45px;
  }
}

@media (max-width: 991px) {
  .header-area {
    padding: 8px 0;
  }

  .mobile-header-elements {
    padding: 10px 0;
  }

  .mobile-logo img {
    max-height: 38px;
  }
}

@media (max-width: 767px) {
  .mobile-sidebar {
    width: 100%;
    max-width: 320px;
  }

  .mobile-nav ul li a {
    padding: 12px 16px;
    font-size: 15px;
  }

  .experience-menu {
    width: 100%;
    max-width: 300px;
    padding: 20px;
  }
}

/* Animation Improvements */
@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.header-area.sticky {
  animation: slideInFromTop 0.3s ease-out;
}

/* Focus States for Accessibility */
.main-nav li a:focus,
.dropdown-item:focus,
.mobile-nav-icon:focus {
  outline: 2px solid #e89d1a;
  outline-offset: 2px;
}

/* Loading States */
.header-loading {
  opacity: 0.7;
  pointer-events: none;
}

.header-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Enhanced Dropdown Styles */
.dropdown-menu {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(3, 39, 110, 0.1);
  border-radius: 8px;
  box-shadow: 0 10px 40px rgba(3, 39, 110, 0.15);
  padding: 8px 0;
  min-width: 220px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
}

.dropdown-menu-parrent:hover .dropdown-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  color: #03276e;
  font-weight: 500;
  transition: all 0.3s ease;
  border-radius: 6px;
  margin: 2px 8px;
}

.dropdown-item:hover {
  background: rgba(3, 39, 110, 0.08);
  color: #e89d1a;
  transform: translateX(4px);
}

.dropdown-item .nav-icon {
  color: #03276e;
  width: 20px;
  text-align: center;
}

.dropdown-item:hover .nav-icon {
  color: #e89d1a;
}

/* Responsive Improvements */
@media (max-width: 991px) {
  .main-nav {
    display: none;
  }

  .mobile-header {
    display: block !important;
  }
}

@media (min-width: 992px) {
  .mobile-header {
    display: none !important;
  }

  .main-nav {
    display: flex;
  }
}

/* Main menu styling */
.main-menu-ex ul li a {
  color: #333;
  font-weight: 500;
  transition: all 0.3s ease;
}

.main-menu-ex ul li a:hover,
.main-menu-ex ul li.active > a {
  color: #03276e;
}

.main-menu-ex ul li.has-dropdown > a::after {
  color: #e89d1a;
}

.main-menu-ex ul li .submenu {
  border-top: 3px solid #e89d1a;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.main-menu-ex ul li .submenu li a:hover {
  background-color: rgba(3, 39, 110, 0.05);
  color: #03276e;
}

/* Mobile menu styling */
.mobile-menu {
  background-color: #fff;
}

.mobile-menu .mobile-menu-close {
  color: #03276e;
}

.mobile-menu .mobile-menu-close:hover {
  color: #e89d1a;
}

.mobile-menu .mobile-menu-logo img {
  max-height: 40px;
}

.mobile-menu ul li a {
  color: #333;
  font-weight: 500;
}

.mobile-menu ul li a:hover,
.mobile-menu ul li.active > a {
  color: #03276e;
}

.mobile-menu ul li.has-dropdown .dropdown-icon {
  color: #e89d1a;
}

/* Success Stories Section Styles */
.success-stories-nav {
  background-color: #f9fafc;
  border-radius: 10px;
  padding: 30px;
  height: 100%;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.success-stories-nav h3 {
  color: #03276e;
  font-weight: 600;
  margin-bottom: 20px;
  border-bottom: 2px solid #e89d1a;
  padding-bottom: 15px;
}

.custom-tabs {
  list-style: none;
  padding: 0;
}

.custom-tabs .nav-item {
  margin-bottom: 15px;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
}

.custom-tabs .nav-item:hover {
  background-color: rgba(3, 39, 110, 0.05);
}

.custom-tabs .nav-item.active {
  background-color: rgba(3, 39, 110, 0.1);
  border-left: 3px solid #03276e;
}

.story-tab-item {
  padding: 15px;
}

.story-tab-item h4 {
  font-size: 16px;
  margin: 8px 0 0 0;
  color: #333;
}

.category-badge {
  display: inline-block;
  background-color: #e89d1a;
  color: white;
  font-size: 12px;
  padding: 4px 10px;
  border-radius: 20px;
}

.success-story-content {
  background-color: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
  height: 100%;
}

.story-image {
  position: relative;
  height: 250px;
  overflow: hidden;
}

.story-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.category-overlay {
  position: absolute;
  bottom: 20px;
  left: 20px;
  background-color: #03276e;
  color: white;
  padding: 8px 15px;
  border-radius: 5px;
  font-weight: 500;
}

.story-details {
  padding: 30px;
}

.story-details h3 {
  color: #03276e;
  margin-bottom: 20px;
  font-weight: 600;
}

.story-section {
  margin-bottom: 25px;
}

.story-section h4 {
  color: #6F6F87;
  font-size: 18px;
  margin-bottom: 10px;
  font-weight: 500;
}

.results-list {
  list-style: none;
  padding: 0;
}

.results-list li {
  display: flex;
  align-items: flex-start;
  margin-bottom: 10px;
}

.results-list li i {
  color: #e89d1a;
  margin-right: 10px;
  margin-top: 5px;
}

.testimonial-box {
  background-color: #f9fafc;
  border-left: 4px solid #e89d1a;
  padding: 20px;
  margin: 30px 0;
  position: relative;
}

.quote-icon {
  position: absolute;
  top: -15px;
  left: 20px;
  background-color: #e89d1a;
  color: white;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.quote-text {
  font-style: italic;
  margin-bottom: 15px;
}

.author-name {
  font-weight: 600;
  margin-bottom: 0;
}

.author-position {
  color: #6F6F87;
  font-size: 14px;
}

.story-cta {
  margin-top: 30px;
  text-align: center;
}

/* Services Explorer Styles */
.services-explorer-container {
  margin-top: 50px;
}

.services-categories {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  margin-bottom: 40px;
}

.category-item {
  flex: 1;
  min-width: 150px;
  text-align: center;
  padding: 20px 15px;
  cursor: pointer;
  border-radius: 10px;
  transition: all 0.3s ease;
  margin: 0 10px 20px;
  background-color: white;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.category-item:hover {
  transform: translateY(-5px);
}

.category-item.active {
  background-color: #f9fafc;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.category-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 15px;
  color: white;
  font-size: 24px;
}

.category-item h4 {
  font-size: 16px;
  margin: 0;
  color: #333;
}

.services-content-wrapper {
  display: flex;
  background-color: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
}

.services-list {
  width: 30%;
  background-color: #f9fafc;
  padding: 30px;
  border-right: 1px solid #ededed;
}

.services-list h3 {
  color: #03276e;
  font-weight: 600;
  margin-bottom: 20px;
}

.services-list ul {
  list-style: none;
  padding: 0;
}

.service-list-item {
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 10px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s ease;
}

.service-list-item:hover {
  background-color: rgba(3, 39, 110, 0.05);
}

.service-list-item.active {
  background-color: #03276e;
  color: white;
}

.service-list-item h4 {
  font-size: 16px;
  margin: 0;
}

.service-details {
  width: 70%;
  padding: 30px;
  transition: opacity 0.3s ease;
}

.service-details.fade-out {
  opacity: 0;
}

.service-details.fade-in {
  opacity: 1;
}

.service-image {
  margin-bottom: 25px;
  height: 250px;
  overflow: hidden;
  border-radius: 8px;
}

.service-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.service-info h3 {
  color: #03276e;
  margin-bottom: 15px;
  font-weight: 600;
}

.service-features {
  margin: 25px 0;
}

.service-features h4 {
  color: #6F6F87;
  font-size: 18px;
  margin-bottom: 15px;
  font-weight: 500;
}

.service-features ul {
  list-style: none;
  padding: 0;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.service-features li {
  display: flex;
  align-items: center;
}

.service-features li i {
  color: #e89d1a;
  margin-right: 10px;
}

/* Technology Stack Styles */
.tech-stack-section {
  background-color: #f9fafc;
}

.tech-categories {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 30px;
}

.tech-category {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px 25px;
  background-color: white;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.tech-category:hover {
  transform: translateY(-5px);
}

.tech-category.active {
  background-color: #03276e;
}

.tech-category.active h4 {
  color: white;
}

.tech-category.active .category-icon {
  background-color: white;
  color: #03276e;
}

.tech-category .category-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #03276e;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  font-size: 20px;
}

.tech-category h4 {
  font-size: 16px;
  margin: 0;
  color: #333;
}

.tech-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 25px;
  margin-top: 30px;
}

.tech-card {
  background-color: white;
  border-radius: 10px;
  padding: 25px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: all 0.5s ease;
  opacity: 0;
  transform: translateY(20px);
}

.tech-card.visible {
  opacity: 1;
  transform: translateY(0);
}

.tech-logo {
  width: 80px;
  height: 80px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tech-logo img {
  max-width: 100%;
  max-height: 100%;
}

.tech-info h4 {
  color: #03276e;
  margin-bottom: 10px;
  font-weight: 600;
}

.tech-info p {
  color: #6F6F87;
  font-size: 14px;
  margin: 0;
}

/* Responsive Adjustments */
@media (max-width: 992px) {
  .services-content-wrapper {
    flex-direction: column;
  }
  
  .services-list, .service-details {
    width: 100%;
  }
  
  .services-list {
    border-right: none;
    border-bottom: 1px solid #ededed;
  }
  
  .service-features ul {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .category-item {
    min-width: 120px;
  }
  
  .tech-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  }
}

@media (max-width: 576px) {
  .tech-categories {
    flex-direction: column;
    align-items: center;
  }
  
  .tech-category {
    width: 80%;
  }
}