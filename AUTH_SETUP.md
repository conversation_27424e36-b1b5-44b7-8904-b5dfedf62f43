# AuthJS (NextAuth v5) Setup Guide

This project has been configured with AuthJS (NextAuth v5) for comprehensive authentication. Here's everything you need to know to get it working.

## 🚀 Features Implemented

- **Multiple Authentication Methods:**
  - Email/Password (Credentials)
  - Google OAuth
  - GitHub OAuth

- **User Management:**
  - User registration and login
  - Role-based access control (ADMIN, EDITOR, AUTHOR, USER)
  - Password hashing with bcrypt

- **Security Features:**
  - Protected routes with middleware
  - Role-based component rendering
  - Secure session management

- **Database Integration:**
  - Prisma ORM with PostgreSQL
  - Complete user and session management
  - CMS-ready schema

## 📁 Files Created

### Core Authentication Files
- `auth.ts` - Main NextAuth configuration
- `middleware.ts` - Route protection middleware
- `src/types/next-auth.d.ts` - TypeScript declarations

### API Routes
- `src/app/api/auth/[...nextauth]/route.ts` - NextAuth API handler
- `src/app/api/auth/register/route.ts` - User registration endpoint

### Authentication Pages
- `src/app/auth/signin/page.tsx` - Sign-in page
- `src/app/auth/signup/page.tsx` - Sign-up page
- `src/app/auth/error/page.tsx` - Error handling page

### Components
- `src/components/auth/auth-buttons.tsx` - Sign-in/out buttons and user nav
- `src/components/auth/role-gate.tsx` - Role-based content protection
- `src/components/providers/session-provider.tsx` - Session provider wrapper
- `src/components/ui/icons.tsx` - OAuth provider icons

### Utilities
- `src/lib/auth.ts` - Server-side auth utilities
- `src/hooks/use-auth.ts` - Client-side auth hooks

## 🔧 Setup Instructions

### 1. Environment Variables

Update your `.env.local` file with the following variables:

```env
# Database
DATABASE_URL="postgresql://postgres@localhost:5432/motshwanelo-itc?schema=public"

# NextAuth
AUTH_SECRET="your-generated-secret"
NEXTAUTH_URL="http://localhost:3000"

# Google OAuth (optional)
AUTH_GOOGLE_ID="your-google-client-id"
AUTH_GOOGLE_SECRET="your-google-client-secret"

# GitHub OAuth (optional)
AUTH_GITHUB_ID="your-github-client-id"
AUTH_GITHUB_SECRET="your-github-client-secret"
```

### 2. OAuth Provider Setup

#### Google OAuth Setup
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable Google+ API
4. Go to Credentials → Create Credentials → OAuth 2.0 Client ID
5. Set authorized redirect URI: `http://localhost:3000/api/auth/callback/google`
6. Copy Client ID and Client Secret to your `.env.local`

#### GitHub OAuth Setup
1. Go to GitHub Settings → Developer settings → OAuth Apps
2. Click "New OAuth App"
3. Set Authorization callback URL: `http://localhost:3000/api/auth/callback/github`
4. Copy Client ID and Client Secret to your `.env.local`

### 3. Database Migration

The database has already been migrated with the auth tables. If you need to reset:

```bash
npx prisma migrate reset
npx prisma migrate dev --name init
```

### 4. Wrap Your App with Session Provider

Update your root layout to include the session provider:

```tsx
// src/app/layout.tsx
import { AuthProvider } from "@/components/providers/session-provider"

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body>
        <AuthProvider>
          {children}
        </AuthProvider>
      </body>
    </html>
  )
}
```

## 🎯 Usage Examples

### Server-Side Authentication

```tsx
// In a server component
import { getCurrentUser, requireAuth, requireAdmin } from "@/lib/auth"

export default async function ProtectedPage() {
  const user = await requireAuth() // Redirects if not authenticated
  
  return <div>Hello {user.name}!</div>
}

// Admin-only page
export default async function AdminPage() {
  const user = await requireAdmin() // Redirects if not admin
  
  return <div>Admin Panel</div>
}
```

### Client-Side Authentication

```tsx
// In a client component
"use client"
import { useCurrentUser, useIsAdmin } from "@/hooks/use-auth"
import { UserNav } from "@/components/auth/auth-buttons"

export default function Header() {
  const user = useCurrentUser()
  const isAdmin = useIsAdmin()
  
  return (
    <header>
      {user ? (
        <div>
          <span>Welcome, {user.name}!</span>
          {isAdmin && <span>Admin</span>}
          <UserNav />
        </div>
      ) : (
        <SignInButton />
      )}
    </header>
  )
}
```

### Role-Based Content Protection

```tsx
import { AdminGate, EditorGate } from "@/components/auth/role-gate"

export default function Dashboard() {
  return (
    <div>
      <h1>Dashboard</h1>
      
      <AdminGate fallback={<p>Admin access required</p>}>
        <AdminPanel />
      </AdminGate>
      
      <EditorGate>
        <EditorTools />
      </EditorGate>
    </div>
  )
}
```

### API Route Protection

```tsx
// src/app/api/admin/route.ts
import { auth } from "@/auth"
import { NextResponse } from "next/server"

export async function GET() {
  const session = await auth()
  
  if (!session?.user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }
  
  if (session.user.role !== "ADMIN") {
    return NextResponse.json({ error: "Forbidden" }, { status: 403 })
  }
  
  return NextResponse.json({ message: "Admin data" })
}
```

## 🔐 User Roles

The system supports four user roles:

- **USER** - Default role for new users
- **AUTHOR** - Can create and manage their own content
- **EDITOR** - Can manage content from multiple authors
- **ADMIN** - Full system access

## 🚦 Protected Routes

The middleware automatically protects these routes:

- `/admin/*` - Admin only
- `/api/admin/*` - Admin API routes
- `/api/user/*` - Authenticated users only

## 🧪 Testing

To test the authentication:

1. Start your development server: `npm run dev`
2. Visit `http://localhost:3000/auth/signin`
3. Try different authentication methods:
   - Create an account with email/password
   - Sign in with Google (if configured)
   - Sign in with GitHub (if configured)

## 🔧 Customization

### Adding New OAuth Providers

1. Install the provider package
2. Add provider configuration to `auth.ts`
3. Add environment variables
4. Update the sign-in page UI

### Modifying User Roles

1. Update the `UserRole` enum in `prisma/schema.prisma`
2. Run `npx prisma migrate dev`
3. Update role-based components as needed

### Custom Sign-in Pages

The sign-in pages are fully customizable. Modify the components in `src/app/auth/` to match your design system.

## 🐛 Troubleshooting

### Common Issues

1. **"Invalid client" error**: Check your OAuth credentials and callback URLs
2. **Database connection issues**: Verify your `DATABASE_URL`
3. **Session not persisting**: Ensure `AUTH_SECRET` is set
4. **TypeScript errors**: Make sure `src/types/next-auth.d.ts` is included

### Debug Mode

Set `debug: true` in `auth.ts` for detailed logging in development.

## 📚 Additional Resources

- [AuthJS Documentation](https://authjs.dev)
- [NextAuth v5 Migration Guide](https://authjs.dev/getting-started/migrating-to-v5)
- [Prisma Documentation](https://www.prisma.io/docs)

Your authentication system is now ready to use! 🎉