import { Metadata } from "next";
import Link from "next/link";

export const metadata: Metadata = {
  title: "Solutions | Motshwanelo IT Consulting",
  description: "Explore our comprehensive IT solutions including DIGIM Platform, NetEco Solutions, FusionModule, and UPS Solutions designed to transform your business.",
  keywords: "IT Solutions, DIGIM Platform, NetEco, FusionModule, UPS Solutions, Business Technology",
};

const solutions = [
  {
    id: "digim",
    title: "DIGIM Platform",
    description: "Advanced digital transformation platform for modern businesses",
    icon: "fa-solid fa-digital-tachograph",
    features: ["Digital Workflow Management", "Real-time Analytics", "Cloud Integration", "Mobile Access"],
    href: "/solutions/digim"
  },
  {
    id: "neteco",
    title: "NetEco Solutions",
    description: "Comprehensive network ecosystem management and monitoring",
    icon: "fa-solid fa-network-wired",
    features: ["Network Monitoring", "Performance Analytics", "Security Management", "Automated Reporting"],
    href: "/solutions/neteco"
  },
  {
    id: "fusion-module",
    title: "FusionModule",
    description: "Modular data center infrastructure solutions",
    icon: "fa-solid fa-cube",
    features: ["Modular Design", "Scalable Architecture", "Energy Efficient", "Rapid Deployment"],
    href: "/solutions/fusion-module"
  },
  {
    id: "ups",
    title: "UPS Solutions",
    description: "Uninterruptible power supply systems for critical infrastructure",
    icon: "fa-solid fa-battery-full",
    features: ["Power Protection", "Battery Backup", "Load Management", "Remote Monitoring"],
    href: "/solutions/ups"
  }
];

export default function SolutionsPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-900 via-blue-800 to-orange-600 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-5xl font-bold mb-6">
              Our Solutions
            </h1>
            <p className="text-xl text-blue-100 mb-8">
              Comprehensive IT solutions designed to drive digital transformation and business growth
            </p>
          </div>
        </div>
      </section>

      {/* Solutions Grid */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {solutions.map((solution) => (
              <div
                key={solution.id}
                className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group"
              >
                <div className="p-8">
                  <div className="flex items-center mb-6">
                    <div className="w-16 h-16 bg-gradient-to-br from-blue-600 to-orange-500 rounded-lg flex items-center justify-center mr-4">
                      <i className={`${solution.icon} text-2xl text-white`}></i>
                    </div>
                    <h3 className="text-2xl font-bold text-gray-800">{solution.title}</h3>
                  </div>
                  
                  <p className="text-gray-600 mb-6 leading-relaxed">
                    {solution.description}
                  </p>
                  
                  <div className="mb-6">
                    <h4 className="font-semibold text-gray-800 mb-3">Key Features:</h4>
                    <ul className="space-y-2">
                      {solution.features.map((feature, index) => (
                        <li key={index} className="flex items-center text-gray-600">
                          <i className="fa-solid fa-check text-green-500 mr-3"></i>
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                  <Link
                    href={solution.href}
                    className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-orange-500 text-white font-semibold rounded-lg hover:from-blue-700 hover:to-orange-600 transition-all duration-300 group-hover:transform group-hover:scale-105"
                  >
                    Learn More
                    <i className="fa-solid fa-arrow-right ml-2"></i>
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gradient-to-r from-blue-900 to-orange-600 text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">
            Ready to Transform Your Business?
          </h2>
          <p className="text-xl text-blue-100 mb-8">
            Contact us today to discuss how our solutions can help your organization
          </p>
          <Link
            href="/contact"
            className="inline-flex items-center px-8 py-4 bg-white text-blue-900 font-bold rounded-lg hover:bg-gray-100 transition-all duration-300"
          >
            Get Started
            <i className="fa-solid fa-arrow-right ml-2"></i>
          </Link>
        </div>
      </section>
    </div>
  );
}
